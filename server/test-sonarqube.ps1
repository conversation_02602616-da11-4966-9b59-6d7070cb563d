# SonarQube API Testing Script (PowerShell)
# Usage: .\test-sonarqube.ps1 [-BaseUrl "http://localhost:3002"] [-ProjectKey "your-project-key"]

param(
    [string]$BaseUrl = "http://localhost:3002",
    [string]$ProjectKey = "your-project-key"
)

Write-Host "🧪 Testing SonarQube API Integration" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor White
Write-Host "Project Key: $ProjectKey" -ForegroundColor White
Write-Host ""

function Test-Endpoint {
    param(
        [string]$Endpoint,
        [string]$Description
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Blue
    Write-Host "Endpoint: $Endpoint" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $Endpoint -Method GET -ContentType "application/json" -ErrorAction Stop
        Write-Host "✅ SUCCESS" -ForegroundColor Green
        
        # Pretty print JSON (first 20 lines)
        $jsonOutput = $response | ConvertTo-Json -Depth 5
        $lines = $jsonOutput -split "`n"
        if ($lines.Count -gt 20) {
            $lines[0..19] | ForEach-Object { Write-Host $_ -ForegroundColor Gray }
            Write-Host "... (truncated)" -ForegroundColor Yellow
        } else {
            Write-Host $jsonOutput -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "❌ FAILED" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
    Write-Host ""
}

# Test 1: Health check
Write-Host "1. Health Check" -ForegroundColor Yellow
Test-Endpoint -Endpoint "$BaseUrl/health" -Description "Server Health Check"

# Test 2: Get available metrics
Write-Host "2. Available Metrics" -ForegroundColor Yellow
Test-Endpoint -Endpoint "$BaseUrl/api/sonarqube/metrics" -Description "Get Available SonarQube Metrics"

# Test 3: Get project metrics (basic)
Write-Host "3. Project Metrics (Basic)" -ForegroundColor Yellow
Test-Endpoint -Endpoint "$BaseUrl/api/sonarqube/projects/$ProjectKey/metrics" -Description "Get Basic Project Metrics"

# Test 4: Get project metrics with specific metrics
Write-Host "4. Project Metrics (Specific)" -ForegroundColor Yellow
Test-Endpoint -Endpoint "$BaseUrl/api/sonarqube/projects/$ProjectKey/metrics?metrics=bugs,vulnerabilities,ncloc,complexity" -Description "Get Specific Project Metrics"

# Test 5: Get project metrics with period
Write-Host "5. Project Metrics (With Period)" -ForegroundColor Yellow
Test-Endpoint -Endpoint "$BaseUrl/api/sonarqube/projects/$ProjectKey/metrics?metrics=bugs,vulnerabilities&period=1" -Description "Get Project Metrics with Period"

# Test 6: Get code quality score
Write-Host "6. Code Quality Score" -ForegroundColor Yellow
Test-Endpoint -Endpoint "$BaseUrl/api/sonarqube/projects/$ProjectKey/quality-score" -Description "Get Code Quality Score"

Write-Host "🎉 Testing Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Tips:" -ForegroundColor Cyan
Write-Host "• Replace 'your-project-key' with your actual SonarQube project key" -ForegroundColor White
Write-Host "• Ensure Azure Key Vault secrets are configured:" -ForegroundColor White
Write-Host "  - sonarqube-base-url" -ForegroundColor White
Write-Host "  - sonarqube-token" -ForegroundColor White
Write-Host "• Check server logs if any tests fail" -ForegroundColor White
Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Cyan
Write-Host "  .\test-sonarqube.ps1" -ForegroundColor White
Write-Host "  .\test-sonarqube.ps1 -BaseUrl 'http://localhost:3002' -ProjectKey 'my-react-app'" -ForegroundColor White
Write-Host "  .\test-sonarqube.ps1 -BaseUrl 'https://api.staging.com' -ProjectKey 'com.company:project'" -ForegroundColor White 