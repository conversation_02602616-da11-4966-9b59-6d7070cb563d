#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:3002"
SONAR_BASE_URL="https://sonar.looper.prod.walmartlabs.com"

# Available teams (single-platform structure)
declare -A TEAMS=(
    ["ecom-ios"]="E-Commerce SAMS iOS"
    ["ecom-android"]="E-Commerce SAMS Android" 
    ["ecom-backend"]="E-Commerce SAMS Backend"
    ["mobile-ios"]="Mobile Development Team iOS"
    ["infrastructure"]="Infrastructure & DevOps Team"
)

declare -A PROJECT_IDS=(
    ["ecom-ios"]="ecom-sams-ios"
    ["ecom-android"]="ecom-sams-android"
    ["ecom-backend"]="ecom-sams-backend"
    ["mobile-ios"]="mobile-app-ios"
    ["infrastructure"]="infrastructure-backend"
)

# Helper function to print colored output
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️ $1${NC}"
}

# Function to make HTTP request and format JSON
make_request() {
    local url="$1"
    local description="$2"
    
    print_info "Testing: $description"
    print_info "URL: $url"
    
    response=$(curl -s -w "\n%{http_code}" "$url")
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        print_success "HTTP $http_code - Success"
        if command -v jq &> /dev/null; then
            echo "$body" | jq '.'
        else
            echo "$body"
        fi
    else
        print_error "HTTP $http_code - Failed"
        echo "$body"
    fi
    echo ""
}

# Test Team-Based API (single-platform teams)
test_team_api() {
    local team_key="$1"
    local team_name="${TEAMS[$team_key]}"
    local project_id="${PROJECT_IDS[$team_key]}"
    
    if [ -z "$team_name" ]; then
        print_error "Unknown team key: $team_key"
        print_info "Available teams: ${!TEAMS[@]}"
        return 1
    fi
    
    print_header "Testing Team-Based API for: $team_name"
    
    # URL encode team name
    encoded_team_name=$(echo "$team_name" | sed 's/ /%20/g')
    
    # Test available metrics
    make_request "$BASE_URL/api/sonarqube/teams/$encoded_team_name/available-metrics" \
        "Get available metrics for $team_name"
    
    # Test project metrics (default)
    make_request "$BASE_URL/api/sonarqube/teams/$encoded_team_name/metrics" \
        "Get default metrics for $team_name"
    
    # Test project metrics (specific)
    make_request "$BASE_URL/api/sonarqube/teams/$encoded_team_name/metrics?metrics=bugs,vulnerabilities,ncloc,coverage" \
        "Get specific metrics for $team_name"
    
    # Test quality score
    make_request "$BASE_URL/api/sonarqube/teams/$encoded_team_name/quality-score" \
        "Get quality score for $team_name"
    
    # Test with slugified name
    slugified_name=$(echo "$team_name" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
    make_request "$BASE_URL/api/sonarqube/teams/$slugified_name/metrics" \
        "Get metrics using slugified name: $slugified_name"
}

# Test Direct API
test_direct_api() {
    local project_key="$1"
    
    if [ -z "$project_key" ]; then
        print_error "Project key is required for direct API testing"
        return 1
    fi
    
    print_header "Testing Direct API for project: $project_key"
    
    # Test available metrics
    make_request "$BASE_URL/api/sonarqube/direct/metrics?baseUrl=$SONAR_BASE_URL" \
        "Get available metrics (direct)"
    
    # Test project metrics (default)
    make_request "$BASE_URL/api/sonarqube/direct/projects/$project_key/metrics?baseUrl=$SONAR_BASE_URL" \
        "Get default metrics for $project_key (direct)"
    
    # Test project metrics (specific)
    make_request "$BASE_URL/api/sonarqube/direct/projects/$project_key/metrics?baseUrl=$SONAR_BASE_URL&metrics=bugs,vulnerabilities,ncloc" \
        "Get specific metrics for $project_key (direct)"
    
    # Test quality score
    make_request "$BASE_URL/api/sonarqube/direct/projects/$project_key/quality-score?baseUrl=$SONAR_BASE_URL" \
        "Get quality score for $project_key (direct)"
}

# Test Legacy API
test_legacy_api() {
    local project_key="$1"
    
    if [ -z "$project_key" ]; then
        print_error "Project key is required for legacy API testing"
        return 1
    fi
    
    print_header "Testing Legacy API for project: $project_key"
    print_warning "Legacy API is deprecated and will be removed in future versions"
    
    # Test available metrics
    make_request "$BASE_URL/api/sonarqube/metrics" \
        "Get available metrics (legacy)"
    
    # Test project metrics (default)
    make_request "$BASE_URL/api/sonarqube/projects/$project_key/metrics" \
        "Get default metrics for $project_key (legacy)"
    
    # Test project metrics (specific)
    make_request "$BASE_URL/api/sonarqube/projects/$project_key/metrics?metrics=bugs,vulnerabilities,ncloc" \
        "Get specific metrics for $project_key (legacy)"
    
    # Test quality score
    make_request "$BASE_URL/api/sonarqube/projects/$project_key/quality-score" \
        "Get quality score for $project_key (legacy)"
}

# Test all approaches for a team
test_all_approaches() {
    local team_key="$1"
    local project_id="${PROJECT_IDS[$team_key]}"
    
    if [ -z "$project_id" ]; then
        print_error "Unknown team key: $team_key"
        print_info "Available teams: ${!TEAMS[@]}"
        return 1
    fi
    
    test_team_api "$team_key"
    test_direct_api "$project_id"
    test_legacy_api "$project_id"
}

# Show usage
show_usage() {
    echo -e "${PURPLE}SonarQube API Test Script${NC}"
    echo ""
    echo "Usage:"
    echo "  $0 team <team_key>           # Test team-based API"
    echo "  $0 direct <project_key>      # Test direct API"
    echo "  $0 legacy <project_key>      # Test legacy API"
    echo "  $0 all <team_key>           # Test all approaches for a team"
    echo ""
    echo "Available teams:"
    for key in "${!TEAMS[@]}"; do
        echo "  $key -> ${TEAMS[$key]} (${PROJECT_IDS[$key]})"
    done
    echo ""
    echo "Examples:"
    echo "  $0 team ecom-ios                    # Test E-Commerce SAMS iOS team"
    echo "  $0 direct glass-ios                 # Test direct API with glass-ios project"
    echo "  $0 legacy glass-ios                 # Test legacy API with glass-ios project"
    echo "  $0 all ecom-android                 # Test all approaches for Android team"
    echo ""
    echo "Note: Requires jq for JSON formatting (optional)"
}

# Main script logic
case "$1" in
    "team")
        if [ -z "$2" ]; then
            print_error "Team key is required"
            show_usage
            exit 1
        fi
        test_team_api "$2"
        ;;
    "direct")
        if [ -z "$2" ]; then
            print_error "Project key is required"
            show_usage
            exit 1
        fi
        test_direct_api "$2"
        ;;
    "legacy")
        if [ -z "$2" ]; then
            print_error "Project key is required"
            show_usage
            exit 1
        fi
        test_legacy_api "$2"
        ;;
    "all")
        if [ -z "$2" ]; then
            print_error "Team key is required"
            show_usage
            exit 1
        fi
        test_all_approaches "$2"
        ;;
    *)
        show_usage
        exit 1
        ;;
esac 