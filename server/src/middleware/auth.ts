import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { ResponseUtil } from '../utils/response';
import { User, UserRole } from '../types';

export const authenticate = (req: Request, res: Response, next: NextFunction): void => {
  // First check for session-based authentication (GitHub OAuth)
  if (req.isAuthenticated && req.isAuthenticated() && req.user) {
    next();
    return;
  }

  // Fall back to JWT authentication
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    ResponseUtil.unauthorized(res, 'Access token required');
    return;
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    req.user = decoded.user;
    next();
  } catch (error) {
    ResponseUtil.unauthorized(res, 'Invalid or expired token');
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = req.user as User;
    
    if (!user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    if (!roles.includes(user.role)) {
      ResponseUtil.forbidden(res, 'Insufficient permissions');
      return;
    }

    next();
  };
};

export const optionalAuth = (req: Request, res: Response, next: NextFunction): void => {
  // First check for session-based authentication
  if (req.isAuthenticated && req.isAuthenticated() && req.user) {
    next();
    return;
  }

  // Fall back to JWT authentication
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    next();
    return;
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    req.user = decoded.user;
  } catch (error) {
    // Ignore invalid tokens for optional auth
  }

  next();
};
