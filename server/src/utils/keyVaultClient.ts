import { ClientSecretCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';
import { azureConfig, validateAzureConfig } from '../config/azure';

class KeyVaultClient {
  private client: SecretClient;

  constructor() {
    try {
      // Validate configuration
      validateAzureConfig();

      const credential = new ClientSecretCredential(
        azureConfig.azureAD.tenantId,
        azureConfig.azureAD.clientId,
        azureConfig.azureAD.clientSecret
      );
      
      this.client = new SecretClient(azureConfig.keyVault.url, credential);
      console.log('✅ Azure Key Vault client initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Azure Key Vault client:', error);
      throw error;
    }
  }

  async getSecret(secretName: string): Promise<string> {
    try {
      console.log(`🔑 Fetching secret: ${secretName}`);
      const secret = await this.client.getSecret(secretName);
      console.log('✅ Secret retrieved successfully');
      return secret.value || '';
    } catch (error) {
      console.error(`❌ Error fetching secret ${secretName}:`, error);
      throw error;
    }
  }

  async setSecret(secretName: string, value: string): Promise<void> {
    try {
      console.log(`🔑 Setting secret: ${secretName}`);
      await this.client.setSecret(secretName, value);
      console.log('✅ Secret stored successfully');
    } catch (error) {
      console.error(`❌ Error setting secret ${secretName}:`, error);
      throw error;
    }
  }

  async deleteSecret(secretName: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting secret: ${secretName}`);
      await this.client.beginDeleteSecret(secretName);
      console.log('✅ Secret deleted successfully');
    } catch (error) {
      console.error(`❌ Error deleting secret ${secretName}:`, error);
      throw error;
    }
  }

  async listSecrets(): Promise<string[]> {
    try {
      console.log('📋 Listing secrets...');
      const secrets: string[] = [];
      for await (const secretProperties of this.client.listPropertiesOfSecrets()) {
        secrets.push(secretProperties.name);
      }
      console.log(`✅ Found ${secrets.length} secrets`);
      return secrets;
    } catch (error) {
      console.error('❌ Error listing secrets:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default new KeyVaultClient(); 