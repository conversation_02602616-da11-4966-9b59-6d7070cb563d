import express from 'express';
import { cosmosClient, cosmosConfig } from '../config/cosmosdb';
import { Team, ClientRelease, Approval } from '../types/database';

const router = express.Router();

// Helper to get releases container
async function getReleasesContainer() {
  const database = cosmosClient.database(cosmosConfig.databaseId);
  return database.container('releases');
}

// Helper to get teams container
async function getTeamsContainer() {
  const database = cosmosClient.database(cosmosConfig.databaseId);
  return database.container(cosmosConfig.containers.teams);
}

// Helper to get team by ID
async function getTeamById(teamId: string): Promise<Team | null> {
  try {
    const container = await getTeamsContainer();
    const { resource } = await container.item(teamId, teamId).read<Team>();
    
    // Debug logging to see what team data is loaded
    console.log(`Loaded team data for ${teamId}:`, JSON.stringify(resource?.roles?.functional_qa, null, 2));
    
    return resource || null;
  } catch (error) {
    console.error(`Error fetching team ${teamId}:`, error);
    return null;
  }
}

// Extended Release interface with approvals (Release already includes approvals from database types)
// Note: We can now use Release directly since it includes approvals, progress, and completedAt fields

/**
 * Get signoff status for a specific release
 * This provides dynamic signoff tracking based on team configuration
 */
router.get('/:releaseId', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const container = await getReleasesContainer();
    
    console.log(`Getting signoff status for release: ${releaseId}`);
    
    // Get release
    const { resource: release } = await container.item(releaseId, releaseId).read<ClientRelease>();
    if (!release) {
      return res.status(404).json({ error: 'Release not found' });
    }
    
    // Get release type from ClientRelease structure
    const releaseType = release.releaseType;
    if (!releaseType) {
      return res.status(400).json({ 
        error: 'Release type not found',
        releaseId: release.id
      });
    }
    
    // Get team configuration
    const team = await getTeamById(release.teamId);
    if (!team?.release_types?.[releaseType]) {
      return res.status(400).json({ 
        error: 'Team configuration not found or release type not configured',
        teamId: release.teamId,
        releaseType: releaseType
      });
    }
    
    const releaseTypeConfig = team.release_types[releaseType];
    const stages = releaseTypeConfig.signoff_order;
    const allApprovals = (release as any).approvals || [];
    
    console.log(`Processing ${stages.length} stages for ${releaseType} release`);
    
    // Build dynamic signoff status
    const signoffStatus = await Promise.all(stages.map(async (stageName: string, stageIndex: number) => {
      const stageApprovals = allApprovals.filter((a: Approval) => a.stage === stageName);
      const stageConfig = releaseTypeConfig.enforcement[stageName];
      
      // Determine if previous stages are completed (for stage availability)
      const previousStagesCompleted = await Promise.all(
        stages.slice(0, stageIndex).map(async (prevStage: string) => {
          const prevApprovals = allApprovals.filter((a: Approval) => a.stage === prevStage);
          return await isStageCompleted(prevStage, prevApprovals, team, releaseType);
        })
      );
      
      const isAvailable = stageIndex === 0 || previousStagesCompleted.every(Boolean);
      const isCompleted = await isStageCompleted(stageName, stageApprovals, team, releaseType);
      
      let status: 'pending' | 'in_progress' | 'completed';
      if (isCompleted) {
        status = 'completed';
      } else if (isAvailable) {
        status = 'in_progress';
      } else {
        status = 'pending';
      }
      
      // Get required approvers for this stage
      const requiredApprovers = await getRequiredApprovers(stageName, team, releaseType);
      
      // Calculate progress for this stage
      const progress = await calculateStageProgress(stageName, stageApprovals, team, releaseType);
      
      return {
        id: stageName,
        name: formatStageName(stageName),
        status,
        isAvailable,
        progress,
        requiredApprovers,
        currentApprovals: stageApprovals,
        minRequiredApprovals: getMinRequiredApprovals(stageConfig, stageName, team, releaseType),
        lastUpdate: getLatestApprovalDate(stageApprovals),
        issues: getStageIssues(stageApprovals)
      };
    }));
    
    // Calculate overall progress
    const overallProgress = calculateOverallProgress(signoffStatus);
    
    res.json({
      releaseId,
      releaseType: releaseType,
      teamId: release.teamId,
      overallProgress,
      stages: signoffStatus,
      totalStages: stages.length,
      completedStages: signoffStatus.filter((s: any) => s.status === 'completed').length
    });
    
  } catch (error) {
    console.error(`Error getting signoff status for ${req.params.releaseId}:`, error);
    res.status(500).json({ error: 'Failed to get signoff status' });
  }
});

/**
 * Submit an approval for a specific stage
 */
router.post('/:releaseId/approve/:stage', async (req, res) => {
  try {
    const { releaseId, stage } = req.params;
    const { approverEmail, sha, buildNumber, notes, subTeam } = req.body;
    
    if (!approverEmail) {
      return res.status(400).json({ error: 'Approver email is required' });
    }
    
    console.log(`Processing approval for release ${releaseId}, stage ${stage}, approver ${approverEmail}`);
    
    const container = await getReleasesContainer();
    
    // Get release
    const { resource: release } = await container.item(releaseId, releaseId).read<ClientRelease>();
    if (!release) {
      return res.status(404).json({ error: 'Release not found' });
    }
    
    // Get release type from ClientRelease structure
    const releaseType = release.releaseType;
    if (!releaseType) {
      return res.status(400).json({ 
        error: 'Release type not found',
        releaseId: release.id
      });
    }
    
    // Get team configuration
    const team = await getTeamById(release.teamId);
    if (!team?.release_types?.[releaseType]) {
      return res.status(400).json({ error: 'Team configuration not found' });
    }
    
    const releaseTypeConfig = team.release_types[releaseType];
    const stages = releaseTypeConfig.signoff_order;
    
    // Validate stage exists
    if (!stages.includes(stage)) {
      return res.status(400).json({ 
        error: 'Invalid stage', 
        validStages: stages 
      });
    }
    
    // Validate approver is authorized for this stage
    const isAuthorized = await isApproverAuthorized(stage, approverEmail, subTeam, team, releaseType);
    if (!isAuthorized) {
      return res.status(403).json({ 
        error: 'Approver not authorized for this stage',
        stage,
        approverEmail,
        subTeam
      });
    }
    
    // Check if previous stages are completed
    const stageIndex = stages.indexOf(stage);
    const previousStagesCompleted = await Promise.all(
      stages.slice(0, stageIndex).map(async (prevStage: string) => {
        const prevApprovals = (release as any).approvals?.filter((a: Approval) => a.stage === prevStage) || [];
        return await isStageCompleted(prevStage, prevApprovals, team, releaseType);
      })
    );
    
    if (!previousStagesCompleted.every(Boolean)) {
      return res.status(400).json({ 
        error: 'Previous stages must be completed first',
        currentStage: stage,
        stageIndex
      });
    }
    
    // Create approval
    const newApproval: Approval = {
      id: `approval-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      releaseId,
      teamId: release.teamId,
      stage,
      approverEmail,
      status: 'approved',
      sha,
      buildNumber,
      subTeam,
      approvedAt: new Date().toISOString(),
      notes
    };
    
    // Update or add approval
    const existingApprovals = (release as any).approvals || [];
    const existingIndex = existingApprovals.findIndex((a: Approval) => 
      a.stage === stage && 
      a.approverEmail === approverEmail &&
      (stage === 'functional_qa' ? a.subTeam === subTeam : true)
    );
    
    let updatedApprovals;
    if (existingIndex >= 0) {
      updatedApprovals = [...existingApprovals];
      updatedApprovals[existingIndex] = newApproval;
      console.log(`Updated existing approval for ${approverEmail} on stage ${stage}`);
    } else {
      updatedApprovals = [...existingApprovals, newApproval];
      console.log(`Added new approval for ${approverEmail} on stage ${stage}`);
    }
    
    // Calculate new progress using actual stage progress percentages
    const allStageProgresses = await Promise.all(stages.map(async (stageName: string) => {
      const stageApprovals = updatedApprovals.filter((a: Approval) => a.stage === stageName);
      return await calculateStageProgress(stageName, stageApprovals, team, releaseType);
    }));
    
    // Calculate overall progress as average of stage progresses
    const newProgress = Math.round(allStageProgresses.reduce((sum, progress) => sum + progress, 0) / stages.length);
    
    // Check if all stages are completed (for completion status)
    const allStageStatuses = await Promise.all(stages.map(async (stageName: string) => {
      const stageApprovals = updatedApprovals.filter((a: Approval) => a.stage === stageName);
      return await isStageCompleted(stageName, stageApprovals, team, releaseType);
    }));
    const allCompleted = allStageStatuses.every(Boolean);
    
    // Update release
    const updatedRelease: any = {
      ...release,
      approvals: updatedApprovals,
      progress: newProgress,
      status: allCompleted ? 'completed' : (release as any).status,
      ...(allCompleted && !(release as any).completedAt ? { completedAt: new Date().toISOString() } : {})
    };
    
    await container.item(releaseId, releaseId).replace(updatedRelease);
    
    console.log(`Successfully processed approval. New progress: ${newProgress}%`);
    console.log('Stored approval:', JSON.stringify(newApproval, null, 2));
    console.log('Total approvals now:', updatedApprovals.length);
    
    res.status(201).json({
      approval: newApproval,
      newProgress,
      stageCompleted: await isStageCompleted(stage, updatedApprovals.filter((a: Approval) => a.stage === stage), team, releaseType),
      releaseCompleted: allCompleted
    });
    
  } catch (error) {
    console.error(`Error processing approval:`, error);
    res.status(500).json({ error: 'Failed to process approval' });
  }
});

/**
 * Get available approvers for a specific stage
 */
router.get('/:releaseId/approvers/:stage', async (req, res) => {
  try {
    const { releaseId, stage } = req.params;
    const container = await getReleasesContainer();
    
    // Get release
    const { resource: release } = await container.item(releaseId, releaseId).read<ClientRelease>();
    if (!release) {
      return res.status(404).json({ error: 'Release not found' });
    }
    
    // Get team configuration
    const team = await getTeamById(release.teamId);
    if (!team?.release_types?.[release.releaseType]) {
      return res.status(400).json({ error: 'Team configuration not found' });
    }
    
    const approvers = await getRequiredApprovers(stage, team, release.releaseType);
    
    res.json({
      stage,
      approvers
    });
    
  } catch (error) {
    console.error(`Error getting approvers for stage ${req.params.stage}:`, error);
    res.status(500).json({ error: 'Failed to get approvers' });
  }
});

// Helper functions

/**
 * Check if a stage is completed based on team configuration
 */
async function isStageCompleted(stageName: string, stageApprovals: Approval[], team: Team, releaseType: string): Promise<boolean> {
  const approvedApprovals = stageApprovals.filter((a: Approval) => a.status === 'approved');
  const releaseTypeConfig = team.release_types[releaseType];
  const stageConfig = releaseTypeConfig.enforcement[stageName];
  
  if (stageName === 'functional_qa') {
    // For functional QA, check sub-team requirements
    const teamRoles = team.roles?.functional_qa;
    if (teamRoles && typeof teamRoles === 'object' && !Array.isArray(teamRoles)) {
      const requiredSubTeams = Object.keys(teamRoles as { [subTeamName: string]: string[] });
      
      if (stageConfig?.require_all_subteams) {
        // All sub-teams must approve
        const approvedSubTeams = new Set(
          approvedApprovals.map((a: Approval) => a.subTeam).filter(Boolean)
        );
        return requiredSubTeams.every((subTeam: string) => approvedSubTeams.has(subTeam));
      } else {
        // Check minimum total signoffs
        const minTotal = stageConfig?.min_total_signoffs || 1;
        return approvedApprovals.length >= minTotal;
      }
    }
    return false;
  } else {
    // For other stages, check minimum signoffs
    const minSignoffs = stageConfig?.min_signoffs || 1;
    return approvedApprovals.length >= minSignoffs;
  }
}

/**
 * Get required approvers for a stage
 */
async function getRequiredApprovers(stageName: string, team: Team, releaseType: string): Promise<any[]> {
  if (stageName === 'functional_qa') {
    // Return sub-teams and their approvers
    const teamRoles = team.roles?.functional_qa;
    if (teamRoles && typeof teamRoles === 'object' && !Array.isArray(teamRoles)) {
      return Object.entries(teamRoles as { [subTeamName: string]: string[] }).map(([subTeam, approvers]) => ({
        subTeam,
        approvers: Array.isArray(approvers) ? approvers : []
      }));
    }
    return [];
  } else {
    // Return stage approvers
    const stageApprovers = team.roles?.[stageName];
    if (Array.isArray(stageApprovers)) {
      return stageApprovers.map((email: string) => ({ email }));
    }
    return [];
  }
}

/**
 * Check if an approver is authorized for a stage
 */
async function isApproverAuthorized(stageName: string, approverEmail: string, subTeam: string | undefined, team: Team, releaseType: string): Promise<boolean> {
  console.log('Authorization check:', { stageName, approverEmail, subTeam, teamRoles: team.roles?.functional_qa });
  
  if (stageName === 'functional_qa') {
    if (!subTeam) {
      console.log('Authorization failed: no subTeam provided');
      return false;
    }
    const teamRoles = team.roles?.functional_qa;
    if (teamRoles && typeof teamRoles === 'object' && !Array.isArray(teamRoles)) {
      const subTeamApprovers = (teamRoles as { [subTeamName: string]: string[] })[subTeam] || [];
      console.log(`Authorization check for subTeam "${subTeam}":`, subTeamApprovers);
      const isAuthorized = Array.isArray(subTeamApprovers) && subTeamApprovers.includes(approverEmail);
      console.log('Authorization result:', isAuthorized);
      return isAuthorized;
    }
    console.log('Authorization failed: teamRoles not found or invalid');
    return false;
  } else {
    const stageApprovers = team.roles?.[stageName];
    return Array.isArray(stageApprovers) && stageApprovers.includes(approverEmail);
  }
}

/**
 * Calculate progress for a specific stage
 */
async function calculateStageProgress(stageName: string, stageApprovals: Approval[], team: Team, releaseType: string): Promise<number> {
  const approvedApprovals = stageApprovals.filter((a: Approval) => a.status === 'approved');
  
  if (stageName === 'functional_qa') {
    const teamRoles = team.roles?.functional_qa;
    if (teamRoles && typeof teamRoles === 'object' && !Array.isArray(teamRoles)) {
      const requiredSubTeams = Object.keys(teamRoles as { [subTeamName: string]: string[] });
      
      if (requiredSubTeams.length === 0) return 0;
      
      const approvedSubTeams = new Set(
        approvedApprovals.map((a: Approval) => a.subTeam).filter(Boolean)
      );
      
      return Math.round((approvedSubTeams.size / requiredSubTeams.length) * 100);
    }
    return 0;
  } else {
    const releaseTypeConfig = team.release_types[releaseType];
    const stageConfig = releaseTypeConfig.enforcement[stageName];
    const minRequired = stageConfig?.min_signoffs || 1;
    
    return Math.min(Math.round((approvedApprovals.length / minRequired) * 100), 100);
  }
}

/**
 * Get minimum required approvals for a stage
 */
function getMinRequiredApprovals(stageConfig: any, stageName: string, team: Team, releaseType: string): number {
  if (stageName === 'functional_qa') {
    if (stageConfig?.require_all_subteams) {
      const teamRoles = team.roles?.functional_qa;
      if (teamRoles && typeof teamRoles === 'object' && !Array.isArray(teamRoles)) {
        return Object.keys(teamRoles as { [subTeamName: string]: string[] }).length;
      }
      return 0;
    }
    return stageConfig?.min_total_signoffs || 1;
  }
  return stageConfig?.min_signoffs || 1;
}

/**
 * Format stage name for display
 */
function formatStageName(stageName: string): string {
  const stageNameMapping: { [key: string]: string } = {
    'functional_qa': 'Functional QA',
    'e2e_qa': 'E2E QA',
    'engineering_leadership': 'Engineering Leadership',
    'performance_qa': 'Performance QA',
    'security_qa': 'Security QA',
    'devops': 'DevOps',
    'sre': 'Site Reliability Engineering',
    'security': 'Security Review',
    'platform_engineering': 'Platform Engineering'
  };
  
  return stageNameMapping[stageName] || stageName.split('_').map((word: string) => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}

/**
 * Calculate overall progress
 */
function calculateOverallProgress(stages: any[]): number {
  if (stages.length === 0) return 0;
  const totalProgress = stages.reduce((sum: number, stage: any) => sum + stage.progress, 0);
  return Math.round(totalProgress / stages.length);
}

/**
 * Get latest approval date for a stage
 */
function getLatestApprovalDate(approvals: Approval[]): string | undefined {
  const approvedApprovals = approvals.filter((a: Approval) => a.status === 'approved' && a.approvedAt);
  if (approvedApprovals.length === 0) return undefined;
  
  return approvedApprovals
    .sort((a: Approval, b: Approval) => new Date(b.approvedAt!).getTime() - new Date(a.approvedAt!).getTime())[0]
    .approvedAt;
}

/**
 * Get issues for a stage
 */
function getStageIssues(approvals: Approval[]): string | undefined {
  const rejectedApprovals = approvals.filter((a: Approval) => a.status === 'rejected');
  if (rejectedApprovals.length === 0) return undefined;
  
  return rejectedApprovals.map((a: Approval) => a.notes).filter(Boolean).join('; ');
}

export default router; 