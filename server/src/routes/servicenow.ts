import { Router } from 'express';
import serviceNowService from '../services/serviceNowService';
import cosmosService from '../services/cosmosService';

const router = Router();

// Test route to verify router is working
router.get("/test", (req, res) => {
  res.json({ message: "ServiceNow router is working" });
});

// Calculate score for change requests
function calculateChangeRequestsScore(changeRequests: any[]): number {
  if (!Array.isArray(changeRequests) || changeRequests.length === 0) {
    return 100; // Perfect score for no change requests
  }

  const totalRequests = changeRequests.length;
  const openRequests = changeRequests.filter((cr: any) => 
    ['Open', 'New', 'Assigned', 'Work in Progress', 'Pending'].includes(cr.state_dv)
  ).length;
  const highPriorityRequests = changeRequests.filter((cr: any) => 
    ['0', '1', '2'].includes(cr.priority) // 0=Critical, 1=High, 2=Medium
  ).length;
  
  let score = 100; // Start with perfect score
  score -= Math.min(totalRequests * 2, 30); // Up to 30 points penalty for total requests
  score -= Math.min(openRequests * 5, 40); // Up to 40 points penalty for open requests
  score -= Math.min(highPriorityRequests * 8, 30); // Up to 30 points penalty for high priority
  
  return Math.max(0, Math.min(100, Math.round(score)));
}

// Get change requests for a release
router.get("/change-requests/:releaseId", async (req, res, next) => {
  try {
    const { releaseId } = req.params;

    console.log("Fetching change requests for release:", releaseId);

    // Get the release details to find the team
    const release = await cosmosService.getRelease(releaseId);

    if (!release) {
      console.log("Release not found:", releaseId);
      return res.status(404).json({ error: "Release not found" });
    }

    console.log("Found release:", release);

    // Get the team details to get the serviceNowId
    const team = await cosmosService.getTeam(release.teamId);

    if (!team || !team.metadata?.serviceNowId) {
      console.log(
        "Team or ServiceNow ID not found for team:",
        release.teamName
      );
      return res.status(400).json({ error: "Team ServiceNow ID not found" });
    }

    console.log("Found team with ServiceNow ID:", team.metadata.serviceNowId);

    const changeRequestsData = await serviceNowService.getChangeRequests(
      team.metadata.serviceNowId
    );
    console.log("Retrieved change requests:", {
      total: changeRequestsData.total,
      count: changeRequestsData.changeRequests.length,
    });

    // Calculate score
    const score = calculateChangeRequestsScore(changeRequestsData.changeRequests);

    // Store change requests within releaseMetrics using the storeReleaseMetrics method
    const changeRequestsMetrics = {
      lastUpdated: new Date().toISOString(),
      total: changeRequestsData.total,
      score,
      items: changeRequestsData.changeRequests,
    };

    await cosmosService.storeReleaseMetrics(
      releaseId,
      release.teamId,
      'changeRequests',
      changeRequestsMetrics
    );

    console.log("Stored change requests data in releaseMetrics");

    res.json(changeRequestsMetrics);
  } catch (error: any) {
    console.error("Error fetching change requests:", error);

    // Check if this is a network/connectivity error that warrants fallback to stored data
    const networkErrors = ['ENOTFOUND', 'ECONNREFUSED', 'ECONNRESET', 'ETIMEDOUT', 'EHOSTUNREACH', 'ENETUNREACH'];
    const isNetworkError = networkErrors.includes(error.code) ||
                          error.message?.includes('getaddrinfo ENOTFOUND') ||
                          error.message?.includes('Failed to fetch');

    if (isNetworkError) {
      console.log(`🔄 Network error detected (${error.code}), attempting to retrieve stored change requests data`);

      try {
        // Try to get stored change requests from releaseMetrics
        const storedMetrics = await cosmosService.getReleaseMetrics(req.params.releaseId);

        if (storedMetrics?.releaseMetrics?.changeRequests) {
          console.log(`✅ Successfully retrieved stored change requests data as fallback`);

          const storedData = storedMetrics.releaseMetrics.changeRequests;

          // Transform stored data to match the expected live API format
          const fallbackData = {
            lastUpdated: storedData.lastUpdated,
            total: storedData.totalRequests || storedData.total || (storedData.rawData ? storedData.rawData.length : 0),
            score: storedData.score,
            items: storedData.rawData || storedData.items || [],
            isFromStored: true,
            fallbackReason: `Network error: ${error.code || error.message}`
          };

          console.log(`📊 Fallback data structure:`, {
            total: fallbackData.total,
            itemsCount: fallbackData.items.length,
            hasItems: Array.isArray(fallbackData.items),
            score: fallbackData.score
          });

          return res.json(fallbackData);
        } else {
          console.log(`❌ No stored change requests data found for release: ${req.params.releaseId}`);
        }
      } catch (fallbackError: any) {
        console.error(`❌ Failed to retrieve stored change requests data:`, fallbackError.message);
      }
    }

    // If no fallback available or not a network error, return the original error
    let errorMessage = "Failed to fetch change requests";
    let statusCode = 500;

    if (error.code === 'UNABLE_TO_GET_ISSUER_CERT_LOCALLY') {
      errorMessage = "SSL certificate verification failed for ServiceNow endpoint";
      statusCode = 503; // Service unavailable
    } else if (error.code === 'ENOTFOUND') {
      errorMessage = "ServiceNow endpoint not reachable - no stored data available";
      statusCode = 503;
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = "Connection refused by ServiceNow server";
      statusCode = 503;
    } else if (error.response?.status === 401) {
      errorMessage = "Authentication failed with ServiceNow";
      statusCode = 401;
    } else if (error.response?.status === 403) {
      errorMessage = "Access forbidden to ServiceNow resources";
      statusCode = 403;
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      errorMessage = `ServiceNow API error: ${error.response.statusText}`;
      statusCode = error.response.status;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(statusCode).json({
      error: errorMessage,
      code: error.code,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

export default router; 