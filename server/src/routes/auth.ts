import { Router, Request, Response } from 'express';
import passport from 'passport';
import { authManager } from '../auth/authManager';
import { logger } from '../utils/logger';
import { ResponseUtil } from '../utils/response';
import { config } from '../config';
import { asyncHand<PERSON> } from '../middleware/errorHandler';

const router = Router();

// Get available auth providers
router.get('/providers', (req: Request, res: Response) => {
  try {
    const providers = authManager.getEnabledProviders();
    ResponseUtil.success(res, { providers }, 'Available authentication providers');
  } catch (error) {
    logger.error('Error getting auth providers:', error);
    ResponseUtil.internalError(res, 'Failed to get authentication providers');
  }
});

// Check authentication status
router.get('/status', (req: Request, res: Response) => {
  try {
    const isAuthenticated = req.isAuthenticated && req.isAuthenticated();

    logger.info('🔍 Auth status check', {
      isAuthenticated,
      hasUser: !!req.user,
      sessionID: req.sessionID,
      userAgent: req.get('User-Agent')?.substring(0, 50),
    });

    ResponseUtil.success(
      res,
      {
        authenticated: isAuthenticated,
        user: isAuthenticated ? req.user : null,
      },
      'Authentication status'
    );
  } catch (error) {
    logger.error('Error checking auth status:', error);
    ResponseUtil.internalError(res, 'Failed to check authentication status');
  }
});

// Logout
router.post('/logout', (req: Request, res: Response) => {
  try {
    req.logout((err) => {
      if (err) {
        logger.error('Logout error:', err);
        return ResponseUtil.internalError(res, 'Failed to logout');
      }

      // Clear session
      req.session.destroy((sessionErr) => {
        if (sessionErr) {
          logger.error('Session destruction error:', sessionErr);
        }
      });

      ResponseUtil.success(res, null, 'Logged out successfully');
    });
  } catch (error) {
    logger.error('Logout error:', error);
    ResponseUtil.internalError(res, 'Failed to logout');
  }
});

// GitHub OAuth routes (only enabled if GitHub is configured)
if (authManager.isGitHubEnabled()) {
  // GitHub OAuth login
  router.get('/github', passport.authenticate('github', { scope: ['user:email'] }));

  // GitHub OAuth callback
  router.get(
    '/github/callback',
    passport.authenticate('github', {
      failureRedirect: `${config.urls.frontend}?auth=error`,
    }),
    (req, res) => {
      try {
        // Authentication successful
        res.redirect(`${config.urls.frontend}?auth=success`);
      } catch (error) {
        console.error('GitHub auth callback error:', error);
        res.redirect(`${config.urls.frontend}?auth=error`);
      }
    }
  );
}

// Azure AD OAuth routes (only enabled if Azure is configured)
if (authManager.isAzureEnabled()) {
  // Azure AD OAuth login
  router.get('/azure', async (req: Request, res: Response) => {
    try {
      const state = (req.query.state as string) || Math.random().toString(36).substring(7);
      const authUrl = await authManager.getAzureProvider().getAuthCodeUrl(state);
      res.redirect(authUrl);
    } catch (error) {
      logger.error('Azure AD OAuth initiation error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });

  // Azure AD OAuth callback
  router.get('/azure/callback', async (req: Request, res: Response) => {
    try {
      const code = req.query.code as string;
      const state = req.query.state as string;

      if (!code) {
        logger.error('No code received in Azure AD callback');
        return res.redirect(`${config.urls.frontend}?auth=error`);
      }

      // Handle the callback using MSAL
      const user = await authManager.getAzureProvider().handleCallback(code, state);

      // Set user in session
      req.login(user, (err) => {
        if (err) {
          logger.error('Error setting user in session:', err);
          return res.redirect(`${config.urls.frontend}?auth=error`);
        }
        res.redirect(`${config.urls.frontend}?auth=success`);
      });
    } catch (error) {
      logger.error('Azure auth callback error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });
}

// SAML routes (only enabled if SAML is configured)
if (authManager.isSamlEnabled()) {
  // SAML login
  router.get(
    '/saml',
    passport.authenticate('saml', { failureRedirect: `${config.urls.frontend}?auth=error` })
  );

  // SAML callback
  router.post(
    '/saml/callback',
    passport.authenticate('saml', { failureRedirect: `${config.urls.frontend}?auth=error` }),
    (req, res) => {
      try {
        // Authentication successful
        res.redirect(`${config.urls.frontend}?auth=success`);
      } catch (error) {
        logger.error('SAML auth callback error:', error);
        res.redirect(`${config.urls.frontend}?auth=error`);
      }
    }
  );

  // SAML logout
  router.get('/saml/logout', (req, res) => {
    try {
      req.logout((err) => {
        if (err) {
          logger.error('SAML logout error:', err);
          return res.redirect(`${config.urls.frontend}?auth=error`);
        }

        // Redirect to SAML IdP logout
        const samlProvider = authManager.getSamlProvider();
        const logoutUrl = samlProvider.getLogoutUrl();
        res.redirect(logoutUrl);
      });
    } catch (error) {
      logger.error('SAML logout error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });

  // SAML metadata endpoint
  router.get('/saml/metadata', (req, res) => {
    try {
      const samlProvider = authManager.getSamlProvider();
      const metadata = samlProvider.getMetadata();
      res.set('Content-Type', 'text/xml');
      res.send(metadata);
    } catch (error) {
      logger.error('Error generating SAML metadata:', error);
      ResponseUtil.internalError(res, 'Failed to generate SAML metadata');
    }
  });
}

// Debug route to test server stability
router.get('/debug/test', (req: Request, res: Response) => {
  logger.info('Debug test route called');
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Server is running and stable',
  });
});

// PingFed OAuth routes (only enabled if PingFed is configured)
if (authManager.isPingFedEnabled()) {
  // PingFed OAuth login
  router.get('/pingfed', async (req: Request, res: Response) => {
    try {
      const state = (req.query.state as string) || Math.random().toString(36).substring(7);
      const authUrl = authManager.getPingFedProvider().getAuthCodeUrl(state);
      res.redirect(authUrl);
    } catch (error) {
      logger.error('PingFed OAuth initiation error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });

  // PingFed OAuth callback with MAXIMUM crash protection
  router.get(
    '/pingfed/callback',
    asyncHandler(async (req: Request, res: Response) => {
      logger.info('🔥 PINGFED CALLBACK STARTED - MAXIMUM PROTECTION MODE');
      logger.info('Request URL:', req.url);
      logger.info('Request query:', req.query);
      logger.info('Request headers:', req.headers);

      // Set global flag to prevent process exit
      (global as any).authenticationInProgress = true;
      logger.info('🛡️ Process exit protection ENABLED');

      // Ultra-defensive wrapper with maximum logging
      const safeExecute = async (operation: () => Promise<void>) => {
        try {
          logger.info('🚀 Starting safe execution wrapper');
          await operation();
          logger.info('✅ Safe execution completed successfully');
        } catch (error) {
          logger.error('💥 SAFE EXECUTION CAUGHT ERROR:', error);
          logger.error('💥 Error message:', error instanceof Error ? error.message : String(error));
          logger.error('💥 Error stack:', error instanceof Error ? error.stack : 'No stack trace');
          logger.error('💥 Error name:', error instanceof Error ? error.name : 'Unknown');

          // Always try to respond, never let the request hang
          if (!res.headersSent) {
            logger.info('🔄 Attempting safe redirect to error page');
            try {
              res.redirect(`${config.urls.frontend}/dashboard?error=auth`);
              logger.info('✅ Safe redirect completed');
            } catch (redirectError) {
              logger.error('💥 Error during safe redirect:', redirectError);
              try {
                res.status(500).send('Authentication error');
                logger.info('✅ Safe error response sent');
              } catch (sendError) {
                logger.error('💥 Error during safe send:', sendError);
                logger.error('💥 CRITICAL: Cannot send any response!');
              }
            }
          } else {
            logger.warn('⚠️ Headers already sent, cannot send error response');
          }
        } finally {
          // Always disable protection after callback
          (global as any).authenticationInProgress = false;
          logger.info('🛡️ Process exit protection DISABLED');
        }
      };

      // Set a global timeout for the entire callback process
      const globalTimeoutId = setTimeout(() => {
        if (!res.headersSent) {
          logger.error('🚨 GLOBAL CALLBACK TIMEOUT - Sending emergency response');
          try {
            res.writeHead(302, {
              Location: `${config.urls.frontend}/dashboard?error=timeout`,
              'Content-Type': 'text/plain',
            });
            res.end('Authentication timeout. Redirecting...');
            logger.info('🚨 Emergency timeout response sent');
          } catch (emergencyError) {
            logger.error('💥 Emergency response failed:', emergencyError);
          }
        }
      }, 5000); // 5-second global timeout (reduced)

      await safeExecute(async () => {
        const code = req.query.code as string;
        const state = req.query.state as string;

        if (!code) {
          logger.error('No code received in PingFed callback');
          clearTimeout(globalTimeoutId);
          if (!res.headersSent) {
            res.writeHead(302, {
              Location: `${config.urls.frontend}/dashboard?error=auth`,
              'Content-Type': 'text/plain',
            });
            res.end('Authentication error. Redirecting...');
          }
          return;
        }

        logger.info('About to call handleCallback');

        // Handle the callback using PingFed provider with reduced timeout
        const handleCallbackWithTimeout = () => {
          return Promise.race([
            authManager.getPingFedProvider().handleCallback(code, state),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('HandleCallback timeout after 6 seconds')), 6000)
            ),
          ]);
        };

        logger.info('🚀 Starting handleCallback with 6-second timeout');
        const user = (await handleCallbackWithTimeout()) as any;
        logger.info('✅ handleCallback completed successfully');

        // Clear the global timeout since we succeeded
        clearTimeout(globalTimeoutId);

        logger.info('handleCallback completed successfully, user received', {
          userId: user.id,
          displayName: user.displayName,
        });

        // Set user in session with proper error handling and timeout
        logger.info('About to call req.login');

        const loginWithTimeout = () => {
          return Promise.race([
            new Promise<void>((resolve, reject) => {
              req.login(user, (err) => {
                if (err) {
                  logger.error('Error setting user in session:', err);
                  reject(err);
                } else {
                  logger.info('req.login completed successfully');
                  resolve();
                }
              });
            }),
            new Promise<void>((_, reject) =>
              setTimeout(() => reject(new Error('Login timeout')), 10000)
            ),
          ]);
        };

        await loginWithTimeout();

        logger.info('PingFed user successfully logged in', {
          userId: user.id,
          displayName: user.displayName,
        });

        // Log session details for debugging
        logger.info('🍪 Session after login', {
          sessionID: req.sessionID,
          isAuthenticated: req.isAuthenticated(),
          hasUser: !!req.user,
          sessionSaved: !!req.session,
          cookieSettings: req.session?.cookie,
        });

        logger.info('About to redirect to success');

        // Clear the global timeout since we're about to respond
        clearTimeout(globalTimeoutId);

        // Use immediate response with manual redirect
        try {
          if (!res.headersSent) {
            logger.info('🚀 Sending redirect response immediately');
            res.writeHead(302, {
              Location: `${config.urls.frontend}/dashboard?auth=success`,
              'Content-Type': 'text/plain',
            });
            res.end('Redirecting to dashboard...');
            logger.info('✅ Redirect response sent successfully');
          } else {
            logger.warn('⚠️ Headers already sent, cannot redirect');
          }
        } catch (redirectError) {
          logger.error('💥 Error sending redirect:', redirectError);
          if (!res.headersSent) {
            try {
              res
                .status(200)
                .send('Authentication successful. Please navigate to dashboard manually.');
            } catch (fallbackError) {
              logger.error('💥 Fallback response failed:', fallbackError);
            }
          }
        }

        // Disable protection immediately after successful auth
        logger.info('Authentication completed successfully');
        (global as any).authenticationInProgress = false;
        logger.info('🛡️ Process exit protection DISABLED');
      });
    })
  );
}

export { router as authRoutes };
