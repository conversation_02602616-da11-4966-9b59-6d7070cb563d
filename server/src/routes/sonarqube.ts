import { Router, Request, Response, NextFunction } from 'express';
import sonarQubeService from '../services/sonarQubeService';
import cosmosService from '../services/cosmosService';

const router = Router();

// Helper function to find team by ID or name from database
async function findTeamByIdOrName(teamIdentifier: string) {
  try {
    // First try to get by ID
    const teamById = await cosmosService.getTeam(teamIdentifier);
    if (teamById) {
      return teamById;
    }

    // Then try to get by exact name
    const teamByName = await cosmosService.getTeamByName(teamIdentifier);
    if (teamByName) {
      return teamByName;
    }

    // Finally, try to find by slugified name matching
    const allTeams = await cosmosService.getAllTeams();
    return allTeams.find(team => {
      const slugifiedTeamName = team.teamName.toLowerCase().replace(/\s+/g, '-');
      const slugifiedIdentifier = teamIdentifier.toLowerCase().replace(/\s+/g, '-');
      return slugifiedTeamName === slugifiedIdentifier;
    });
  } catch (error) {
    console.error('Error finding team:', error);
    return null;
  }
}

// Team-based API endpoints (simplified for single-platform teams)
router.get('/teams/:teamId/metrics', async (req, res) => {
  try {
    const { teamId } = req.params;
    const { metrics, period } = req.query;
    
    const team = await findTeamByIdOrName(teamId);
    if (!team) {
      return res.status(404).json({ error: 'Team not found' });
    }

    const metricsArray = metrics ? (metrics as string).split(',') : [];
    const result = await sonarQubeService.getProjectMetricsForTeam(
      team, 
      metricsArray, 
      period as string
    );
    
    res.json(result);
  } catch (error: any) {
    console.error('Error getting team metrics:', error);
    res.status(500).json({ error: error.message });
  }
});

router.get('/teams/:teamId/quality-score', async (req, res) => {
  try {
    const { teamId } = req.params;
    
    const team = await findTeamByIdOrName(teamId);
    if (!team) {
      return res.status(404).json({ error: 'Team not found' });
    }

    const result = await sonarQubeService.getCodeQualityScoreForTeam(team);
    res.json(result);
  } catch (error: any) {
    console.error('Error getting team quality score:', error);
    res.status(500).json({ error: error.message });
  }
});

router.get('/teams/:teamId/available-metrics', async (req, res) => {
  try {
    const { teamId } = req.params;
    
    const team = await findTeamByIdOrName(teamId);
    if (!team) {
      return res.status(404).json({ error: 'Team not found' });
    }

    const result = await sonarQubeService.getAvailableMetricsForTeam(team);
    res.json(result);
  } catch (error: any) {
    console.error('Error getting available metrics for team:', error);
    res.status(500).json({ error: error.message });
  }
});

// NEW: Direct API with baseUrl and projectKey parameters
router.get('/direct/metrics', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { baseUrl } = req.query;
    
    if (!baseUrl) {
      return res.status(400).json({ error: 'baseUrl query parameter is required' });
    }

    const metrics = await sonarQubeService.getAvailableMetrics(baseUrl as string);
    res.json(metrics);
  } catch (error) {
    next(error);
  }
});

router.get('/direct/projects/:projectKey/metrics', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { projectKey } = req.params;
    const { baseUrl, metrics, period } = req.query;
    
    if (!baseUrl) {
      return res.status(400).json({ error: 'baseUrl query parameter is required' });
    }

    const metricsArray = metrics ? (metrics as string).split(',') : undefined;
    
    const data = await sonarQubeService.getProjectMetrics(
      baseUrl as string,
      projectKey,
      metricsArray,
      period as string
    );
    
    res.json(data);
  } catch (error) {
    next(error);
  }
});

router.get('/direct/projects/:projectKey/quality-score', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { projectKey } = req.params;
    const { baseUrl } = req.query;
    
    if (!baseUrl) {
      return res.status(400).json({ error: 'baseUrl query parameter is required' });
    }

    const score = await sonarQubeService.getCodeQualityScore(baseUrl as string, projectKey);
    res.json(score);
  } catch (error) {
    next(error);
  }
});

// LEGACY: Backward compatibility routes (deprecated but functional)
router.get('/projects/:projectKey/metrics', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.warn('⚠️ Legacy SonarQube route used. Consider migrating to team-based or direct routes.');
    
    const { metrics, period } = req.query;
    const metricsArray = metrics ? (metrics as string).split(',') : undefined;
    
    const data = await sonarQubeService.getProjectMetricsLegacy(
      req.params.projectKey,
      metricsArray,
      period as string
    );
    
    res.json(data);
  } catch (error) {
    next(error);
  }
});

router.get('/metrics', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.warn('⚠️ Legacy SonarQube route used. Consider migrating to team-based or direct routes.');
    
    const metrics = await sonarQubeService.getAvailableMetricsLegacy();
    res.json(metrics);
  } catch (error) {
    next(error);
  }
});

router.get('/projects/:projectKey/quality-score', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.warn('⚠️ Legacy SonarQube route used. Consider migrating to team-based or direct routes.');
    
    const { projectKey } = req.params;
    
    if (!projectKey) {
      return res.status(400).json({ error: 'Project key is required' });
    }

    const score = await sonarQubeService.getCodeQualityScoreLegacy(projectKey);
    res.json(score);
  } catch (error) {
    next(error);
  }
});

// Get team-based SonarQube metrics
router.get('/teams/:teamName/metrics', async (req, res) => {
  try {
    const { teamName } = req.params;
    const { metrics } = req.query;

    console.log(`🔍 Fetching SonarQube metrics for team: ${teamName}`);

    // Get team from Cosmos DB
    const team = await findTeamByIdOrName(teamName);
    if (!team) {
      return res.status(404).json({ 
        error: 'Team not found',
        message: `Team "${teamName}" not found in database`
      });
    }

    if (metrics && typeof metrics === 'string') {
      // Split metrics into overall and new code metrics
      const requestedMetrics = metrics.split(',');
      const overallMetrics = requestedMetrics.filter(m => !m.startsWith('new_'));
      const newCodeMetrics = requestedMetrics.filter(m => m.startsWith('new_'));

      if (newCodeMetrics.length > 0 && overallMetrics.length > 0) {
        // Make separate API calls for overall and new code metrics
        const [overallResult, newCodeResult] = await Promise.all([
          sonarQubeService.getProjectMetricsForTeam(team, overallMetrics),
          sonarQubeService.getProjectMetricsForTeam(team, newCodeMetrics, '1') // period=1 for new code
        ]);

        // Combine the results
        const combinedMeasures = [
          ...overallResult.component.measures,
          ...newCodeResult.component.measures
        ];

        const combinedResult = {
          component: {
            ...overallResult.component,
            measures: combinedMeasures
          }
        };

        console.log(`✅ Successfully fetched combined metrics for team: ${teamName}`);
        console.log(`📊 Overall metrics: ${overallResult.component.measures.length}`);
        console.log(`📈 New code metrics: ${newCodeResult.component.measures.length}`);
        
        return res.json(combinedResult);
      } else if (newCodeMetrics.length > 0) {
        // Only new code metrics requested
        const result = await sonarQubeService.getProjectMetricsForTeam(team, newCodeMetrics, '1');
        console.log(`✅ Successfully fetched new code metrics for team: ${teamName}`);
        return res.json(result);
      } else {
        // Only overall metrics requested
        const result = await sonarQubeService.getProjectMetricsForTeam(team, overallMetrics);
        console.log(`✅ Successfully fetched overall metrics for team: ${teamName}`);
        return res.json(result);
      }
    } else {
      // No specific metrics requested, use default behavior
      const metricsArray: string[] = [];
      const result = await sonarQubeService.getProjectMetricsForTeam(team, metricsArray);
      console.log(`✅ Successfully fetched default metrics for team: ${teamName}`);
      return res.json(result);
    }

  } catch (error: any) {
    console.error('❌ Error fetching team SonarQube metrics:', error.message);
    
    if (error.message.includes('does not have sonarqube_url')) {
      res.status(400).json({
        error: 'Missing SonarQube configuration',
        message: 'Team metadata must include sonarqube_url and sonarqube_project_id'
      });
    } else {
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message
      });
    }
  }
});

// Get team-based test coverage metrics
router.get('/teams/:teamName/coverage', async (req, res) => {
  try {
    const { teamName } = req.params;

    console.log(`🔍 Fetching test coverage metrics for team: ${teamName}`);

    // Get team from Cosmos DB
    const team = await findTeamByIdOrName(teamName);
    if (!team) {
      return res.status(404).json({ 
        error: 'Team not found',
        message: `Team "${teamName}" not found in database`
      });
    }

    // Define coverage-specific metrics for overall code
    const overallMetrics = [
      'coverage',
      'lines_to_cover',
      'uncovered_lines',
      'line_coverage',
      'conditions_to_cover',
      'uncovered_conditions',
      'tests',
      'test_errors',
      'test_failures',
      'skipped_tests',
      'test_success_density',
      'test_execution_time'
    ];

    // Define coverage-specific metrics for new code
    const newCodeMetrics = [
      'new_coverage',
      'new_lines_to_cover',
      'new_uncovered_lines',
      'new_line_coverage',
      'new_conditions_to_cover',
      'new_uncovered_conditions'
    ];
    
    // Make two separate API calls - one for overall metrics, one for new code metrics
    const [overallResult, newCodeResult] = await Promise.all([
      sonarQubeService.getProjectMetricsForTeam(team, overallMetrics),
      sonarQubeService.getProjectMetricsForTeam(team, newCodeMetrics, '1') // period=1 for new code
    ]);

    // Combine the results
    const combinedMeasures = [
      ...overallResult.component.measures,
      ...newCodeResult.component.measures
    ];

    // Create combined response
    const combinedResult = {
      component: {
        ...overallResult.component,
        measures: combinedMeasures
      }
    };

    console.log(`✅ Successfully fetched coverage metrics for team: ${teamName}`);
    console.log(`📊 Overall metrics: ${overallResult.component.measures.length}`);
    console.log(`📈 New code metrics: ${newCodeResult.component.measures.length}`);
    
    res.json(combinedResult);

  } catch (error: any) {
    console.error('❌ Error fetching team test coverage metrics:', error.message);
    
    if (error.message.includes('does not have sonarqube_url')) {
      res.status(400).json({
        error: 'Missing SonarQube configuration',
        message: 'Team metadata must include sonarqube_url and sonarqube_project_id'
      });
    } else {
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message
      });
    }
  }
});

// Store SonarQube metrics in releases container
router.post('/teams/:teamName/metrics/store', async (req, res) => {
  try {
    const { teamName } = req.params;
    const { releaseId, metrics } = req.body;

    console.log(`📦 Storing SonarQube metrics for team: ${teamName}, release: ${releaseId}`);

    if (!releaseId || !metrics) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Both releaseId and metrics are required'
      });
    }

    // Get team from Cosmos DB
    const team = await findTeamByIdOrName(teamName);
    if (!team) {
      return res.status(404).json({ 
        error: 'Team not found',
        message: `Team "${teamName}" not found in database`
      });
    }

    // Initialize Cosmos DB connection
    const cosmosClient = new (require('@azure/cosmos').CosmosClient)(process.env.COSMOS_CONNECTION_STRING!);
    const database = cosmosClient.database(process.env.COSMOS_DATABASE_NAME!);
    const releasesContainer = database.container('releases');

    // Get or create release document
    let release;
    try {
      const { resource } = await releasesContainer.item(releaseId, releaseId).read();
      release = resource;
    } catch (error: any) {
      if (error.code === 404) {
        // Create new release document if it doesn't exist
        release = {
          id: releaseId,
          teamId: team.id,
          teamName: team.teamName,
          releaseName: `Release-${releaseId}`,
          releaseType: 'minor',
          releaseRolloutDate: new Date().toISOString(),
          releaseBranch: 'main',
          description: 'Auto-created for metrics storage',
          supportedPlatforms: {
            mobile: { enabled: false, ios: false, android: false },
            web: { enabled: true, desktop: true, mobileWeb: true },
            backend: { enabled: true }
          },
          createdBy: 'system',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        const { resource: newRelease } = await releasesContainer.items.create(release);
        release = newRelease;
        console.log(`✅ Created new release document: ${releaseId}`);
      } else {
        throw error;
      }
    }

    // Process and store SonarQube metrics
    const sonarQubeMetrics = {
      score: calculateSonarQubeScore(metrics),
      label: 'Code Quality Metrics',
      lastUpdated: new Date().toISOString(),
      status: determineSonarQubeStatus(metrics),
      bugs: parseInt(metrics.bugs || '0'),
      vulnerabilities: parseInt(metrics.vulnerabilities || '0'),
      codeSmells: parseInt(metrics.code_smells || '0'),
      coverage: parseFloat(metrics.coverage || '0'),
      maintainabilityRating: mapRatingToLetter(metrics.sqale_rating),
      reliabilityRating: mapRatingToLetter(metrics.reliability_rating),
      securityRating: mapRatingToLetter(metrics.security_rating),
      details: {
        linesOfCode: parseInt(metrics.ncloc || '0'),
        complexity: parseInt(metrics.complexity || '0'),
        duplicatedLines: parseFloat(metrics.duplicated_lines_density || '0'),
        unitTests: parseInt(metrics.tests || '0'),
        // New code metrics
        newBugs: parseInt(metrics.new_bugs || '0'),
        newVulnerabilities: parseInt(metrics.new_vulnerabilities || '0'),
        newCodeSmells: parseInt(metrics.new_code_smells || '0'),
        newCoverage: parseFloat(metrics.new_coverage || '0'),
        newLines: parseInt(metrics.new_lines || '0'),
        newDuplicatedLines: parseFloat(metrics.new_duplicated_lines_density || '0'),
        newMaintainabilityRating: mapRatingToLetter(metrics.new_maintainability_rating),
        newReliabilityRating: mapRatingToLetter(metrics.new_reliability_rating),
        newSecurityRating: mapRatingToLetter(metrics.new_security_rating)
      }
    };

    // Update release with SonarQube metrics
    const updatedRelease = {
      ...release,
      releaseMetrics: {
        ...release.releaseMetrics,
        codeQuality: sonarQubeMetrics
      },
      lastMetricsUpdate: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await releasesContainer.item(releaseId, releaseId).replace(updatedRelease);

    console.log(`✅ Successfully stored SonarQube metrics for release: ${releaseId}`);
    
    res.json({
      success: true,
      releaseId,
      teamName: team.teamName,
      metrics: sonarQubeMetrics,
      storedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error storing SonarQube metrics:', error.message);
    res.status(500).json({
      error: 'Failed to store metrics',
      message: error.message
    });
  }
});

// Helper functions for SonarQube metrics processing
function calculateSonarQubeScore(metrics: any): number {
  // Calculate a weighted score based on key metrics
  let score = 100;
  
  // Deduct points for bugs (high impact)
  const bugs = parseInt(metrics.bugs || '0');
  score -= Math.min(bugs * 2, 30);
  
  // Deduct points for vulnerabilities (critical impact)
  const vulnerabilities = parseInt(metrics.vulnerabilities || '0');
  score -= Math.min(vulnerabilities * 5, 40);
  
  // Deduct points for low coverage
  const coverage = parseFloat(metrics.coverage || '0');
  if (coverage < 80) {
    score -= Math.min((80 - coverage) * 0.5, 20);
  }
  
  // Deduct points for high duplication
  const duplication = parseFloat(metrics.duplicated_lines_density || '0');
  if (duplication > 5) {
    score -= Math.min((duplication - 5) * 2, 15);
  }
  
  return Math.max(Math.round(score), 0);
}

function determineSonarQubeStatus(metrics: any): 'success' | 'warning' | 'error' {
  const bugs = parseInt(metrics.bugs || '0');
  const vulnerabilities = parseInt(metrics.vulnerabilities || '0');
  const coverage = parseFloat(metrics.coverage || '0');
  
  if (vulnerabilities > 0 || bugs > 50) return 'error';
  if (bugs > 10 || coverage < 60) return 'warning';
  return 'success';
}

function mapRatingToLetter(rating: string): 'A' | 'B' | 'C' | 'D' | 'E' {
  const ratingMap: { [key: string]: 'A' | 'B' | 'C' | 'D' | 'E' } = {
    '1.0': 'A', '2.0': 'B', '3.0': 'C', '4.0': 'D', '5.0': 'E'
  };
  return ratingMap[rating] || 'C';
}

export default router;