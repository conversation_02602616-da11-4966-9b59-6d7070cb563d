import { Router, Request, Response } from 'express';
import { validate, validateQuery, validateParams } from '../middleware/validation';
import { optionalAuth, authenticate } from '../middleware/auth';
import cosmosService from '../services/cosmosService';
import { CreateClientReleaseData } from '../types/database';
import { User } from '../types/api';
import { logger } from '../utils/logger';
import {
  createReleaseSchema,
  updateReleaseSchema,
  releaseIdSchema,
  releaseQuerySchema,
} from '../modules/releases/releases.validation';
import { githubService } from '../services/githubService';

const router = Router();

// GET /api/releases - Get all releases with optional filters and timeout protection
router.get('/', validateQuery(releaseQuerySchema), async (req: Request, res: Response) => {
  try {
    logger.info('🚀 Releases API called - starting with timeout protection');
    const { startDate, endDate, teamId } = req.query;

    // Set a 5-second timeout for the API call
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Releases API timeout after 5 seconds')), 5000);
    });

    let releasesPromise;

    if (startDate && endDate) {
      releasesPromise = cosmosService.getReleasesByDateRange(
        startDate as string,
        endDate as string
      );
    } else if (teamId) {
      releasesPromise = cosmosService.getReleasesByTeam(teamId as string);
    } else {
      releasesPromise = cosmosService.getAllReleases();
    }

    const releases = await Promise.race([releasesPromise, timeoutPromise]);

    logger.info('✅ Releases API completed successfully', {
      releaseCount: Array.isArray(releases) ? releases.length : 'unknown',
    });

    res.json({
      success: true,
      data: releases,
    });
  } catch (error) {
    logger.error('💥 Error fetching releases:', error);

    // Return empty array instead of error to prevent UI crashes
    if (error instanceof Error && error.message.includes('timeout')) {
      logger.warn('⏰ Releases API timed out, returning empty array');
      res.json({
        success: true,
        data: [],
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch releases',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
});

// GET /api/releases/:id - Get specific release
router.get('/:id', validateParams(releaseIdSchema), async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const release = await cosmosService.getRelease(id);

    if (!release) {
      return res.status(404).json({
        success: false,
        message: 'Release not found',
      });
    }

    res.json({
      success: true,
      data: release,
    });
  } catch (error) {
    logger.error('Error fetching release:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch release',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

// POST /api/releases - Create new release (requires authentication)
router.post(
  '/',
  authenticate,
  validate(createReleaseSchema),
  async (req: Request, res: Response) => {
    try {
      const releaseData: CreateClientReleaseData = req.body;
      const user = req.user as User;

      if (!user?.email) {
        return res.status(401).json({
          success: false,
          message: 'User email not available',
        });
      }

      // Create the release first
      const release = await cosmosService.createRelease(releaseData, user.email);

      logger.info('Release created:', {
        releaseId: release.id,
        releaseName: release.releaseName,
        teamId: release.teamId,
        createdBy: user.email,
        platforms: {
          mobile: releaseData.supportedPlatforms.mobile.enabled
            ? {
                ios: releaseData.supportedPlatforms.mobile.ios,
                android: releaseData.supportedPlatforms.mobile.android,
              }
            : false,
          web: releaseData.supportedPlatforms.web.enabled
            ? {
                desktop: releaseData.supportedPlatforms.web.desktop,
                mobileWeb: releaseData.supportedPlatforms.web.mobileWeb,
              }
            : false,
          backend: releaseData.supportedPlatforms.backend.enabled,
        },
      });

      // Fetch and store metrics in the background (don't block the response)
      setImmediate(async () => {
        try {
          logger.info('Starting background metrics fetch for release:', release.id);

          // Get team information for metrics fetching
          const team = await cosmosService.getTeam(release.teamId);
          if (!team || !team.metadata?.sonarqube_project_id) {
            logger.warn(
              'Skipping metrics fetch - team not found or missing SonarQube config:',
              release.teamId
            );
            return;
          }

          // Fetch metrics from SonarQube with expanded metric list
          const metricsParams = [
            'bugs',
            'vulnerabilities',
            'code_smells',
            'ncloc',
            'complexity',
            'reliability_rating',
            'security_rating',
            'sqale_rating',
            'coverage',
            'duplicated_lines_density',
            'tests',
            'test_errors',
            'test_failures',
            'skipped_tests',
            'test_success_density',
            'test_execution_time',
            'lines_to_cover',
            'uncovered_lines',
            'line_coverage',
            'conditions_to_cover',
            'uncovered_conditions',
            // New code metrics
            'new_bugs',
            'new_vulnerabilities',
            'new_code_smells',
            'new_coverage',
            'new_lines',
            'new_duplicated_lines_density',
            'new_lines_to_cover',
            'new_uncovered_lines',
            'new_line_coverage',
            'new_conditions_to_cover',
            'new_uncovered_conditions',
            'new_reliability_rating',
            'new_security_rating',
            'new_maintainability_rating',
          ].join(',');

          // Fetch metrics from SonarQube using the proper endpoints for each metric type
          const baseUrl = 'http://localhost:3002';

          // Fetch all metrics in parallel including GitHub PRs, Build Stability, and Change Requests
          const metricsPromises = [
            // 1. Code Quality metrics - general metrics endpoint
            fetch(
              `${baseUrl}/api/sonarqube/teams/${encodeURIComponent(
                team.teamName
              )}/metrics?metrics=${metricsParams}`
            ),
            // 2. Code Coverage metrics - coverage-specific endpoint
            fetch(`${baseUrl}/api/sonarqube/teams/${encodeURIComponent(team.teamName)}/coverage`),
            // 3. Test Results - also from coverage endpoint but processed differently
            fetch(`${baseUrl}/api/sonarqube/teams/${encodeURIComponent(team.teamName)}/coverage`),
          ];

          // Fetch GitHub PRs separately to avoid type conflicts
          let githubPRsPromise: Promise<any[] | null> = Promise.resolve(null);
          if (team.metadata?.github_repo) {
            githubPRsPromise = fetchGitHubPRsForRelease(team, release, baseUrl);
          }

          // Fetch Build Stability separately
          let buildStabilityPromise: Promise<any | null> = Promise.resolve(null);
          if (team.metadata?.looper_team_id) {
            const branch = release.releaseBranch || team.metadata.repo_default_branch || 'main';
            buildStabilityPromise = fetch(
              `${baseUrl}/api/looper/teams/${team.id}/stability?branch=${encodeURIComponent(
                branch
              )}`
            )
              .then((response) => (response.ok ? response.json() : null))
              .catch((error) => {
                logger.warn('⚠️ Failed to fetch build stability during release creation:', error);
                return null;
              });
          }

          // Fetch Change Requests separately
          let changeRequestsPromise: Promise<any[] | null> = Promise.resolve(null);
          if (team.metadata?.serviceNowId) {
            changeRequestsPromise = fetch(`${baseUrl}/api/servicenow/change-requests/${release.id}`)
              .then(async (response) => {
                if (response.ok) {
                  const data: any = await response.json();
                  return data?.data || null;
                }
                return null;
              })
              .catch((error) => {
                logger.warn('⚠️ Failed to fetch change requests during release creation:', error);
                return null;
              });
          }

          const [metricsResults, pullRequestsResult, buildStabilityResult, changeRequestsResult] =
            await Promise.allSettled([
              Promise.allSettled(metricsPromises),
              githubPRsPromise,
              buildStabilityPromise,
              changeRequestsPromise,
            ]);

          // Extract individual metric results
          const [codeQualityResponse, codeCoverageResponse, testResultsResponse] =
            metricsResults.status === 'fulfilled'
              ? metricsResults.value
              : [
                  { status: 'rejected' as const, reason: 'Metrics fetch failed' },
                  { status: 'rejected' as const, reason: 'Metrics fetch failed' },
                  { status: 'rejected' as const, reason: 'Metrics fetch failed' },
                ];

          // Process and store each metric type with its specific data
          const storePromises = [];

          // Store code quality metrics
          if (codeQualityResponse.status === 'fulfilled' && codeQualityResponse.value?.ok) {
            const codeQualityData = await codeQualityResponse.value.json();
            if (codeQualityData && typeof codeQualityData === 'object') {
              storePromises.push(
                fetch(`${baseUrl}/api/metrics/${release.id}/code-quality`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    teamId: team.id,
                    metrics: codeQualityData,
                  }),
                })
              );
              logger.info('✅ Prepared code quality metrics for storage');
            }
          } else {
            logger.warn('⚠️ Failed to fetch code quality metrics');
          }

          // Store code coverage metrics
          if (codeCoverageResponse.status === 'fulfilled' && codeCoverageResponse.value?.ok) {
            const codeCoverageData = await codeCoverageResponse.value.json();
            if (codeCoverageData && typeof codeCoverageData === 'object') {
              storePromises.push(
                fetch(`${baseUrl}/api/metrics/${release.id}/test-coverage`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    teamId: team.id,
                    metrics: codeCoverageData,
                  }),
                })
              );
              logger.info('✅ Prepared code coverage metrics for storage');
            }
          } else {
            logger.warn('⚠️ Failed to fetch code coverage metrics');
          }

          // Store test results metrics
          if (testResultsResponse.status === 'fulfilled' && testResultsResponse.value?.ok) {
            const testResultsData = await testResultsResponse.value.json();
            if (testResultsData && typeof testResultsData === 'object') {
              storePromises.push(
                fetch(`${baseUrl}/api/metrics/${release.id}/test-results`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    teamId: team.id,
                    metrics: testResultsData,
                  }),
                })
              );
              logger.info('✅ Prepared test results metrics for storage');
            }
          } else {
            logger.warn('⚠️ Failed to fetch test results metrics');
          }

          // Store build stability metrics (only if not from stored fallback)
          if (buildStabilityResult.status === 'fulfilled' && buildStabilityResult.value) {
            const buildData = buildStabilityResult.value;
            if (buildData && typeof buildData === 'object') {
              // Check if the data came from stored fallback
              const isFromStoredFallback = buildData.isFromStored || buildData.fallbackReason;

              if (!isFromStoredFallback) {
                storePromises.push(
                  fetch(`${baseUrl}/api/metrics/${release.id}/build-stability`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      teamId: team.id,
                      buildData: (buildData as any).data || buildData,
                      releaseData: {
                        releaseBranch: release.releaseBranch,
                        createdAt: release.createdAt,
                        releaseRolloutDate: release.releaseRolloutDate,
                      },
                    }),
                  })
                );
                logger.info('✅ Prepared fresh build stability metrics for storage');
              } else {
                logger.info(
                  '📁 Skipping storage of build stability metrics - data came from stored fallback'
                );
              }
            }
          } else {
            logger.warn('⚠️ Failed to fetch build stability metrics');
          }

          // Store change requests metrics
          if (changeRequestsResult.status === 'fulfilled' && changeRequestsResult.value) {
            const changeRequests = changeRequestsResult.value;
            if (Array.isArray(changeRequests)) {
              storePromises.push(
                fetch(`${baseUrl}/api/metrics/${release.id}/change-requests`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    teamId: team.id,
                    changeRequests: changeRequests,
                    releaseData: {
                      releaseBranch: release.releaseBranch,
                      createdAt: release.createdAt,
                      releaseRolloutDate: release.releaseRolloutDate,
                    },
                  }),
                })
              );
              logger.info(
                `✅ Prepared change requests metrics for storage (${changeRequests.length} requests)`
              );
            }
          } else {
            logger.warn('⚠️ Failed to fetch change requests metrics');
          }

          // Store changelog metrics (GitHub PRs) as part of main metrics flow
          if (pullRequestsResult.status === 'fulfilled' && pullRequestsResult.value) {
            const pullRequests = pullRequestsResult.value;
            if (Array.isArray(pullRequests)) {
              // Add changelog to main storePromises array for synchronous processing
              const changelogStorePromise = fetch(
                `${baseUrl}/api/metrics/${release.id}/changelog`,
                {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    teamId: team.id,
                    pullRequests: pullRequests,
                    releaseData: {
                      releaseBranch: release.releaseBranch,
                      createdAt: release.createdAt,
                      releaseRolloutDate: release.releaseRolloutDate,
                    },
                  }),
                }
              );

              // Add to main promises array so it's processed with other metrics
              storePromises.push(changelogStorePromise);
              logger.info(
                `✅ Added changelog metrics to main storage flow (${pullRequests.length} PRs)`
              );
            }
          } else if (pullRequestsResult.status === 'rejected') {
            logger.warn('⚠️ Failed to fetch GitHub pull requests:', pullRequestsResult.reason);
          } else {
            logger.info('⚠️ No GitHub repository configured, skipping PR metrics');
          }

          // Execute all storage operations (including all 6 metrics now)
          if (storePromises.length > 0) {
            const storeResults = await Promise.allSettled(storePromises);

            // Log results with proper HTTP status checking
            const metricTypes = [
              'code-quality',
              'test-coverage',
              'test-results',
              'build-stability',
              'change-requests',
              'changelog',
            ];
            for (let i = 0; i < storeResults.length; i++) {
              const result = storeResults[i];
              const metricType = metricTypes[i] || `metric-${i}`;

              if (result.status === 'fulfilled') {
                try {
                  const response = result.value;
                  if (response.ok) {
                    const responseData = await response.json();
                    logger.info(`✅ Stored ${metricType} metrics successfully`);
                  } else {
                    const errorText = await response.text();
                    logger.error(
                      `❌ Failed to store ${metricType} metrics - HTTP ${response.status}:`,
                      errorText
                    );
                  }
                } catch (parseError) {
                  logger.error(`❌ Failed to parse ${metricType} storage response:`, parseError);
                }
              } else {
                logger.error(`❌ Failed to store ${metricType} metrics:`, result.reason);
              }
            }

            // Update confidence score after storing fresh metrics
            try {
              const updatedRelease = await cosmosService.updateConfidenceScore(release.id);
              logger.info('✅ Confidence score updated successfully after storing fresh metrics:', {
                releaseId: release.id,
                confidenceScore: updatedRelease.confidenceMetrics?.overallScore,
                freshMetricsStored: storePromises.length,
              });
            } catch (scoreError) {
              logger.error('❌ Failed to update confidence score:', scoreError);
            }

            logger.info('✅ Background metrics processing completed for release:', release.id);
          } else {
            logger.info(
              '📁 No fresh metrics to store for release (all from stored fallback) - skipping confidence score update to preserve timestamps:',
              release.id
            );
          }
        } catch (error) {
          logger.error('Error in background metrics fetch:', error);
        }
      });

      res.status(201).json({
        success: true,
        data: release,
      });
    } catch (error) {
      logger.error('Error creating release:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create release',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// PUT /api/releases/:id - Update release (requires authentication)
router.put(
  '/:id',
  authenticate,
  validateParams(releaseIdSchema),
  validate(updateReleaseSchema),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const updates = req.body;
      const user = req.user as User;

      if (!user?.email) {
        return res.status(401).json({
          success: false,
          message: 'User email not available',
        });
      }

      // Check if user has permission to edit this release
      const existingRelease = await cosmosService.getRelease(id);
      if (!existingRelease) {
        return res.status(404).json({
          success: false,
          message: 'Release not found',
        });
      }

      if (existingRelease.createdBy !== user.email) {
        return res.status(403).json({
          success: false,
          message: 'You can only edit releases you created',
        });
      }

      const release = await cosmosService.updateRelease(id, updates);

      logger.info('Release updated:', {
        releaseId: release.id,
        releaseName: release.releaseName,
        updatedBy: user.email,
        updates: Object.keys(updates),
      });

      res.json({
        success: true,
        data: release,
      });
    } catch (error) {
      logger.error('Error updating release:', error);

      if (error instanceof Error && error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: 'Release not found',
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to update release',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// DELETE /api/releases/:id - Delete release (requires authentication)
router.delete(
  '/:id',
  authenticate,
  validateParams(releaseIdSchema),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const user = req.user as User;

      if (!user?.email) {
        return res.status(401).json({
          success: false,
          message: 'User email not available',
        });
      }

      // Check if user has permission to delete this release
      const existingRelease = await cosmosService.getRelease(id);
      if (!existingRelease) {
        return res.status(404).json({
          success: false,
          message: 'Release not found',
        });
      }

      if (existingRelease.createdBy !== user.email) {
        return res.status(403).json({
          success: false,
          message: 'You can only delete releases you created',
        });
      }

      await cosmosService.deleteRelease(id);

      logger.info('Release deleted:', {
        releaseId: id,
        deletedBy: user.email,
      });

      res.json({
        success: true,
        message: 'Release deleted successfully',
      });
    } catch (error) {
      logger.error('Error deleting release:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete release',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// Helper functions for score calculation
function calculateCodeQualityScore(data: any): number {
  if (!data) return 0;

  let score = 100;

  // Deduct points for poor ratings
  const reliabilityRating = parseFloat(data.reliability_rating || '1.0');
  if (reliabilityRating > 1.0) score -= (reliabilityRating - 1.0) * 15;

  const securityRating = parseFloat(data.security_rating || '1.0');
  if (securityRating > 1.0) score -= (securityRating - 1.0) * 15;

  const maintainabilityRating = parseFloat(data.sqale_rating || '1.0');
  if (maintainabilityRating > 1.0) score -= (maintainabilityRating - 1.0) * 10;

  // Factor in bugs and vulnerabilities density
  const ncloc = parseInt(data.ncloc || '1');
  const bugs = parseInt(data.bugs || '0');
  const vulnerabilities = parseInt(data.vulnerabilities || '0');

  if (ncloc > 0) {
    const bugDensity = (bugs / ncloc) * 1000;
    const vulnDensity = (vulnerabilities / ncloc) * 1000;

    score -= Math.min(bugDensity * 5, 20);
    score -= Math.min(vulnDensity * 10, 30);
  }

  return Math.max(0, Math.round(score));
}

function calculateCoverageScore(data: any): number {
  if (!data || !data.coverage) return 0;

  const coverage = parseFloat(data.coverage);

  if (coverage >= 90) return 100;
  if (coverage >= 80) return 90;
  if (coverage >= 70) return 75;
  if (coverage >= 60) return 60;
  if (coverage >= 50) return 45;
  if (coverage >= 40) return 30;
  if (coverage >= 30) return 20;
  return Math.max(0, Math.round(coverage / 2));
}

function calculateTestResultsScore(data: any): number {
  if (!data || !data.tests) return 0;

  const total = parseInt(data.tests || '0');
  const failures = parseInt(data.test_failures || '0');
  const errors = parseInt(data.test_errors || '0');

  if (total === 0) return 0;

  const successRate = ((total - failures - errors) / total) * 100;

  if (successRate >= 99) return 100;
  if (successRate >= 95) return 90;
  if (successRate >= 90) return 80;
  if (successRate >= 80) return 65;
  if (successRate >= 70) return 50;
  return Math.max(0, Math.round(successRate / 2));
}

// Helper function to fetch GitHub PRs for a release
async function fetchGitHubPRsForRelease(
  team: any,
  release: any,
  baseUrl: string
): Promise<any[] | null> {
  try {
    if (!team.metadata?.github_repo) {
      logger.info('No GitHub repository configured for team:', team.teamName);
      return null;
    }

    // Extract repository path from GitHub URL
    const getRepoPath = (githubRepo: string): string => {
      // Handle various GitHub URL formats
      if (githubRepo.includes('://')) {
        // Full URL format: https://github.com/owner/repo or https://enterprise.github.com/owner/repo
        const urlParts = githubRepo.split('/');
        const pathParts = urlParts.slice(3); // e.g., ["owner", "repo"] or ["owner", "repo.git"]

        if (pathParts.length >= 2) {
          const owner = pathParts[0];
          const repo = pathParts[1].replace('.git', '');
          return `${owner}/${repo}`;
        }
      } else if (githubRepo.includes('/')) {
        // Already in owner/repo format
        return githubRepo.replace('.git', '');
      }

      // Fallback - return as is and let GitHub API handle any errors
      return githubRepo.replace('.git', '');
    };

    const repoPath = getRepoPath(team.metadata.github_repo);
    const branch = release.releaseBranch || team.metadata.repo_default_branch || 'main';

    logger.info(`[Release Creation] Fetching all PRs for ${repoPath}, branch: ${branch}`);

    // Use the GitHub service to fetch PRs without date filtering
    const pullRequests = await githubService.getPullRequestsByDateRange(repoPath, {
      base: branch,
      state: 'all',
      per_page: 100,
    });

    logger.info(
      `[Release Creation] Retrieved ${pullRequests.length} PRs for release ${release.id}`
    );
    return pullRequests;
  } catch (error) {
    logger.error('Error fetching GitHub PRs for release:', error);
    throw error;
  }
}

export { router as releasesRoutes };
