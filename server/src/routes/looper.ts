import express, { Request, Response } from 'express';
import { param, query, validationResult } from 'express-validator';
import looperService from '../services/looperService';
import cosmosService from '../services/cosmosService';
import { logger } from '../utils/logger';

const router = express.Router();

// Validation error handler
const handleValidationErrors = (req: Request, res: Response, next: Function) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

/**
 * GET /api/looper/teams/:teamId/builds
 * Get build history for a team
 */
router.get('/teams/:teamId/builds',
  param('teamId').isUUID().withMessage('Invalid team ID'),
  query('branch').optional().isString().withMessage('Branch must be a string'),
  query('size').optional().isInt({ min: 1, max: 200 }).withMessage('Size must be between 1 and 200'),
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { teamId } = req.params;
      const { branch, size = '100' } = req.query;

      // Get team information
      const team = await cosmosService.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ error: 'Team not found' });
      }

      if (!team.metadata.looper_team_id) {
        return res.status(400).json({ 
          error: 'Team not configured for build metrics',
          message: 'Team metadata must include looper_team_id'
        });
      }

      if (!branch) {
        return res.status(400).json({ 
          error: 'Branch parameter is required',
          message: 'Specify the branch to get build history for'
        });
      }

      // Fetch build history
      const buildHistory = await looperService.getBuildHistory(
        team.metadata.looper_team_id,
        branch as string,
        parseInt(size as string)
      );

      res.json({
        success: true,
        data: buildHistory,
        team: {
          id: team.id,
          name: team.teamName,
          looperTeamId: team.metadata.looper_team_id
        }
      });

    } catch (error: any) {
      logger.error('Error fetching build history:', error);
      
      // Check for specific error types and pass through detailed messages
      if (error.message.includes('SSL Certificate Error') || error.message.includes('certificate')) {
        res.status(502).json({
          error: 'SSL Certificate Error',
          message: error.message
        });
      } else if (error.message.includes('Network Error') || error.message.includes('Cannot connect')) {
        res.status(502).json({
          error: 'Network Error',
          message: error.message
        });
      } else if (error.message.includes('Timeout Error') || error.message.includes('timeout')) {
        res.status(502).json({
          error: 'Timeout Error',
          message: error.message
        });
      } else if (error.message.includes('Authentication Error') || error.message.includes('Access denied')) {
        res.status(401).json({
          error: 'Authentication Error',
          message: error.message
        });
      } else if (error.message.includes('not configured')) {
        res.status(400).json({
          error: 'Configuration Error',
          message: error.message
        });
      } else if (error.message.includes('Not Found')) {
        res.status(404).json({
          error: 'Not Found',
          message: error.message
        });
      } else if (error.message.includes('Server Error') || error.message.includes('Looper API is experiencing issues')) {
        res.status(502).json({
          error: 'External Service Error',
          message: error.message
        });
      } else {
        res.status(500).json({
          error: 'Internal Server Error',
          message: error.message || 'Failed to fetch build history'
        });
      }
    }
  }
);

/**
 * GET /api/looper/teams/:teamId/stability
 * Get build stability metrics for a team
 */
router.get('/teams/:teamId/stability',
  param('teamId').isUUID().withMessage('Invalid team ID'),
  query('branch').isString().withMessage('Branch parameter is required'),
  handleValidationErrors,
  async (req: Request, res: Response) => {
    const { teamId } = req.params;
    const { branch } = req.query;

    // Get team information outside try block so it's accessible in catch
    let team;
    try {
      team = await cosmosService.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ error: 'Team not found' });
      }

      if (!team.metadata.looper_team_id) {
        return res.status(400).json({
          error: 'Team not configured for build metrics',
          message: 'Team metadata must include looper_team_id'
        });
      }

      // Get build stability data
      const buildData = await looperService.getBuildStabilityForTeam(team, branch as string);

      // Calculate quality score
      const qualityScore = looperService.calculateQualityScore(buildData.statistics);

      res.json({
        success: true,
        data: {
          ...buildData,
          qualityScore
        },
        team: {
          id: team.id,
          name: team.teamName,
          looperTeamId: team.metadata.looper_team_id
        }
      });

    } catch (error: any) {
      logger.error('Error fetching build stability:', error);

      // Check if this is a network/connectivity error that warrants fallback to stored data
      const networkErrors = ['ENOTFOUND', 'ECONNREFUSED', 'ECONNRESET', 'ETIMEDOUT', 'EHOSTUNREACH', 'ENETUNREACH'];
      const isNetworkError = networkErrors.includes(error.code) ||
                            error.message?.includes('getaddrinfo ENOTFOUND') ||
                            error.message?.includes('Failed to fetch') ||
                            error.message?.includes('Network Error') ||
                            error.message?.includes('Cannot connect') ||
                            error.message?.includes('ENOTFOUND') ||
                            error.message?.includes('ECONNREFUSED');

      if (isNetworkError) {
        logger.info(`🔄 Network error detected (${error.code || error.message}), attempting to retrieve stored build stability data`);

        try {
          // Try to get stored build stability from releaseMetrics
          // We need to find the release that matches this team
          const releases = await cosmosService.getReleasesByTeam(req.params.teamId);
          const latestRelease = releases.length > 0 ? releases[0] : null;

          if (latestRelease) {
            const storedMetrics = await cosmosService.getReleaseMetrics(latestRelease.id);

            if (storedMetrics?.releaseMetrics?.buildStability) {
              logger.info(`✅ Successfully retrieved stored build stability data as fallback`);

              const storedData = storedMetrics.releaseMetrics.buildStability;

              // Transform stored data to match the expected live API format
              // Handle nested rawData structure (rawData.rawData)
              const nestedRawData = storedData.rawData?.rawData || storedData.rawData;
              let statistics = storedData.processedData?.statistics || nestedRawData?.statistics || {};
              const builds = nestedRawData?.builds || storedData.rawData?.builds || [];

              // Check if statistics are missing or all zeros but we have builds data
              const hasBuilds = builds && Array.isArray(builds) && builds.length > 0;
              const hasValidStats = statistics && statistics.total > 0;

              if (hasBuilds && !hasValidStats) {
                logger.info(`🔧 Recalculating statistics from ${builds.length} stored builds`);

                // Recalculate statistics from builds data
                statistics = looperService.calculateStatistics(builds);

                logger.info(`📊 Recalculated statistics:`, {
                  total: statistics.total,
                  success: statistics.success,
                  failure: statistics.failure,
                  successRate: statistics.successRate
                });
              }

              // Recalculate quality score if we have valid statistics
              let qualityScore = storedData.score || 50;
              if (statistics && statistics.total > 0) {
                qualityScore = looperService.calculateQualityScore(statistics);
                logger.info(`🎯 Recalculated quality score: ${qualityScore}`);
              }

              const fallbackData = {
                success: true,
                data: {
                  statistics,
                  builds,
                  teamId: storedData.teamId || req.params.teamId,
                  branch: req.query.branch || 'main',
                  qualityScore
                },
                team: team ? {
                  id: team.id,
                  name: team.teamName,
                  looperTeamId: team.metadata.looper_team_id
                } : {
                  id: req.params.teamId,
                  name: 'Unknown Team',
                  looperTeamId: storedData.teamId || req.params.teamId
                },
                isFromStored: true,
                fallbackReason: `Network error: ${error.code || error.message}`
              };

              logger.info(`📊 Fallback build stability data structure:`, {
                hasStatistics: !!fallbackData.data.statistics,
                statisticsTotal: fallbackData.data.statistics?.total || 0,
                buildsCount: fallbackData.data.builds?.length || 0,
                qualityScore: fallbackData.data.qualityScore,
                successRate: fallbackData.data.statistics?.successRate || 0
              });

              // Set cache-busting headers for fallback data to ensure client gets the isFromStored flag
              res.set({
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              });

              return res.json(fallbackData);
            } else {
              logger.info(`❌ No stored build stability data found for team: ${req.params.teamId}`);
            }
          } else {
            logger.info(`❌ No releases found for team: ${req.params.teamId}`);
          }
        } catch (fallbackError: any) {
          logger.error(`❌ Failed to retrieve stored build stability data:`, fallbackError.message);
        }
      }

      // If no fallback available or not a network error, return the original error
      // Check for specific error types and pass through detailed messages
      if (error.message.includes('SSL Certificate Error') || error.message.includes('certificate')) {
        res.status(502).json({
          error: 'SSL Certificate Error',
          message: error.message
        });
      } else if (error.message.includes('Network Error') || error.message.includes('Cannot connect')) {
        res.status(503).json({
          error: 'Service Unavailable',
          message: 'Build service is currently unavailable and no stored data available. Please try again later.',
          details: error.message
        });
      } else if (error.message.includes('Timeout Error') || error.message.includes('timeout')) {
        res.status(502).json({
          error: 'Timeout Error',
          message: error.message
        });
      } else if (error.message.includes('Authentication Error') || error.message.includes('Access denied')) {
        res.status(401).json({
          error: 'Authentication Error',
          message: error.message
        });
      } else if (error.message.includes('not configured')) {
        res.status(400).json({
          error: 'Configuration Error',
          message: error.message
        });
      } else if (error.message.includes('Not Found')) {
        res.status(404).json({
          error: 'Not Found',
          message: error.message
        });
      } else if (error.message.includes('Server Error') || error.message.includes('Looper API is experiencing issues')) {
        res.status(502).json({
          error: 'External Service Error',
          message: error.message
        });
      } else {
        res.status(500).json({
          error: 'Internal Server Error',
          message: error.message || 'Failed to fetch build stability metrics'
        });
      }
    }
  }
);

export default router; 