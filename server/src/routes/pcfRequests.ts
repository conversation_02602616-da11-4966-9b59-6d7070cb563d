import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { cosmosClient, cosmosConfig } from '../config/cosmosdb';
import { ClientRelease } from '../types/database';

const router = express.Router();

// Helper to get releases container
async function getReleasesContainer() {
  const database = cosmosClient.database(cosmosConfig.databaseId);
  return database.container('releases');
}

// PCF Request interface
interface PCFRequest {
  id: string;
  releaseId: string;
  teamId: string;
  jiraTicket: string;
  summary: string;
  description: string;
  requestedBy: string;
  requestedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  approver?: string;
  approvedAt?: string;
  approverRole?: string;
  rejectionReason?: string;
}

// Helper function to update PCF requests timestamp in releaseMetrics
async function updatePCFRequestsTimestamp(container: any, release: ClientRelease, pcfRequests: PCFRequest[]) {
  const stats = {
    totalRequests: pcfRequests.length,
    pendingRequests: pcfRequests.filter(req => req.status === 'pending').length,
    approvedRequests: pcfRequests.filter(req => req.status === 'approved').length,
    rejectedRequests: pcfRequests.filter(req => req.status === 'rejected').length,
  };

  const pcfMetrics = {
    score: Math.round((stats.approvedRequests / Math.max(stats.totalRequests, 1)) * 100),
    label: `${stats.totalRequests} total requests`,
    lastUpdated: new Date().toISOString(),
    status: 'success' as const,
    ...stats,
    rawData: pcfRequests
  };

  const updatedRelease = {
    ...release,
    // Remove pcfRequests from root level - now only stored in releaseMetrics
    pcfRequests: undefined,
    releaseMetrics: {
      ...release.releaseMetrics,
      pcfRequests: pcfMetrics
    },
    lastMetricsUpdate: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  return updatedRelease;
}

/**
 * Get all PCF requests for a release
 */
router.get('/:releaseId', async (req, res) => {
  try {
    const { releaseId } = req.params;

    const container = await getReleasesContainer();
    const { resource: release } = await container.item(releaseId, releaseId).read<ClientRelease>();

    if (!release) {
      return res.status(404).json({ error: 'Release not found' });
    }

    // Get PCF requests from releaseMetrics (new location) or fallback to old location for backward compatibility
    let pcfRequests = release.releaseMetrics?.pcfRequests?.rawData || (release as any).pcfRequests || [];

    // Sort by creation date (newest first)
    pcfRequests.sort((a: PCFRequest, b: PCFRequest) =>
      new Date(b.requestedAt).getTime() - new Date(a.requestedAt).getTime()
    );

    // Always update timestamp when PCF requests are accessed (like other metrics)
    let shouldUpdateTimestamp = false;

    // If data is in old location, migrate it to new location
    if ((release as any).pcfRequests && !release.releaseMetrics?.pcfRequests?.rawData) {
      console.log(`🔄 Migrating PCF requests to releaseMetrics for release: ${releaseId}`);
      shouldUpdateTimestamp = true;
    }
    // If no timestamp metadata exists, create it for backward compatibility
    else if (!release.releaseMetrics?.pcfRequests?.lastUpdated && pcfRequests.length > 0) {
      console.log(`🔄 Creating PCF timestamp metadata for release: ${releaseId}`);
      shouldUpdateTimestamp = true;
    }
    // Always update timestamp on access (like refreshing other metrics)
    else if (pcfRequests.length > 0) {
      console.log(`🔄 Refreshing PCF timestamp for release: ${releaseId}`);
      shouldUpdateTimestamp = true;
    }

    // Update timestamp if needed
    if (shouldUpdateTimestamp) {
      const updatedRelease = await updatePCFRequestsTimestamp(container, release, pcfRequests);
      await container.item(releaseId, releaseId).replace(updatedRelease);
    }

    res.json(pcfRequests);
  } catch (error) {
    console.error('Error fetching PCF requests:', error);
    res.status(500).json({ error: 'Failed to fetch PCF requests' });
  }
});

/**
 * Create a new PCF request
 */
router.post('/:releaseId', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { jiraTicket, summary, description, requestedBy, approver } = req.body;
    
    if (!jiraTicket || !summary || !description || !requestedBy) {
      return res.status(400).json({ 
        error: 'Missing required fields: jiraTicket, summary, description, requestedBy' 
      });
    }
    
    const container = await getReleasesContainer();
    const { resource: release } = await container.item(releaseId, releaseId).read<ClientRelease>();
    
    if (!release) {
      return res.status(404).json({ error: 'Release not found' });
    }
    
    // Create new PCF request
    const pcfRequest: PCFRequest = {
      id: uuidv4(),
      releaseId,
      teamId: release.teamId,
      jiraTicket: jiraTicket.trim(),
      summary: summary.trim(),
      description: description.trim(),
      requestedBy: requestedBy.trim(),
      requestedAt: new Date().toISOString(),
      status: 'pending',
      ...(approver && { approver: approver.trim() })
    };
    
    // Add PCF request to release document (get from new or old location)
    const existingPcfRequests = release.releaseMetrics?.pcfRequests?.rawData || (release as any).pcfRequests || [];
    const newPcfRequests = [...existingPcfRequests, pcfRequest];

    // Update release with new PCF request and timestamp metadata (stored only in releaseMetrics)
    const updatedRelease = await updatePCFRequestsTimestamp(container, release, newPcfRequests);

    await container.item(releaseId, releaseId).replace(updatedRelease);
    
    console.log(`✅ Created PCF request ${pcfRequest.id} for release ${releaseId}`);
    res.status(201).json(pcfRequest);
  } catch (error) {
    console.error('Error creating PCF request:', error);
    res.status(500).json({ error: 'Failed to create PCF request' });
  }
});

/**
 * Approve a PCF request
 */
router.post('/:requestId/approve', async (req, res) => {
  try {
    const { requestId } = req.params;
    const { approverEmail } = req.body;
    
    if (!approverEmail) {
      return res.status(400).json({ error: 'Approver email is required' });
    }
    
    // Find the release containing this PCF request (check both old and new locations)
    const container = await getReleasesContainer();
    const query = {
      query: 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.pcfRequests, {"id": @requestId}, true) OR ARRAY_CONTAINS(c.releaseMetrics.pcfRequests.rawData, {"id": @requestId}, true)',
      parameters: [{ name: '@requestId', value: requestId }]
    };

    const { resources } = await container.items.query<ClientRelease>(query).fetchAll();

    if (resources.length === 0) {
      return res.status(404).json({ error: 'PCF request not found' });
    }

    const release = resources[0];
    const pcfRequests = release.releaseMetrics?.pcfRequests?.rawData || (release as any).pcfRequests || [];
    const requestIndex = pcfRequests.findIndex((req: PCFRequest) => req.id === requestId);
    
    if (requestIndex === -1) {
      return res.status(404).json({ error: 'PCF request not found' });
    }
    
    const pcfRequest = pcfRequests[requestIndex];
    
    if (pcfRequest.status !== 'pending') {
      return res.status(400).json({ 
        error: `Cannot approve request with status: ${pcfRequest.status}` 
      });
    }
    
    // TODO: Add authorization check here
    // Verify that the approver is authorized for this team's PCF approvals
    
    // Update the PCF request
    pcfRequests[requestIndex] = {
      ...pcfRequest,
      status: 'approved',
      approver: approverEmail,
      approvedAt: new Date().toISOString()
    };

    // Update the release document with timestamp metadata (stored only in releaseMetrics)
    const updatedRelease = await updatePCFRequestsTimestamp(container, release, pcfRequests);

    await container.item(release.id, release.id).replace(updatedRelease);
    
    console.log(`✅ Approved PCF request ${requestId} by ${approverEmail}`);
    res.json(pcfRequests[requestIndex]);
  } catch (error) {
    console.error('Error approving PCF request:', error);
    res.status(500).json({ error: 'Failed to approve PCF request' });
  }
});

/**
 * Reject a PCF request
 */
router.post('/:requestId/reject', async (req, res) => {
  try {
    const { requestId } = req.params;
    const { approverEmail, rejectionReason } = req.body;
    
    if (!approverEmail) {
      return res.status(400).json({ error: 'Approver email is required' });
    }
    
    if (!rejectionReason) {
      return res.status(400).json({ error: 'Rejection reason is required' });
    }
    
    // Find the release containing this PCF request (check both old and new locations)
    const container = await getReleasesContainer();
    const query = {
      query: 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.pcfRequests, {"id": @requestId}, true) OR ARRAY_CONTAINS(c.releaseMetrics.pcfRequests.rawData, {"id": @requestId}, true)',
      parameters: [{ name: '@requestId', value: requestId }]
    };

    const { resources } = await container.items.query<ClientRelease>(query).fetchAll();

    if (resources.length === 0) {
      return res.status(404).json({ error: 'PCF request not found' });
    }

    const release = resources[0];
    const pcfRequests = release.releaseMetrics?.pcfRequests?.rawData || (release as any).pcfRequests || [];
    const requestIndex = pcfRequests.findIndex((req: PCFRequest) => req.id === requestId);
    
    if (requestIndex === -1) {
      return res.status(404).json({ error: 'PCF request not found' });
    }
    
    const pcfRequest = pcfRequests[requestIndex];
    
    if (pcfRequest.status !== 'pending') {
      return res.status(400).json({ 
        error: `Cannot reject request with status: ${pcfRequest.status}` 
      });
    }
    
    // TODO: Add authorization check here
    // Verify that the approver is authorized for this team's PCF approvals
    
    // Update the PCF request
    pcfRequests[requestIndex] = {
      ...pcfRequest,
      status: 'rejected',
      approver: approverEmail,
      approvedAt: new Date().toISOString(),
      rejectionReason: rejectionReason.trim()
    };

    // Update the release document with timestamp metadata (stored only in releaseMetrics)
    const updatedRelease = await updatePCFRequestsTimestamp(container, release, pcfRequests);

    await container.item(release.id, release.id).replace(updatedRelease);
    
    console.log(`❌ Rejected PCF request ${requestId} by ${approverEmail}: ${rejectionReason}`);
    res.json(pcfRequests[requestIndex]);
  } catch (error) {
    console.error('Error rejecting PCF request:', error);
    res.status(500).json({ error: 'Failed to reject PCF request' });
  }
});

/**
 * Update a PCF request (only if pending)
 */
router.put('/:requestId', async (req, res) => {
  try {
    const { requestId } = req.params;
    const { jiraTicket, summary, description } = req.body;
    
    // Find the release containing this PCF request
    const container = await getReleasesContainer();
    const query = {
      query: 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.pcfRequests, {"id": @requestId}, true)',
      parameters: [{ name: '@requestId', value: requestId }]
    };
    
    const { resources } = await container.items.query<ClientRelease>(query).fetchAll();
    
    if (resources.length === 0) {
      return res.status(404).json({ error: 'PCF request not found' });
    }
    
    const release = resources[0];
    const pcfRequests = (release as any).pcfRequests || [];
    const requestIndex = pcfRequests.findIndex((req: PCFRequest) => req.id === requestId);
    
    if (requestIndex === -1) {
      return res.status(404).json({ error: 'PCF request not found' });
    }
    
    const pcfRequest = pcfRequests[requestIndex];
    
    if (pcfRequest.status !== 'pending') {
      return res.status(400).json({ 
        error: `Cannot update request with status: ${pcfRequest.status}` 
      });
    }
    
    // Update the PCF request
    pcfRequests[requestIndex] = {
      ...pcfRequest,
      ...(jiraTicket && { jiraTicket: jiraTicket.trim() }),
      ...(summary && { summary: summary.trim() }),
      ...(description && { description: description.trim() })
    };
    
    // Update the release document
    const updatedRelease = {
      ...release,
      pcfRequests,
      updatedAt: new Date().toISOString()
    };
    
    await container.item(release.id, release.id).replace(updatedRelease);
    
    console.log(`✅ Updated PCF request ${requestId}`);
    res.json(pcfRequests[requestIndex]);
  } catch (error) {
    console.error('Error updating PCF request:', error);
    res.status(500).json({ error: 'Failed to update PCF request' });
  }
});

/**
 * Delete a PCF request (only if pending)
 */
router.delete('/:requestId', async (req, res) => {
  try {
    const { requestId } = req.params;
    
    // Find the release containing this PCF request
    const container = await getReleasesContainer();
    const query = {
      query: 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.pcfRequests, {"id": @requestId}, true)',
      parameters: [{ name: '@requestId', value: requestId }]
    };
    
    const { resources } = await container.items.query<ClientRelease>(query).fetchAll();
    
    if (resources.length === 0) {
      return res.status(404).json({ error: 'PCF request not found' });
    }
    
    const release = resources[0];
    const pcfRequests = (release as any).pcfRequests || [];
    const requestIndex = pcfRequests.findIndex((req: PCFRequest) => req.id === requestId);
    
    if (requestIndex === -1) {
      return res.status(404).json({ error: 'PCF request not found' });
    }
    
    const pcfRequest = pcfRequests[requestIndex];
    
    if (pcfRequest.status !== 'pending') {
      return res.status(400).json({ 
        error: `Cannot delete request with status: ${pcfRequest.status}` 
      });
    }
    
    // Remove the PCF request
    pcfRequests.splice(requestIndex, 1);
    
    // Update the release document
    const updatedRelease = {
      ...release,
      pcfRequests,
      updatedAt: new Date().toISOString()
    };
    
    await container.item(release.id, release.id).replace(updatedRelease);
    
    console.log(`🗑️ Deleted PCF request ${requestId}`);
    res.json({ message: 'PCF request deleted successfully' });
  } catch (error) {
    console.error('Error deleting PCF request:', error);
    res.status(500).json({ error: 'Failed to delete PCF request' });
  }
});

export default router; 