import { Router, Request, Response } from 'express';
import { CosmosClient } from '@azure/cosmos';
import { v4 as uuidv4 } from 'uuid';
import { Release, ReleaseMetrics, ConfidenceMetrics, MetricScore } from '../types/database';
import cosmosService from '../services/cosmosService';
import ConfidenceScoreService, { 
  DEFAULT_WEIGHTINGS, 
  Weightings, 
  ConfidenceMetrics as ConfidenceMetricsType,
  ReleaseMetrics as ReleaseMetricsType
} from '../services/confidenceScoreService';

const router = Router();

const realMetricsService = {
  // Get real Code Quality metrics from stored data
  async getCodeQualityMetrics(releaseId: string): Promise<ReleaseMetrics['codeQuality'] | null> {
    try {
      const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.codeQuality) {
        return releaseMetrics.releaseMetrics.codeQuality;
      }
      return null;
    } catch (error) {
      console.error('Error fetching code quality metrics:', error);
      return null;
    }
  },

  // Get real Test Coverage metrics from stored data
  async getTestCoverageMetrics(releaseId: string): Promise<any> {
    try {
      const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.codeCoverage) {
        return releaseMetrics.releaseMetrics.codeCoverage;
      }
      return null;
    } catch (error) {
      console.error('Error fetching test coverage metrics:', error);
      return null;
    }
  },

  // Get real Test Results metrics from stored data
  async getTestResultsMetrics(releaseId: string): Promise<any> {
    try {
      const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.testResults) {
        return releaseMetrics.releaseMetrics.testResults;
      }
      return null;
    } catch (error) {
      console.error('Error fetching test results metrics:', error);
      return null;
    }
  },

  // Get real Build Stability metrics from stored data
  async getBuildStabilityMetrics(releaseId: string): Promise<any> {
    try {
      const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.buildStability) {
        const storedData = releaseMetrics.releaseMetrics.buildStability;

        // Check if statistics are missing or all zeros but we have builds data
        const nestedRawData = storedData.rawData?.rawData || storedData.rawData;
        let statistics = storedData.processedData?.statistics || nestedRawData?.statistics || storedData.statistics || {};
        const builds = nestedRawData?.builds || storedData.rawData?.builds || storedData.builds || [];

        const hasBuilds = builds && Array.isArray(builds) && builds.length > 0;
        const hasValidStats = statistics && statistics.total > 0;

        if (hasBuilds && !hasValidStats) {
          console.log(`🔧 Recalculating Build Stability statistics from ${builds.length} stored builds`);

          // Import looper service to recalculate statistics
          const { default: looperService } = await import('../services/looperService');
          statistics = looperService.calculateStatistics(builds);

          console.log(`📊 Recalculated Build Stability statistics:`, {
            total: statistics.total,
            success: statistics.success,
            failure: statistics.failure,
            successRate: statistics.successRate
          });

          // Update the stored data with recalculated statistics
          storedData.statistics = statistics;
          if (storedData.processedData) {
            storedData.processedData.statistics = statistics;
          }

          // Recalculate quality score
          const qualityScore = looperService.calculateQualityScore(statistics);
          storedData.score = qualityScore;

          console.log(`🎯 Recalculated Build Stability quality score: ${qualityScore}`);
        }

        return storedData;
      }
      return null;
    } catch (error) {
      console.error('Error fetching build stability metrics:', error);
      return null;
    }
  },

  // Get real Change Requests metrics from stored data
  async getChangeRequestsMetrics(releaseId: string): Promise<any> {
    try {
      const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.changeRequests) {
        return releaseMetrics.releaseMetrics.changeRequests;
      }
      return null;
    } catch (error) {
      console.error('Error fetching change requests metrics:', error);
      return null;
  }
  },

  // Get real Change Log metrics from stored data  
  async getChangeLogMetrics(releaseId: string): Promise<any> {
    try {
      const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.changeLog) {
        return releaseMetrics.releaseMetrics.changeLog;
  }
      // Fallback to check for 'changelog' naming convention
      if (releaseMetrics?.releaseMetrics?.changelog) {
        return releaseMetrics.releaseMetrics.changelog;
  }
      return null;
    } catch (error) {
      console.error('Error fetching change log metrics:', error);
      return null;
}
  }
};

// GET /metrics/:releaseId - Get metrics for a release
router.get('/:releaseId', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { refresh } = req.query;

    // Get current release using cosmosService
    const releaseData = await cosmosService.getReleaseMetrics(releaseId);
    
    if (!releaseData) {
      return res.status(404).json({ error: 'Release not found' });
    }

    // Check if we need to refresh metrics
    const now = new Date();
    const lastUpdate = releaseData.lastMetricsUpdate ? new Date(releaseData.lastMetricsUpdate) : null;
    const updateFrequency = 30; // default 30 minutes
    const shouldRefresh = refresh === 'true' || !lastUpdate || 
      (now.getTime() - lastUpdate.getTime()) > (updateFrequency * 60 * 1000);

    if (shouldRefresh) {
      // Fetch real metrics from stored data
      const [codeQualityMetrics, testCoverageMetrics, testResultsMetrics, buildStabilityMetrics, changeRequestsMetrics, changeLogMetrics] = await Promise.all([
        realMetricsService.getCodeQualityMetrics(releaseId),
        realMetricsService.getTestCoverageMetrics(releaseId),
        realMetricsService.getTestResultsMetrics(releaseId),
        realMetricsService.getBuildStabilityMetrics(releaseId),
        realMetricsService.getChangeRequestsMetrics(releaseId),
        realMetricsService.getChangeLogMetrics(releaseId)
      ]);

      const releaseMetrics: any = {};
      
      if (codeQualityMetrics) {
        releaseMetrics.codeQuality = codeQualityMetrics;
      }
      if (testCoverageMetrics) {
        releaseMetrics.codeCoverage = testCoverageMetrics;
      }
      if (testResultsMetrics) {
        releaseMetrics.testResults = testResultsMetrics;
      }
      if (buildStabilityMetrics) {
        releaseMetrics.buildStability = buildStabilityMetrics;
      }
      if (changeRequestsMetrics) {
        releaseMetrics.changeRequests = changeRequestsMetrics;
      }
      if (changeLogMetrics) {
        releaseMetrics.changeLog = changeLogMetrics;
      }

      // Calculate confidence score using centralized service
      const overallScore = ConfidenceScoreService.calculateConfidenceScore(releaseMetrics, DEFAULT_WEIGHTINGS);
      
      // Calculate trending
      const previousScore = releaseData.confidenceMetrics?.overallScore;
      let trending: { direction: 'up' | 'down' | 'stable', changePercent: number, previousScore?: number } = { direction: 'stable', changePercent: 0, previousScore };
      
      if (previousScore !== undefined) {
        const change = overallScore - previousScore;
        const changePercent = Math.round((change / previousScore) * 100);
        trending = {
          direction: change > 2 ? 'up' : change < -2 ? 'down' : 'stable',
          changePercent: Math.abs(changePercent),
          previousScore
        };
      }

      const confidenceMetrics: ConfidenceMetricsType = {
        overallScore,
        lastCalculated: new Date().toISOString(),
        weightings: DEFAULT_WEIGHTINGS,
        trending
      };

      // Update using cosmosService
      await cosmosService.updateConfidenceScore(releaseId);

      res.json({
        releaseId,
        releaseMetrics,
        confidenceMetrics,
        lastUpdated: now.toISOString(),
        refreshed: true
      });
    } else {
      // Return cached metrics
      res.json({
        releaseId,
        releaseMetrics: releaseData.releaseMetrics || {},
        confidenceMetrics: releaseData.confidenceMetrics || ConfidenceScoreService.createConfidenceMetrics({}),
        lastUpdated: releaseData.lastMetricsUpdate,
        refreshed: false
      });
    }
  } catch (error) {
    console.error('Error getting metrics:', error);
    res.status(500).json({ error: 'Failed to get metrics' });
  }
});

// POST /metrics/:releaseId/refresh - Force refresh metrics
router.post('/:releaseId/refresh', async (req, res) => {
  try {
    const { releaseId } = req.params;

    // Get current release using cosmosService
    const releaseData = await cosmosService.getReleaseMetrics(releaseId);
    
    if (!releaseData) {
      return res.status(404).json({ error: 'Release not found' });
    }

    // Force refresh all real metrics
    const [codeQualityMetrics, testCoverageMetrics, testResultsMetrics, buildStabilityMetrics, changeRequestsMetrics, changeLogMetrics] = await Promise.all([
      realMetricsService.getCodeQualityMetrics(releaseId),
      realMetricsService.getTestCoverageMetrics(releaseId),
      realMetricsService.getTestResultsMetrics(releaseId),
      realMetricsService.getBuildStabilityMetrics(releaseId),
      realMetricsService.getChangeRequestsMetrics(releaseId),
      realMetricsService.getChangeLogMetrics(releaseId)
    ]);

    const releaseMetrics: any = {};
    
    if (codeQualityMetrics) {
      releaseMetrics.codeQuality = codeQualityMetrics;
    }
    if (testCoverageMetrics) {
      releaseMetrics.codeCoverage = testCoverageMetrics;
    }
    if (testResultsMetrics) {
      releaseMetrics.testResults = testResultsMetrics;
    }
    if (buildStabilityMetrics) {
      releaseMetrics.buildStability = buildStabilityMetrics;
    }
    if (changeRequestsMetrics) {
      releaseMetrics.changeRequests = changeRequestsMetrics;
    }
    if (changeLogMetrics) {
      releaseMetrics.changeLog = changeLogMetrics;
    }

    // Calculate confidence score with new weightings
    const newWeightings = { 
      codeQuality: 20, 
      codeCoverage: 20, 
      testResults: 20, 
      buildStability: 20, 
      changeRequests: 10, 
      changeLog: 10 
    };
    const overallScore = ConfidenceScoreService.calculateConfidenceScore(releaseMetrics, newWeightings);
    
    // Calculate trending
    const previousScore = releaseData.confidenceMetrics?.overallScore;
    let trending: { direction: 'up' | 'down' | 'stable', changePercent: number, previousScore?: number } = { direction: 'stable', changePercent: 0, previousScore };
    
    if (previousScore !== undefined) {
      const change = overallScore - previousScore;
      const changePercent = Math.round((change / previousScore) * 100);
      trending = {
        direction: change > 2 ? 'up' : change < -2 ? 'down' : 'stable',
        changePercent: Math.abs(changePercent),
        previousScore
      };
    }

    const confidenceMetrics: ConfidenceMetrics = {
      overallScore,
      lastCalculated: new Date().toISOString(),
      weightings: newWeightings,
      trending
    };

    // Update using cosmosService
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      message: 'Metrics refreshed successfully',
      releaseId,
      releaseMetrics,
      confidenceMetrics,
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error refreshing metrics:', error);
    res.status(500).json({ error: 'Failed to refresh metrics' });
  }
});

// PUT /metrics/:releaseId/weightings - Update confidence score weightings
router.put('/:releaseId/weightings', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { weightings } = req.body;

    // Validate weightings
    if (!weightings || typeof weightings !== 'object') {
      return res.status(400).json({ error: 'Invalid weightings format' });
    }

    const { codeQuality, codeCoverage, testResults, buildStability, changeRequests, changeLog } = weightings;
    if (typeof codeQuality !== 'number' || typeof codeCoverage !== 'number' || 
        typeof testResults !== 'number' || typeof buildStability !== 'number' ||
        typeof changeRequests !== 'number' || typeof changeLog !== 'number') {
      return res.status(400).json({ error: 'All weightings must be numbers' });
    }

    if (codeQuality + codeCoverage + testResults + buildStability + changeRequests + changeLog !== 100) {
      return res.status(400).json({ error: 'Weightings must sum to 100' });
    }

    // Get current release using cosmosService
    const releaseData = await cosmosService.getReleaseMetrics(releaseId);
    
    if (!releaseData) {
      return res.status(404).json({ error: 'Release not found' });
    }

    // Recalculate confidence score with new weightings
    let newOverallScore = 0;
    if (releaseData.releaseMetrics) {
      newOverallScore = ConfidenceScoreService.calculateConfidenceScore(releaseData.releaseMetrics, weightings);
    }

    const updatedConfidenceMetrics: any = {
      ...releaseData.confidenceMetrics,
      overallScore: newOverallScore,
      weightings,
      lastCalculated: new Date().toISOString()
    };

    // Update using cosmosService (we'll need to enhance this method to accept custom weightings)
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      message: 'Weightings updated successfully',
      confidenceMetrics: updatedConfidenceMetrics
    });
  } catch (error) {
    console.error('Error updating weightings:', error);
    res.status(500).json({ error: 'Failed to update weightings' });
  }
});

// Store Code Quality metrics for a release
router.post('/:releaseId/code-quality', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { teamId, metrics, forceTimestamp = false } = req.body;

    console.log(`📦 Storing Code Quality metrics for release: ${releaseId}${forceTimestamp ? ' (forcing timestamp update)' : ''}`);

    if (!teamId || !metrics) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Both teamId and metrics are required'
      });
    }

    // Transform SonarQube API response to flat format if needed
    let flatMetrics = metrics;
    if (metrics.component && metrics.component.measures) {
      // Convert SonarQube measures array to flat object
      flatMetrics = {};
      metrics.component.measures.forEach((measure: any) => {
        if (measure.period && measure.period.value !== undefined) {
          // New code metrics have period.value
          flatMetrics[measure.metric] = measure.period.value;
        } else if (measure.value !== undefined) {
          // Regular metrics have direct value
          flatMetrics[measure.metric] = measure.value;
        }
      });
      console.log('📊 Converted SonarQube API response to flat metrics:', Object.keys(flatMetrics));
    }

    // Process and store SonarQube metrics
    const sonarQubeMetrics = {
      score: ConfidenceScoreService.calculateSonarQubeScore(flatMetrics),
      label: 'Code Quality Metrics',
      lastUpdated: new Date().toISOString(),
      status: determineSonarQubeStatus(flatMetrics),
      bugs: parseInt(flatMetrics.bugs || '0'),
      vulnerabilities: parseInt(flatMetrics.vulnerabilities || '0'),
      codeSmells: parseInt(flatMetrics.code_smells || '0'),
      coverage: parseFloat(flatMetrics.coverage || '0'),
      maintainabilityRating: mapRatingToLetter(flatMetrics.sqale_rating),
      reliabilityRating: mapRatingToLetter(flatMetrics.reliability_rating),
      securityRating: mapRatingToLetter(flatMetrics.security_rating),
      rawData: flatMetrics, // Store the complete raw metrics for UI consumption
      details: {
        linesOfCode: parseInt(flatMetrics.ncloc || '0'),
        complexity: parseInt(flatMetrics.complexity || '0'),
        duplicatedLines: parseFloat(flatMetrics.duplicated_lines_density || '0'),
        unitTests: parseInt(flatMetrics.tests || '0'),
        // New code metrics
        newBugs: parseInt(flatMetrics.new_bugs || '0'),
        newVulnerabilities: parseInt(flatMetrics.new_vulnerabilities || '0'),
        newCodeSmells: parseInt(flatMetrics.new_code_smells || '0'),
        newCoverage: parseFloat(flatMetrics.new_coverage || '0'),
        newLines: parseInt(flatMetrics.new_lines || '0'),
        newDuplicatedLines: parseFloat(flatMetrics.new_duplicated_lines_density || '0'),
        newMaintainabilityRating: mapRatingToLetter(flatMetrics.new_maintainability_rating),
        newReliabilityRating: mapRatingToLetter(flatMetrics.new_reliability_rating),
        newSecurityRating: mapRatingToLetter(flatMetrics.new_security_rating)
      }
    };

    // Store metrics using CosmosService
    const result = await cosmosService.storeReleaseMetrics(releaseId, teamId, 'codeQuality', sonarQubeMetrics, forceTimestamp);

    // Update confidence score
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      success: true,
      releaseId,
      metrics: sonarQubeMetrics,
      storedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error storing Code Quality metrics:', error.message);
    res.status(500).json({
      error: 'Failed to store Code Quality metrics',
      message: error.message
    });
  }
});

// Store Test Coverage metrics for a release
router.post('/:releaseId/test-coverage', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { teamId, metrics, forceTimestamp = false } = req.body;

    console.log(`📦 Storing Test Coverage metrics for release: ${releaseId}${forceTimestamp ? ' (forcing timestamp update)' : ''}`);

    if (!teamId || !metrics) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Both teamId and metrics are required'
      });
    }

    // Transform SonarQube API response to flat format if needed
    let flatMetrics = metrics;
    if (metrics.component && metrics.component.measures) {
      // Convert SonarQube measures array to flat object
      flatMetrics = {};
      metrics.component.measures.forEach((measure: any) => {
        if (measure.period && measure.period.value !== undefined) {
          // New code metrics have period.value
          flatMetrics[measure.metric] = measure.period.value;
        } else if (measure.value !== undefined) {
          // Regular metrics have direct value
          flatMetrics[measure.metric] = measure.value;
        }
      });
      console.log('📊 Converted SonarQube coverage response to flat metrics:', Object.keys(flatMetrics));
    }

    // Process and store Test Coverage metrics
    const coverageMetrics = {
      score: ConfidenceScoreService.calculateCoverageScore(flatMetrics),
      label: 'Test Coverage Metrics',
      lastUpdated: new Date().toISOString(),
      status: determineCoverageStatus(flatMetrics),
      coverage: parseFloat(flatMetrics.coverage || '0'),
      linesToCover: parseInt(flatMetrics.lines_to_cover || '0'),
      uncoveredLines: parseInt(flatMetrics.uncovered_lines || '0'),
      lineCoverage: parseFloat(flatMetrics.line_coverage || '0'),
      conditionsToCover: parseInt(flatMetrics.conditions_to_cover || '0'),
      uncoveredConditions: parseInt(flatMetrics.uncovered_conditions || '0'),
      tests: parseInt(flatMetrics.tests || '0'),
      testErrors: parseInt(flatMetrics.test_errors || '0'),
      testFailures: parseInt(flatMetrics.test_failures || '0'),
      skippedTests: parseInt(flatMetrics.skipped_tests || '0'),
      testSuccessDensity: parseFloat(flatMetrics.test_success_density || '100'),
      testExecutionTime: parseInt(flatMetrics.test_execution_time || '0'),
      rawData: flatMetrics, // Store the complete raw metrics for UI consumption
      details: {
        // New code coverage metrics
        newCoverage: parseFloat(flatMetrics.new_coverage || '0'),
        newLinesToCover: parseInt(flatMetrics.new_lines_to_cover || '0'),
        newUncoveredLines: parseInt(flatMetrics.new_uncovered_lines || '0'),
        newLineCoverage: parseFloat(flatMetrics.new_line_coverage || '0'),
        newConditionsToCover: parseInt(flatMetrics.new_conditions_to_cover || '0'),
        newUncoveredConditions: parseInt(flatMetrics.new_uncovered_conditions || '0')
      }
    };

    // Store metrics using CosmosService
    const result = await cosmosService.storeReleaseMetrics(releaseId, teamId, 'codeCoverage', coverageMetrics, forceTimestamp);

    // Update confidence score
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      success: true,
      releaseId,
      metrics: coverageMetrics,
      storedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error storing Test Coverage metrics:', error.message);
    res.status(500).json({
      error: 'Failed to store Test Coverage metrics',
      message: error.message
    });
  }
});

// Store Test Results metrics for a release
router.post('/:releaseId/test-results', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { teamId, metrics, forceTimestamp = false } = req.body;

    console.log(`📦 Storing Test Results metrics for release: ${releaseId}${forceTimestamp ? ' (forcing timestamp update)' : ''}`);

    if (!teamId || !metrics) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Both teamId and metrics are required'
      });
    }

    // Transform SonarQube API response to flat format if needed
    let flatMetrics = metrics;
    if (metrics.component && metrics.component.measures) {
      // Convert SonarQube measures array to flat object
      flatMetrics = {};
      metrics.component.measures.forEach((measure: any) => {
        if (measure.period && measure.period.value !== undefined) {
          // New code metrics have period.value
          flatMetrics[measure.metric] = measure.period.value;
        } else if (measure.value !== undefined) {
          // Regular metrics have direct value
          flatMetrics[measure.metric] = measure.value;
        }
      });
      console.log('📊 Converted SonarQube test results response to flat metrics:', Object.keys(flatMetrics));
    }

    // Process and store Test Results metrics
    const testResultsMetrics = {
      score: ConfidenceScoreService.calculateTestResultsScore(flatMetrics),
      label: 'Test Results Metrics',
      lastUpdated: new Date().toISOString(),
      status: determineTestResultsStatus(flatMetrics),
      totalTests: parseInt(flatMetrics.tests || '0'),
      testErrors: parseInt(flatMetrics.test_errors || '0'),
      testFailures: parseInt(flatMetrics.test_failures || '0'),
      skippedTests: parseInt(flatMetrics.skipped_tests || '0'),
      testSuccessDensity: parseFloat(flatMetrics.test_success_density || '100'),
      testExecutionTime: parseInt(flatMetrics.test_execution_time || '0'),
      rawData: flatMetrics, // Store the complete raw metrics for UI consumption
      details: {
        passedTests: parseInt(flatMetrics.tests || '0') - parseInt(flatMetrics.test_errors || '0') - parseInt(flatMetrics.test_failures || '0'),
        avgExecutionTime: Math.round((parseInt(flatMetrics.test_execution_time || '0') / Math.max(parseInt(flatMetrics.tests || '1'), 1)) * 100) / 100,
        testHealth: 'good' // This could be calculated based on trends
      }
    };

    // Store metrics using CosmosService
    const result = await cosmosService.storeReleaseMetrics(releaseId, teamId, 'testResults', testResultsMetrics, forceTimestamp);

    // Update confidence score
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      success: true,
      releaseId,
      metrics: testResultsMetrics,
      storedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error storing Test Results metrics:', error.message);
    res.status(500).json({
      error: 'Failed to store Test Results metrics',
      message: error.message
    });
  }
});

// GET /metrics/:releaseId/stored - Get stored metrics for a release
router.get('/:releaseId/stored', async (req, res) => {
  try {
    const { releaseId } = req.params;
    
    // Check if the release exists
    const release = await cosmosService.getRelease(releaseId);
    if (!release) {
      return res.status(404).json({
        success: false,
        message: 'Release not found'
      });
    }

    // Get stored metrics for this release
    const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
    
    // Include PCF requests from the release document
    const pcfRequests = (release as any).pcfRequests || [];
    
    // Add PCF requests to the releaseMetrics object if it exists
    let responseMetrics = releaseMetrics?.releaseMetrics || {};
    if (pcfRequests.length > 0) {
      responseMetrics = {
        ...responseMetrics,
        pcfRequests
      };
    }
    
    res.json({
      success: true,
      data: {
        releaseId,
        releaseMetrics: responseMetrics,
        confidenceMetrics: releaseMetrics?.confidenceMetrics || null,
        hasMetrics: releaseMetrics?.releaseMetrics && Object.keys(releaseMetrics.releaseMetrics).length > 0,
        hasConfidenceScore: releaseMetrics?.confidenceMetrics?.overallScore !== undefined
      }
    });
  } catch (error) {
    console.error('Error checking stored metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check stored metrics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper functions for metrics processing - using centralized service for calculations

function determineSonarQubeStatus(metrics: any): 'success' | 'warning' | 'error' {
  const bugs = parseInt(metrics.bugs || '0');
  const vulnerabilities = parseInt(metrics.vulnerabilities || '0');
  const coverage = parseFloat(metrics.coverage || '0');
  
  if (vulnerabilities > 0 || bugs > 50) return 'error';
  if (bugs > 10 || coverage < 60) return 'warning';
  return 'success';
}

function determineCoverageStatus(metrics: any): 'success' | 'warning' | 'error' {
  const coverage = parseFloat(metrics.coverage || '0');
  
  if (coverage >= 80) return 'success';
  if (coverage >= 60) return 'warning';
  return 'error';
}

function determineTestResultsStatus(metrics: any): 'success' | 'warning' | 'error' {
  const successDensity = parseFloat(metrics.test_success_density || '100');
  const errors = parseInt(metrics.test_errors || '0');
  const failures = parseInt(metrics.test_failures || '0');
  
  if (errors > 0 || failures > 0) return 'error';
  if (successDensity < 95) return 'warning';
  return 'success';
}

function mapRatingToLetter(rating: string): 'A' | 'B' | 'C' | 'D' | 'E' {
  const ratingMap: { [key: string]: 'A' | 'B' | 'C' | 'D' | 'E' } = {
    '1.0': 'A', '2.0': 'B', '3.0': 'C', '4.0': 'D', '5.0': 'E'
  };
  return ratingMap[rating] || 'C';
}

// POST /metrics/:releaseId/recalculate - Recalculate confidence score with new weightings
router.post('/:releaseId/recalculate', async (req, res) => {
  try {
    const { releaseId } = req.params;

    console.log(`🔄 Recalculating confidence score for release: ${releaseId}`);

    // Get stored metrics
    const releaseMetrics = await cosmosService.getReleaseMetrics(releaseId);
    
    if (!releaseMetrics || !releaseMetrics.releaseMetrics) {
      return res.status(404).json({ 
        error: 'No stored metrics found',
        message: `No metrics data found for release: ${releaseId}`
      });
    }

    console.log('📊 Found stored metrics:', {
      codeQuality: releaseMetrics.releaseMetrics.codeQuality?.score,
      codeCoverage: releaseMetrics.releaseMetrics.codeCoverage?.score,
      testResults: releaseMetrics.releaseMetrics.testResults?.score
    });

    // Calculate confidence score with new weightings
    const newWeightings = { 
      codeQuality: 20, 
      codeCoverage: 20, 
      testResults: 20, 
      buildStability: 20, 
      changeRequests: 10, 
      changeLog: 10 
    };
    const overallScore = ConfidenceScoreService.calculateConfidenceScore(releaseMetrics.releaseMetrics, newWeightings);
    
    console.log(`🎯 Calculated new confidence score: ${overallScore}%`);

    // Update the confidence metrics in the release
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      message: 'Confidence score recalculated successfully',
      releaseId,
      newScore: overallScore,
      oldWeightings: releaseMetrics.confidenceMetrics?.weightings,
      newWeightings,
      recalculatedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error recalculating confidence score:', error.message);
    res.status(500).json({
      error: 'Failed to recalculate confidence score',
      message: error.message
    });
  }
});

// POST /metrics/recalculate-all - Recalculate confidence scores for all releases
router.post('/recalculate-all', async (req, res) => {
  try {
    console.log('🔄 Starting bulk recalculation of all release confidence scores...');

    const cosmosClient = new CosmosClient(process.env.COSMOS_CONNECTION_STRING!);
    const database = cosmosClient.database(process.env.COSMOS_DATABASE_NAME!);
    const container = database.container('releases');

    // Get all releases that have releaseMetrics
    const { resources: releases } = await container.items
      .query({
        query: 'SELECT * FROM c WHERE IS_DEFINED(c.releaseMetrics)'
      })
      .fetchAll();

    console.log(`📊 Found ${releases.length} releases with metrics to recalculate`);

    const results = [];
    const newWeightings = { 
      codeQuality: 20, 
      codeCoverage: 20, 
      testResults: 20, 
      buildStability: 20, 
      changeRequests: 10, 
      changeLog: 10 
    };

    for (const release of releases) {
      try {
        if (release.releaseMetrics) {
          const oldScore = release.confidenceMetrics?.overallScore || 0;
          const newScore = ConfidenceScoreService.calculateConfidenceScore(release.releaseMetrics, newWeightings);
          
          // Update the confidence metrics
          const updatedRelease = {
            ...release,
            confidenceMetrics: {
              overallScore: newScore,
              lastCalculated: new Date().toISOString(),
              weightings: newWeightings,
              trending: {
                direction: newScore > oldScore + 2 ? 'up' : newScore < oldScore - 2 ? 'down' : 'stable',
                changePercent: oldScore > 0 ? Math.abs(Math.round(((newScore - oldScore) / oldScore) * 100)) : 0,
                previousScore: oldScore
              }
            },
            updatedAt: new Date().toISOString()
          };

          await container.item(release.id, release.id).replace(updatedRelease);
          
          results.push({
            releaseId: release.id,
            releaseName: release.releaseName || release.title,
            oldScore,
            newScore,
            change: newScore - oldScore
          });

          console.log(`✅ Updated ${release.releaseName || release.id}: ${oldScore}% → ${newScore}%`);
        }
      } catch (error) {
        console.error(`❌ Failed to update release ${release.id}:`, error);
        results.push({
          releaseId: release.id,
          releaseName: release.releaseName || release.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    res.json({
      message: 'Bulk recalculation completed',
      totalReleases: releases.length,
      successfulUpdates: results.filter(r => !r.error).length,
      failedUpdates: results.filter(r => r.error).length,
      results,
      newWeightings,
      processedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error in bulk recalculation:', error.message);
    res.status(500).json({
      error: 'Failed to perform bulk recalculation',
      message: error.message
    });
  }
});

// POST /metrics/:releaseId/changelog - Store changelog metrics (GitHub PRs)
router.post('/:releaseId/changelog', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { teamId, pullRequests, releaseData, forceTimestamp = false } = req.body;

    if (!teamId || !Array.isArray(pullRequests) || !releaseData) {
      return res.status(400).json({
        error: 'Missing required fields: teamId, pullRequests, releaseData'
      });
    }

    console.log(`📦 Storing Changelog metrics for release: ${releaseId}${forceTimestamp ? ' (forcing timestamp update)' : ''}`);

    // Process and store changelog metrics
    const changelogMetrics = {
      score: ConfidenceScoreService.calculateChangelogScore(pullRequests),
      label: 'Changelog Metrics',
      lastUpdated: new Date().toISOString(),
      status: determineChangelogStatus(pullRequests),
      totalPRs: pullRequests.length,
      mergedPRs: pullRequests.filter(pr => pr.status === 'merged').length,
      closedPRs: pullRequests.filter(pr => pr.status === 'closed').length,
      openPRs: pullRequests.filter(pr => pr.status === 'open').length,
      branch: releaseData.releaseBranch || 'main',
      dateRange: {
        start: releaseData.createdAt,
        end: releaseData.releaseRolloutDate
      },
      rawData: pullRequests,
      details: {
        avgPRsPerDay: calculateAvgPRsPerDay(pullRequests, releaseData),
        contributors: [...new Set(pullRequests.map(pr => pr.author))].length,
        latestPR: pullRequests.length > 0 ? pullRequests.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )[0] : null
      }
    };

    // Store metrics using CosmosService
    const result = await cosmosService.storeReleaseMetrics(releaseId, teamId, 'changeLog', changelogMetrics, forceTimestamp);

    // Update confidence score
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      success: true,
      releaseId,
      metrics: changelogMetrics,
      storedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error storing Changelog metrics:', error.message);
    res.status(500).json({
      error: 'Failed to store Changelog metrics',
      message: error.message
    });
  }
});

// Helper functions for changelog metrics
function determineChangelogStatus(pullRequests: any[]): 'excellent' | 'good' | 'needs-attention' | 'critical' {
  const score = ConfidenceScoreService.calculateChangelogScore(pullRequests);
  
  if (score >= 80) return 'excellent';
  if (score >= 60) return 'good';
  if (score >= 40) return 'needs-attention';
  return 'critical';
}

function calculateAvgPRsPerDay(pullRequests: any[], releaseData: any): number {
  if (!pullRequests || pullRequests.length === 0) return 0;
  
  const startDate = new Date(releaseData.createdAt);
  const endDate = new Date(releaseData.releaseRolloutDate || new Date());
  const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
  
  return Math.round((pullRequests.length / daysDiff) * 100) / 100;
}

// Helper functions for change requests metrics
function determineChangeRequestsStatus(changeRequests: any[]): 'excellent' | 'good' | 'needs-attention' | 'critical' {
  const score = ConfidenceScoreService.calculateChangeRequestsScore(changeRequests);
  
  if (score >= 80) return 'excellent';
  if (score >= 60) return 'good';
  if (score >= 40) return 'needs-attention';
  return 'critical';
}

function calculateAvgResolveTime(changeRequests: any[]): number {
  const resolvedRequests = changeRequests.filter(cr => 
    (cr.state === 'Resolved' || cr.state === 'Closed') && cr.resolved_at
  );
  
  if (resolvedRequests.length === 0) return 0;
  
  const totalResolveTime = resolvedRequests.reduce((sum, cr) => {
    const openedAt = new Date(cr.opened_at);
    const resolvedAt = new Date(cr.resolved_at);
    const resolveTimeHours = (resolvedAt.getTime() - openedAt.getTime()) / (1000 * 60 * 60);
    return sum + resolveTimeHours;
  }, 0);
  
  return Math.round((totalResolveTime / resolvedRequests.length) * 100) / 100;
}

// Helper function to parse build stability data using centralized service
function parseBuildStabilityData(buildData: any): any {
  if (!buildData || !buildData.statistics) {
    return {
      score: 50,
      statistics: {
        total: 0,
        success: 0,
        failure: 0,
        aborted: 0,
        unstable: 0,
        successRate: 0,
        failureRate: 0,
        avgBuildTime: 0
      }
    };
  }

  const score = ConfidenceScoreService.calculateBuildStabilityScore(buildData);
  
  return {
    score,
    statistics: buildData.statistics,
    teamId: buildData.teamId,
    branch: buildData.branch,
    builds: buildData.builds ? buildData.builds.slice(0, 20) : [] // Store first 20 builds for details
  };
}

// POST /api/metrics/:releaseId/build-stability - Store build stability metrics
router.post('/:releaseId/build-stability', async (req: Request, res: Response) => {
  try {
    const { releaseId } = req.params;
    const { teamId, buildData, releaseData, forceTimestamp = false } = req.body;

    if (!buildData) {
      return res.status(400).json({
        success: false,
        error: 'Missing build stability data'
      });
    }

    console.log(`📦 Storing Build Stability metrics for release: ${releaseId}${forceTimestamp ? ' (forcing timestamp update)' : ''}`);

    const processedData = parseBuildStabilityData(buildData);

    const metricsData = {
      score: processedData.score,
      rawData: buildData,
      processedData,
      teamId,
      releaseData: releaseData || {},
      lastUpdated: new Date().toISOString()
    };

    await cosmosService.storeReleaseMetrics(releaseId, teamId, 'buildStability', metricsData, forceTimestamp);

    console.log(`✅ Successfully stored Build Stability metrics in database`);

    res.json({
      success: true,
      message: 'Build Stability metrics stored successfully',
      data: {
        releaseId,
        score: processedData.score,
        statistics: processedData.statistics
      }
    });

  } catch (error: any) {
    console.error('❌ Failed to store Build Stability metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to store Build Stability metrics',
      message: error.message
    });
  }
});

// POST /metrics/:releaseId/change-requests - Store change requests metrics
router.post('/:releaseId/change-requests', async (req, res) => {
  try {
    const { releaseId } = req.params;
    const { teamId, changeRequests, forceTimestamp = false } = req.body;

    if (!teamId || !Array.isArray(changeRequests)) {
      return res.status(400).json({
        error: 'Missing required fields: teamId, changeRequests'
      });
    }

    console.log(`📦 Storing Change Requests metrics for release: ${releaseId}${forceTimestamp ? ' (forcing timestamp update)' : ''}`);

    // Get release data from database
    const releaseData = await cosmosService.getReleaseMetrics(releaseId);

    // Process and store change requests metrics
    const changeRequestsMetrics = {
      score: ConfidenceScoreService.calculateChangeRequestsScore(changeRequests),
      label: 'Change Requests Metrics',
      lastUpdated: new Date().toISOString(),
      status: determineChangeRequestsStatus(changeRequests),
      totalRequests: changeRequests.length,
      openRequests: changeRequests.filter(cr => cr.state === 'Open' || cr.state === 'New').length,
      resolvedRequests: changeRequests.filter(cr => cr.state === 'Resolved' || cr.state === 'Closed').length,
      inProgressRequests: changeRequests.filter(cr => cr.state === 'In Progress' || cr.state === 'Work in Progress').length,
      branch: releaseData?.releaseBranch || 'main',
      dateRange: {
        start: releaseData?.createdAt,
        end: releaseData?.releaseRolloutDate
      },
      rawData: changeRequests,
      details: {
        avgResolveTime: calculateAvgResolveTime(changeRequests),
        highPriorityRequests: changeRequests.filter(cr =>
          cr.priority === 'High' || cr.priority === 'Critical' || cr.priority === '1 - Critical'
        ).length,
        latestRequest: changeRequests.length > 0 ? changeRequests.sort((a, b) =>
          new Date(b.opened_at).getTime() - new Date(a.opened_at).getTime()
        )[0] : null
      }
    };

    // Store metrics using CosmosService
    const result = await cosmosService.storeReleaseMetrics(releaseId, teamId, 'changeRequests', changeRequestsMetrics, forceTimestamp);

    // Update confidence score
    await cosmosService.updateConfidenceScore(releaseId);

    res.json({
      success: true,
      releaseId,
      metrics: changeRequestsMetrics,
      storedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Error storing Change Requests metrics:', error.message);
    res.status(500).json({
      error: 'Failed to store Change Requests metrics',
      message: error.message
    });
}
});

export default router;