import { Router, Request, Response } from 'express';
import { body, param, validationResult } from 'express-validator';
import cosmosService from '../services/cosmosService';
import { Team } from '../types/database';
import { logger } from '../utils/logger';

const router = Router();

// Validation middleware
const handleValidationErrors = (req: Request, res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/teams - Get all teams with aggressive timeout protection
router.get('/', async (req: Request, res: Response) => {
  try {
    logger.info('🚀 Teams API called - starting with aggressive timeout protection');

    // Set a very short 2-second timeout for the API call
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Teams API timeout after 2 seconds')), 2000);
    });

    const teamsPromise = cosmosService.getAllTeams();

    const teams = await Promise.race([teamsPromise, timeoutPromise]);

    logger.info('✅ Teams API completed successfully', {
      teamCount: Array.isArray(teams) ? teams.length : 'unknown',
    });
    res.json({ data: teams });
  } catch (error) {
    logger.error('💥 Error fetching teams:', error);

    // Always return empty array to prevent UI crashes - never return errors
    logger.warn('⏰ Teams API failed, returning empty array for graceful degradation');
    res.json({
      data: [],
      warning: 'Teams data temporarily unavailable',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/teams/:id - Get specific team
router.get(
  '/:id',
  param('id').isUUID().withMessage('Invalid team ID'),
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const team = await cosmosService.getTeam(id);

      if (!team) {
        return res.status(404).json({ error: 'Team not found' });
      }

      res.json({ data: team });
    } catch (error) {
      logger.error('Error fetching team:', error);
      res.status(500).json({
        error: 'Failed to fetch team',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// GET /api/teams/:id/config - Get team configuration for authorization
router.get(
  '/:id/config',
  param('id').isUUID().withMessage('Invalid team ID'),
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const team = await cosmosService.getTeam(id);

      if (!team) {
        return res.status(404).json({ error: 'Team configuration not found' });
      }

      // Return the team configuration with roles and release types
      // This should match the structure expected by the frontend
      const config = {
        id: team.id,
        teamName: team.teamName,
        metadata: team.metadata || {},
        roles: team.roles || {},
        release_types: team.release_types || {},
      };

      res.json(config);
    } catch (error) {
      logger.error('Error fetching team config:', error);
      res.status(500).json({
        error: 'Failed to fetch team configuration',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

// POST /api/teams - Create new team
router.post(
  '/',
  [
    body('name').notEmpty().withMessage('Team name is required'),
    body('members').isArray({ min: 1 }).withMessage('At least one team member is required'),
    body('members.*').isEmail().withMessage('All team members must have valid email addresses'),
    body('lead').isEmail().withMessage('Team lead must have a valid email address'),
    body('stage')
      .isIn(['functional_qa', 'e2e_qa', 'engineering_leadership'])
      .withMessage('Invalid stage'),
    body('subTeams').optional().isArray().withMessage('Sub teams must be an array'),
    body('subTeams.*')
      .optional()
      .isIn(['cart', 'checkout', 'plp', 'pdp', 'sng'])
      .withMessage('Invalid sub team'),
  ],
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const teamData: Omit<Team, 'id'> = req.body;
      const team = await cosmosService.createTeam(teamData);

      logger.info('Team created:', { teamId: team.id, name: team.teamName });
      res.status(201).json({ data: team });
    } catch (error) {
      logger.error('Error creating team:', error);
      res.status(500).json({
        error: 'Failed to create team',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
);

export { router as teamsRoutes };
