import express from 'express';
import { githubService } from '../services/githubService';

const router = express.Router();

// Get repository info
router.get('/repos/:org/:repo', async (req, res, next) => {
  try {
    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const repo = await githubService.getRepository(fullRepoPath);
    res.json(repo);
  } catch (error) {
    next(error);
  }
});

// Get repository branches
router.get('/repos/:org/:repo/branches', async (req, res, next) => {
  try {
    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const branches = await githubService.getBranches(fullRepoPath);
    res.json(branches);
  } catch (error) {
    next(error);
  }
});

// Get pull requests with filters
router.get('/repos/:org/:repo/pulls', async (req, res, next) => {
  try {
    console.log('[GitHub Route] Received PR request:', {
      owner: req.params.org,
      repo: req.params.repo,
      query: req.query,
      base: req.query.base,
      since: req.query.since,
      until: req.query.until
    });

    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const pulls = await githubService.getPullRequestsByDateRange(
      fullRepoPath,
      {
        startDate: req.query.since as string,
        endDate: req.query.until as string,
        state: (req.query.state as 'open' | 'closed' | 'all') || 'all',
        base: req.query.base as string,
        per_page: req.query.per_page ? parseInt(req.query.per_page as string) : undefined
      }
    );

    console.log('[GitHub Route] PR request response:', {
      pullRequestCount: pulls.length,
      firstPR: pulls[0]
    });

    res.json(pulls);
  } catch (error) {
    console.error('[GitHub Route] Error in PR route:', error);
    next(error);
  }
});

// Get PRs by date range (alternative endpoint)
router.get('/repos/:org/:repo/pulls/date-range', async (req, res, next) => {
  try {
    const { 
      startDate,    // e.g. "2024-01-01"
      endDate,      // e.g. "2024-03-31"
      state,        // "open", "closed", "all"
      base,         // branch name e.g. "develop"
      per_page     // number of results per page
    } = req.query;

    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const pulls = await githubService.getPullRequestsByDateRange(
      fullRepoPath,
      { 
        startDate: startDate as string, 
        endDate: endDate as string, 
        state: state as 'open' | 'closed' | 'all', 
        base: base as string, 
        per_page: per_page ? parseInt(per_page as string) : undefined
      }
    );
    
    res.json(pulls);
  } catch (error) {
    next(error);
  }
});

// Get branch details
router.get('/repos/:org/:repo/branches/:branchName', async (req, res, next) => {
  try {
    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const branch = await githubService.getBranchDetails(
      fullRepoPath,
      req.params.branchName
    );
    res.json(branch);
  } catch (error) {
    next(error);
  }
});

// Get commits in date range
router.get('/repos/:org/:repo/commits', async (req, res, next) => {
  try {
    const { startDate, endDate, branch } = req.query;
    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const commits = await githubService.getCommitsByDateRange(
      fullRepoPath,
      { 
        startDate: startDate as string, 
        endDate: endDate as string, 
        branch: branch as string 
      }
    );
    res.json(commits);
  } catch (error) {
    next(error);
  }
});

// Compare branches
router.get('/repos/:org/:repo/compare/:baseBranch/:compareBranch', async (req, res, next) => {
  try {
    const fullRepoPath = `${req.params.org}/${req.params.repo}`;
    const comparison = await githubService.compareBranches(
      fullRepoPath,
      req.params.baseBranch,
      req.params.compareBranch
    );
    res.json(comparison);
  } catch (error) {
    next(error);
  }
});

// Get specific user info
router.get('/users/:username', async (req, res, next) => {
  try {
    const userInfo = await githubService.getUserInfo(req.params.username);
    res.json(userInfo);
  } catch (error) {
    next(error);
  }
});

export default router; 