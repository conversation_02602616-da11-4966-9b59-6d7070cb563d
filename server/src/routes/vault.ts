import express from 'express';
import type { Request, Response, NextFunction } from 'express';
import keyVaultClient from '../utils/keyVaultClient';

const router = express.Router();

// Get a secret
router.get('/secret/:name', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log(`Attempting to fetch secret: ${req.params.name}`);
    const value = await keyVaultClient.getSecret(req.params.name);
    console.log('Secret retrieved successfully');
    res.json({ name: req.params.name, value });
  } catch (error) {
    console.error('Error in vault route:', error);
    next(error);
  }
});

// Set a secret
router.post('/secret', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, value } = req.body;
    if (!name || !value) {
      return res.status(400).json({ error: 'Name and value are required' });
    }
    
    await keyVaultClient.setSecret(name, value);
    res.status(201).json({ message: 'Secret stored successfully' });
  } catch (error) {
    next(error);
  }
});

// Delete a secret
router.delete('/secret/:name', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log(`Attempting to delete secret: ${req.params.name}`);
    await keyVaultClient.deleteSecret(req.params.name);
    console.log('Secret deleted successfully');
    res.json({ message: 'Secret deleted successfully' });
  } catch (error) {
    console.error('Error deleting secret:', error);
    next(error);
  }
});

// List secrets
router.get('/secrets', async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('Attempting to list secrets');
    const secrets = await keyVaultClient.listSecrets();
    console.log('Secrets listed successfully');
    res.json({ secrets });
  } catch (error) {
    console.error('Error listing secrets:', error);
    next(error);
  }
});

export default router; 