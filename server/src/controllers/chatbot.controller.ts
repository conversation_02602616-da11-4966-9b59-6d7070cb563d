import { Request, Response } from 'express';
import { ChatbotService, ChatMessage } from '../services/chatbot.service';

export class ChatbotController {
  constructor(private chatbotService: ChatbotService) {}

  public async handleMessage(req: Request, res: Response): Promise<void> {
    try {
      const { releaseId } = req.params;
      const { message, conversationHistory } = req.body;

      if (!releaseId || !message) {
        res.status(400).json({
          error: 'Missing required parameters: releaseId and message are required'
        });
        return;
      }

      const response = await this.chatbotService.processMessage(
        releaseId,
        message,
        conversationHistory
      );

      res.status(200).json(response);
    } catch (error) {
      console.error('Error in chat message handler:', error);

      // Determine error type and provide appropriate response
      let errorMessage = 'Failed to process chat message';
      let statusCode = 500;

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.name === 'APIConnectionTimeoutError') {
          errorMessage = 'Request timed out. Please try again with a shorter message or try again later.';
          statusCode = 408;
        } else if (error.message.includes('API key') || error.message.includes('authentication')) {
          errorMessage = 'Authentication error. Please contact support.';
          statusCode = 401;
        } else if (error.message.includes('Rate limit') || error.message.includes('quota')) {
          errorMessage = 'Service is currently busy. Please try again in a few moments.';
          statusCode = 429;
        } else if (error.message.includes('Release not found')) {
          errorMessage = 'Release information not found. Please check the release ID.';
          statusCode = 404;
        } else if (error.message.includes('network') || error.message.includes('connection')) {
          errorMessage = 'Network error. Please check your connection and try again.';
          statusCode = 503;
        }
      }

      res.status(statusCode).json({
        error: errorMessage,
        type: error instanceof Error ? error.name : 'UnknownError'
      });
    }
  }
} 