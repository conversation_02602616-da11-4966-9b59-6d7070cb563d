import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import path from 'path';
import session from 'express-session';
import { config, isProduction } from './config';
import { logger } from './utils/logger';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { teamsRoutes } from './routes/teams';
import { releasesRoutes } from './routes/releases';
import { authRoutes } from './routes/auth';
import signoffsRouter from './routes/signoffs';
import pcfRequestsRouter from './routes/pcfRequests';
import vaultRouter from './routes/vault';
import sonarqubeRouter from './routes/sonarqube';
import githubRouter from './routes/github';
import servicenowRouter from './routes/servicenow';
import looperRouter from './routes/looper';
import cosmosService from './services/cosmosService';
import { authManager } from './auth/authManager';
import passport from 'passport';
import metricsRouter from './routes/metrics';
import { loadSonarQubeConfig } from './config/sonarqube';
import { loadGitHubConfig } from './config/github';
import { loadLooperConfig } from './config/looper';
import { createChatbotRouter } from './routes/chatbot.routes';
import { ChatbotController } from './controllers/chatbot.controller';
import { ChatbotService } from './services/chatbot.service';
import { ReleaseService } from './services/release.service';
import { SignoffService } from './services/signoff.service';
import { createAzureOpenAIService } from './services/azureOpenAI.service';
import { MetricsService } from './services/metrics.service';

const app = express();

// Server reference for graceful shutdown
let server: any;

// Function to start server with graceful shutdown support
const startServer = () => {
  server = app.listen(config.port, () => {
    logger.info(`🚀 Server running on port ${config.port}`, {
      environment: config.nodeEnv,
      port: config.port,
      cosmosEndpoint: config.cosmosdb.endpoint ? 'Connected' : 'Not configured',
      authProvider: authManager.getCurrentProvider(),
      githubEnabled: authManager.isGitHubEnabled(),
      azureEnabled: authManager.isAzureEnabled(),
    });
  });
};

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        connectSrc: [
          "'self'",
          'https://engineering-excellence.engineering-excellence.useast-dev-az-002.cluster.k8s.us.walmart.net',
          'https://signoff-release.documents.azure.com',
          'https://release-portal-vault.vault.azure.net',
          'https://api.github.com',
          'https://github.com',
          'https://wmtllmgateway.stage.walmart.com',
          'https://signal.walmartlabs.com',
          'https://signal.dev.walmartlabs.com',
        ],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        scriptSrcAttr: ["'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        styleSrcElem: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        fontSrc: ["'self'", 'https://fonts.gstatic.com', 'https://fonts.googleapis.com', 'data:'],
        imgSrc: ["'self'", 'data:', 'https:'],
        formAction: ["'self'", 'https://pfeddev.wal-mart.com'],
      },
    },
  })
);
app.use(cors(config.cors));

// Compression middleware
app.use(compression());

// Logging middleware
if (config.nodeEnv === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session middleware (required for passport)
// Create a custom session store for production that handles cleanup
class ProductionMemoryStore extends session.MemoryStore {
  constructor() {
    super();
    // Set up periodic cleanup to prevent memory leaks
    if (isProduction) {
      setInterval(() => {
        this.all((err, sessions) => {
          if (err) return;
          const now = Date.now();
          Object.keys(sessions || {}).forEach((sid) => {
            const sess = sessions![sid];
            if (sess && sess.cookie && sess.cookie.expires) {
              if (new Date(sess.cookie.expires).getTime() < now) {
                this.destroy(sid, () => {});
              }
            }
          });
        });
      }, 15 * 60 * 1000); // Clean up every 15 minutes
    }
  }
}

const sessionConfig = {
  ...config.session,
  // Use our custom store for production
  store: isProduction ? new ProductionMemoryStore() : undefined,
  rolling: true, // Reset expiration on activity
  unset: 'destroy' as const, // Destroy session when unset
};

app.use(session(sessionConfig));

// Initialize Auth Manager and Passport
authManager.initialize();
app.use(passport.initialize());
app.use(passport.session());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: config.nodeEnv,
    version: process.env.npm_package_version || '1.0.0',
  });
});

interface Approval {
  stage: string;
  status: string;
  approverEmail?: string;
  approvedAt?: string;
  notes?: string;
}

interface SignoffStage {
  name: string;
  status: 'pending' | 'in_progress' | 'completed';
  required: number;
  completed: number;
  progress: number;
  isCurrentStage: boolean;
}

// Initialize services for chatbot (sync, minimal for route registration)
const releaseService: ReleaseService = {
  getReleaseById: async (id: string) => {
    try {
      const release = await cosmosService.getRelease(id);
      if (!release) {
        throw new Error('Release not found');
      }
      return {
        id: release.id,
        releaseName: release.releaseName,
        releaseType: release.releaseType,
        teamId: release.teamId,
        teamName: release.teamName,
        releaseRolloutDate: release.releaseRolloutDate,
        status: 'in-progress',
        createdAt: new Date(release.createdAt),
        updatedAt: new Date(release.updatedAt),
        supportedPlatforms: Object.keys(release.supportedPlatforms || {}),
        description: release.description || '',
      };
    } catch (error) {
      console.error('Error fetching release:', error);
      throw new Error('Failed to fetch release data');
    }
  },
};

const signoffService: SignoffService = {
  getSignoffStatus: async (releaseId: string) => {
    try {
      const release = await cosmosService.getRelease(releaseId);
      if (!release) {
        throw new Error('Release not found');
      }

      // Get team configuration to understand approval requirements
      const team = await cosmosService.getTeam(release.teamId);
      if (!team) {
        throw new Error('Team not found');
      }

      const approvals = ((release as any).approvals as Approval[]) || [];

      // Get the release type configuration
      const releaseTypeConfig = team.release_types?.[release.releaseType];
      const signoffOrder = releaseTypeConfig?.signoff_order || [];

      // Calculate completed approvals
      const completedApprovals = approvals.filter((a) => a.status === 'approved').length;

      // Calculate total required based on team configuration
      let totalRequired = 0;
      const stages: SignoffStage[] = [];

      if (releaseTypeConfig) {
        // Process each stage in order
        for (const stage of signoffOrder) {
          const enforcement = releaseTypeConfig.enforcement[stage];
          let stageRequired = 0;

          if (enforcement) {
            if (stage === 'functional_qa' && team.roles?.functional_qa) {
              // Calculate functional QA requirements
              if (typeof team.roles.functional_qa === 'object') {
                const subTeams = Object.keys(team.roles.functional_qa);
                if (enforcement.require_all_subteams) {
                  stageRequired = subTeams.length;
                } else if (enforcement.min_signoffs_per_team) {
                  stageRequired = subTeams.length * enforcement.min_signoffs_per_team;
                } else {
                  stageRequired = enforcement.min_signoffs || subTeams.length;
                }
              } else {
                stageRequired = enforcement.min_signoffs || 1;
              }
            } else {
              // Calculate requirements for other stages
              stageRequired = enforcement.min_total_signoffs || enforcement.min_signoffs || 1;
            }
          }

          // Calculate stage completion
          const stageApprovals = approvals.filter(
            (a) => a.stage === stage && a.status === 'approved'
          ).length;
          const stageProgress =
            stageRequired > 0 ? Math.round((stageApprovals / stageRequired) * 100) : 0;

          // Determine stage status
          let status: 'pending' | 'in_progress' | 'completed' = 'pending';
          if (stageApprovals >= stageRequired) {
            status = 'completed';
          } else if (stageApprovals > 0) {
            status = 'in_progress';
          }

          // Determine if this is the current stage
          const isCurrentStage = !stages.some((s) => s.status !== 'completed');

          stages.push({
            name: stage,
            status,
            required: stageRequired,
            completed: stageApprovals,
            progress: stageProgress,
            isCurrentStage,
          });

          totalRequired += stageRequired;
        }
      }

      // Use the progress directly from the release object
      const progress = (release as any).progress || 0;

      // Find current stage
      const currentStage = stages.find((s) => s.isCurrentStage)?.name || signoffOrder[0];

      return {
        progress,
        pendingApprovals: Math.max(0, totalRequired - completedApprovals),
        completedApprovals,
        totalRequired,
        currentStage,
        stages,
        lastUpdated: new Date(release.lastMetricsUpdate || release.updatedAt),
      };
    } catch (error) {
      console.error('Error fetching signoff status:', error);
      throw new Error('Failed to fetch signoff status');
    }
  },
};

const metricsService: MetricsService = {
  getReleaseMetrics: async (releaseId: string) => {
    try {
      const release = await cosmosService.getRelease(releaseId);
      if (!release) {
        throw new Error('Release not found');
      }
      return {
        overallConfidence: release.confidenceMetrics?.overallScore || 0,
        codeQuality: release.releaseMetrics?.codeQuality?.score || 0,
        testCoverage: release.releaseMetrics?.codeCoverage?.score || 0,
        performance: release.releaseMetrics?.buildStability?.score || 0,
        lastUpdated: new Date(release.lastMetricsUpdate || release.updatedAt),
      };
    } catch (error) {
      console.error('Error fetching metrics:', error);
      throw new Error('Failed to fetch metrics');
    }
  },
};

// Serve static files from client/dist
const clientDistPath = path.join(__dirname, '../../client/dist');
app.use(express.static(clientDistPath));

// Register API routes
app.use('/api/teams', teamsRoutes);
app.use('/api/releases', releasesRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/metrics', metricsRouter);
app.use('/api/signoffs', signoffsRouter);
app.use('/api/pcf-requests', pcfRequestsRouter);
app.use('/api/vault', vaultRouter);
app.use('/api/sonarqube', sonarqubeRouter);
app.use('/api/github', githubRouter);
app.use('/api/servicenow', servicenowRouter);
app.use('/api/looper', looperRouter);

// Initialize ChatbotService with a placeholder (will be replaced after async init)
let chatbotController: ChatbotController | undefined;

(async () => {
  try {
    // Initialize Cosmos DB
    logger.info('Initializing Cosmos DB...');
    await cosmosService.initializeDatabase();
    await cosmosService.seedTeamsFromYaml();

    // Initialize Azure OpenAI service
    logger.info('Initializing Azure OpenAI service...');
    const azureOpenAIService = await createAzureOpenAIService();
    logger.info('🤖 Azure OpenAI service initialized successfully');

    // Initialize ChatbotService with Azure OpenAI
    const chatbotService = new ChatbotService(
      azureOpenAIService,
      releaseService,
      signoffService,
      metricsService,
      cosmosService
    );
    chatbotController = new ChatbotController(chatbotService);
    logger.info('✅ Chatbot service initialized successfully');

    // Initialize SonarQube configuration
    logger.info('Initializing SonarQube configuration...');
    try {
      await loadSonarQubeConfig();
      logger.info('✅ SonarQube configuration loaded successfully');
    } catch (error) {
      logger.warn(
        '⚠️  SonarQube configuration failed to load - SonarQube features will be unavailable',
        error
      );
    }

    // Initialize GitHub configuration
    logger.info('Initializing GitHub configuration...');
    try {
      await loadGitHubConfig();
      logger.info('✅ GitHub configuration loaded successfully');
    } catch (error) {
      logger.warn(
        '⚠️  GitHub configuration failed to load - GitHub features will be unavailable',
        error
      );
    }

    // Initialize Looper configuration
    logger.info('Initializing Looper configuration...');
    try {
      await loadLooperConfig();
      logger.info('✅ Looper configuration loaded successfully');
    } catch (error) {
      logger.warn(
        '⚠️  Looper configuration failed to load - Build stability features will be unavailable',
        error
      );
    }

    // Start the server with graceful shutdown support
    startServer();
  } catch (error) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
})();

// Register chatbot route with a proxy handler that waits for initialization
app.use('/api/chat', (req, res, next) => {
  if (!chatbotController) {
    return res
      .status(503)
      .json({ error: 'Chatbot service is initializing, please try again shortly.' });
  }
  return createChatbotRouter(chatbotController)(req, res, next);
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: config.nodeEnv,
    version: process.env.npm_package_version || '1.0.0',
  });
});

// Catch-all handler: send back React's index.html file for client-side routing
app.get('*', (req, res) => {
  // Skip API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({
      message: 'API endpoint not found',
      path: req.path,
    });
  }

  // Serve React app for all other routes
  res.sendFile(path.join(clientDistPath, 'index.html'));
});

// 404 handler
app.use(notFoundHandler);

// Error handling middleware (must be last)
app.use(errorHandler);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', error);
  // Only exit for critical errors, not authentication-related issues
  if (error.message?.includes('authentication') || error.message?.includes('session')) {
    logger.warn('Authentication-related uncaught exception, continuing...');
    return;
  }
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
  // Only exit for critical errors, not authentication-related issues
  const reasonStr = String(reason);
  if (
    reasonStr.includes('authentication') ||
    reasonStr.includes('session') ||
    reasonStr.includes('login')
  ) {
    logger.warn('Authentication-related unhandled rejection, continuing...');
    return;
  }
  process.exit(1);
});

// Graceful shutdown with cleanup

const gracefulShutdown = (signal: string) => {
  logger.info(`${signal} received, shutting down gracefully`);

  if (server) {
    server.close(() => {
      logger.info('HTTP server closed');
      process.exit(0);
    });

    // Force close after 10 seconds
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  } else {
    process.exit(0);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

export default app;
