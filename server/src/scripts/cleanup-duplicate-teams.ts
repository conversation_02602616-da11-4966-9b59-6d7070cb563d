#!/usr/bin/env ts-node

import cosmosService from '../services/cosmosService';

/**
 * Standalone script to find and clean up duplicate teams in the database
 * Keeps the newest team and removes older duplicates
 */
async function cleanupDuplicateTeams() {
  try {
    console.log('🚀 Starting duplicate teams cleanup process...');
    
    // Initialize database
    await cosmosService.initializeDatabase();
    
    // Find and clean up duplicates
    const result = await cosmosService.cleanupDuplicateTeams();
    
    if (result.cleaned === 0) {
      console.log('✨ Database is clean - no duplicate teams found!');
    } else {
      console.log('🎉 Cleanup completed successfully!');
      console.log(`📊 Summary:`);
      console.log(`   - Teams kept: ${result.kept}`);
      console.log(`   - Duplicates removed: ${result.cleaned}`);
    }
    
    // Show final team count
    const allTeams = await cosmosService.getAllTeams();
    console.log(`💾 Total teams in database: ${allTeams.length}`);
    
  } catch (error) {
    console.error('💥 Failed to cleanup duplicate teams:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  cleanupDuplicateTeams().then(() => {
    console.log('✅ Cleanup script completed');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Cleanup script failed:', error);
    process.exit(1);
  });
}

export { cleanupDuplicateTeams }; 