#!/usr/bin/env ts-node

import cosmosService from '../services/cosmosService';
import yamlLoader from '../services/yamlLoader';

/**
 * Standalone script to load YAML teams data into the database
 * This can be run manually or via cron jobs
 */
async function loadYamlToDatabase() {
  try {
    console.log('🚀 Starting YAML to Database loading process...');
    
    // Initialize database
    await cosmosService.initializeDatabase();
    
    // First, clean up any existing duplicates
    console.log('🧹 Cleaning up any existing duplicate teams...');
    await cosmosService.cleanupDuplicateTeams();
    
    // Load teams from YAML files
    const yamlTeams = await yamlLoader.loadAllTeams();
    
    if (yamlTeams.length === 0) {
      console.log('⚠️ No teams found in YAML files');
      process.exit(0);
    }
    
    console.log(`📄 Found ${yamlTeams.length} teams in YAML files`);
    
    // Validate YAML teams for uniqueness
    const yamlTeamNames = yamlTeams.map(t => t.teamName.toLowerCase());
    const duplicateYamlNames = yamlTeamNames.filter((name, index) => yamlTeamNames.indexOf(name) !== index);
    
    if (duplicateYamlNames.length > 0) {
      console.error(`❌ Found duplicate team names in YAML files: ${duplicateYamlNames.join(', ')}`);
      process.exit(1);
    }
    
    // Get existing teams from database
    const existingTeams = await cosmosService.getAllTeams();
    console.log(`💾 Found ${existingTeams.length} existing teams in database`);
    
    let created = 0;
    let updated = 0;
    let skipped = 0;
    
    // Process each YAML team
    for (const yamlTeam of yamlTeams) {
      try {
        // Check if team already exists by teamName (case-insensitive)
        const existingTeam = existingTeams.find(t => 
          t.teamName.toLowerCase() === yamlTeam.teamName.toLowerCase()
        );
        
        if (existingTeam) {
          // Compare team data to see if update is needed
          const needsUpdate = (
            JSON.stringify(existingTeam.metadata) !== JSON.stringify(yamlTeam.metadata) ||
            JSON.stringify(existingTeam.roles) !== JSON.stringify(yamlTeam.roles) ||
            JSON.stringify(existingTeam.release_types) !== JSON.stringify(yamlTeam.release_types)
          );
          
          if (needsUpdate) {
            console.log(`🔄 Updating team: ${yamlTeam.teamName}`);
            await cosmosService.updateTeam(existingTeam.id!, {
              ...yamlTeam,
              id: existingTeam.id // Keep existing ID
            });
            updated++;
          } else {
            console.log(`✅ Team already up to date: ${yamlTeam.teamName}`);
            skipped++;
          }
        } else {
          // Create new team
          console.log(`➕ Creating new team: ${yamlTeam.teamName}`);
          await cosmosService.createTeam(yamlTeam);
          created++;
        }
      } catch (error) {
        console.error(`❌ Failed to process team ${yamlTeam.teamName}:`, error);
        // Continue with other teams instead of failing completely
      }
    }
    
    console.log('🎉 YAML to Database loading completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Created: ${created} teams`);
    console.log(`   - Updated: ${updated} teams`);
    console.log(`   - Skipped: ${skipped} teams`);
    console.log(`   - Total processed: ${created + updated + skipped} teams`);
    
    // Final validation - check for any remaining duplicates
    const finalDuplicates = await cosmosService.findDuplicateTeams();
    if (finalDuplicates.length > 0) {
      console.warn(`⚠️ Warning: Found ${finalDuplicates.length} sets of duplicate teams after loading`);
      for (const dup of finalDuplicates) {
        console.warn(`   - ${dup.teamName}: ${dup.count} entries`);
      }
    }
    
  } catch (error) {
    console.error('💥 Failed to load YAML to database:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  loadYamlToDatabase().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { loadYamlToDatabase }; 