// Release types
export type ReleaseType = 'regular' | 'emergency' | 'hotfix';
export type ReleaseStatus = 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'failed';
export type ReleasePriority = 'low' | 'medium' | 'high' | 'critical';

// Client-compatible release types
export type ClientReleaseType = 'minor' | 'major' | 'patch' | 'hotfix';

// Approval interface for signoff tracking
export interface Approval {
  id: string;
  releaseId: string;
  teamId: string;
  stage: string;
  approverEmail: string;
  status: 'pending' | 'approved' | 'rejected';
  sha?: string;
  buildNumber?: string;
  subTeam?: string;
  approvedAt?: string;
  notes?: string;
}

// Add comprehensive metrics interfaces
export interface MetricScore {
  score: number;
  label: string;
  lastUpdated: string;
  status: 'success' | 'warning' | 'error';
  details?: Record<string, any>;
}

export interface ReleaseMetrics {
  codeQuality?: MetricScore & {
    bugs?: number;
    vulnerabilities?: number;
    codeSmells?: number;
    coverage?: number;
    maintainabilityRating?: 'A' | 'B' | 'C' | 'D' | 'E';
    reliabilityRating?: 'A' | 'B' | 'C' | 'D' | 'E';
    securityRating?: 'A' | 'B' | 'C' | 'D' | 'E';
  };
  codeCoverage?: MetricScore & {
    coverage?: number;
    linesToCover?: number;
    uncoveredLines?: number;
    lineCoverage?: number;
    conditionsToCover?: number;
    uncoveredConditions?: number;
    tests?: number;
    testErrors?: number;
    testFailures?: number;
    skippedTests?: number;
    testSuccessDensity?: number;
    testExecutionTime?: number;
  };
  testResults?: MetricScore & {
    totalTests?: number;
    testErrors?: number;
    testFailures?: number;
    skippedTests?: number;
    testSuccessDensity?: number;
    testExecutionTime?: number;
  };
  buildStability?: MetricScore & {
    statistics?: {
      total: number;
      success: number;
      failure: number;
      aborted: number;
      unstable: number;
      successRate: number;
      failureRate: number;
      avgBuildTime: number;
      lastBuildDate: string;
      firstBuildDate: string;
    };
    builds?: any[];
    teamId?: string;
    branch?: string;
    qualityScore?: number;
  };
  changeRequests?: MetricScore & {
    totalPRs?: number;
    mergedPRs?: number;
    closedPRs?: number;
    openPRs?: number;
    branch?: string;
    dateRange?: {
      start: string;
      end: string;
    };
    rawData?: any[];
  };
  changeLog?: MetricScore & {
    totalPRs?: number;
    mergedPRs?: number;
    closedPRs?: number;
    openPRs?: number;
    branch?: string;
    dateRange?: {
      start: string;
      end: string;
    };
    rawData?: any[];
  };
  pcfRequests?: MetricScore & {
    totalRequests?: number;
    pendingRequests?: number;
    approvedRequests?: number;
    rejectedRequests?: number;
    rawData?: any[];
  };
}

export interface ConfidenceMetrics {
  overallScore: number;
  lastCalculated: string;
  weightings: {
    codeQuality: number;
    codeCoverage: number;
    testResults: number;
    buildStability: number;
    changeRequests: number;
    changeLog: number;
  };
  trending: {
    direction: 'up' | 'down' | 'stable';
    changePercent: number;
    previousScore?: number;
  };
}

export interface Release {
  id: string;
  version: string;
  title: string;
  description?: string;
  type: ReleaseType;
  status: ReleaseStatus;
  priority: ReleasePriority;
  teamId: string;
  teamName?: string;
  createdBy: string;
  assignee?: string;
  plannedDate: string;
  releaseDate?: string;
  createdAt: string;
  updatedAt: string;
  // Signoff tracking fields
  approvals?: Approval[];
  progress?: number;
  completedAt?: string;
  // Add comprehensive metrics storage
  confidenceMetrics?: ConfidenceMetrics;
  releaseMetrics?: ReleaseMetrics;
  lastMetricsUpdate?: string;
  metricsUpdateFrequency?: number; // minutes
  // Cosmos DB specific fields
  _ts?: number;
  _etag?: string;
}

export interface CreateReleaseData {
  version: string;
  title: string;
  description?: string;
  type: ReleaseType;
  priority: ReleasePriority;
  teamId: string;
  createdBy: string;
  assignee?: string;
  plannedDate: string;
}

// Client-compatible release interface
export interface ClientRelease {
  id: string;
  releaseName: string;
  teamId: string;
  teamName: string;
  releaseRolloutDate: string;
  releaseType: ClientReleaseType;
  releaseBranch: string;
  supportedPlatforms: {
    mobile: {
      enabled: boolean;
      ios: boolean;
      android: boolean;
    };
    web: {
      enabled: boolean;
      desktop: boolean;
      mobileWeb: boolean;
    };
    backend: {
      enabled: boolean;
    };
  };
  description?: string;
  confidenceScore?: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  // Add comprehensive metrics storage
  confidenceMetrics?: ConfidenceMetrics;
  releaseMetrics?: ReleaseMetrics;
  lastMetricsUpdate?: string;
  metricsUpdateFrequency?: number; // minutes
  // Cosmos DB specific fields
  _ts?: number;
  _etag?: string;
}

export interface CreateClientReleaseData {
  releaseName: string;
  teamId: string;
  releaseRolloutDate: string;
  releaseType: ClientReleaseType;
  releaseBranch: string;
  supportedPlatforms: {
    mobile: {
      enabled: boolean;
      ios: boolean;
      android: boolean;
    };
    web: {
      enabled: boolean;
      desktop: boolean;
      mobileWeb: boolean;
    };
    backend: {
      enabled: boolean;
    };
  };
  description?: string;
}

// Completely flexible Team interface - now single platform per team
export interface Team {
  id?: string; // Added by database
  teamName: string;
  metadata: {
    // Platform identification (required)
    platform: 'ios' | 'android' | 'web' | 'backend' | 'desktop' | 'mobile_web';
    platform_type: 'mobile' | 'web' | 'backend';
    
    // Core platform configuration (required)
    github_repo: string;
    repo_default_branch: string;
    looper_url: string;
    looper_team_id?: string; // For build stability metrics
    sonarqube_url: string;
    sonarqube_project_id: string;
    
    // Optional platform-specific fields
    deployment_url?: string; // For web and backend platforms
    app_store_url?: string; // For iOS
    play_store_url?: string; // For Android
    deployment_environment?: string; // e.g., "production", "staging"
    
    // Team communication
    slack_webhook?: string;
    
    // ServiceNow integration
    serviceNowId?: string;
    
    [key: string]: any; // Allow additional metadata fields for backward compatibility
  };
  roles: {
    [roleName: string]: string[] | { [subTeamName: string]: string[] };
  };
  release_types: {
    [releaseTypeName: string]: {
      signoff_order: string[];
      enforcement: {
        [roleName: string]: {
          require_all_subteams?: boolean;
          min_signoffs_per_team?: number;
          min_signoffs?: number;
          min_total_signoffs?: number;
          [key: string]: any; // Allow additional enforcement rules
        };
      } & {
        blocking_conditions?: Array<{
          condition: string;
          description: string;
          [key: string]: any; // Allow additional condition properties
        }>;
      };
    };
  };
  // Cosmos DB specific fields
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  _ts?: number;
}

// Remove the old PlatformConfig interface as it's no longer needed
// Platform configuration is now directly in the team metadata 