import keyVaultClient from '../utils/keyVaultClient';

let cachedLooperConfig: { baseUrl: string } | null = null;

export interface LooperConfig {
  baseUrl: string;
}

/**
 * Load Looper configuration from Azure Key Vault
 */
export async function loadLooperConfig(): Promise<LooperConfig> {
  if (cachedLooperConfig) {
    return cachedLooperConfig;
  }

  try {
    console.log('[Looper] Loading Looper configuration from Key Vault...');
    
    // Fetch base URL from Key Vault
    const baseUrl = await keyVaultClient.getSecret('looperpro-base-url');
    
    if (!baseUrl) {
      throw new Error('Looper base URL not found in Key Vault');
    }

    cachedLooperConfig = {
      baseUrl: baseUrl.trim()
    };

    console.log('[Looper] ✅ Looper configuration loaded successfully');
    return cachedLooperConfig;
  } catch (error) {
    console.error('[Looper] ❌ Failed to load Looper configuration:', error);
    throw new Error(`Failed to load Looper configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get cached Looper configuration
 */
export function getLooperConfig(): LooperConfig | null {
  return cachedLooperConfig;
}

/**
 * Clear cached configuration (useful for testing)
 */
export function clearLooperConfig(): void {
  cachedLooperConfig = null;
} 