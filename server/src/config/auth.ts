import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env file first
dotenv.config({ path: path.join(__dirname, '../../../.env') });

export type AuthProvider = 'github' | 'azure' | 'saml' | 'pingfed' | 'both';

export interface AuthConfig {
  provider: AuthProvider;
  sessionSecret: string;
  jwtSecret: string;
  jwtRefreshSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  github: {
    enabled: boolean;
    clientId: string;
    clientSecret: string;
    callbackURL: string;
  };
  azure: {
    enabled: boolean;
    clientId: string;
    clientSecret: string;
    tenantId: string;
    callbackURL: string;
  };
  saml: {
    enabled: boolean;
    entryPoint: string;
    issuer: string;
    callbackURL: string;
    logoutUrl?: string;
    logoutCallbackUrl?: string;
  };
  pingfed: {
    enabled: boolean;
    clientId: string;
    clientSecret: string;
    authorizationURL: string;
    tokenURL: string;
    userInfoURL: string;
    callbackURL: string;
  };
}

export const authConfig: AuthConfig = {
  provider: (process.env.AUTH_PROVIDER as AuthProvider) || 'github',
  sessionSecret: process.env.SESSION_SECRET || 'engineering-excellence-session-secret',
  jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
  jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
  jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',

  github: {
    enabled: ['github', 'both'].includes(process.env.AUTH_PROVIDER || 'github'),
    clientId: process.env.GITHUB_CLIENT_ID || '',
    clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    callbackURL:
      process.env.GITHUB_CALLBACK_URL ||
      `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/auth/github/callback`,
  },

  azure: {
    enabled: ['azure', 'both'].includes(process.env.AUTH_PROVIDER || 'github'),
    clientId: process.env.AZURE_CLIENT_ID || '',
    clientSecret: process.env.AZURE_CLIENT_SECRET || '',
    tenantId: process.env.AZURE_TENANT_ID || '',
    callbackURL:
      process.env.AZURE_CALLBACK_URL ||
      `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/auth/azure/callback`,
  },

  saml: {
    enabled: false, // Disabled to avoid conflicts with PingFed OAuth
    entryPoint: process.env.SAML_ENTRY_POINT || 'https://pfeddev.wal-mart.com/idp/SSO.saml2',
    issuer: process.env.SAML_ISSUER || 'engineering-excellence-app',
    callbackURL:
      process.env.SAML_CALLBACK_URL ||
      `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/auth/saml/callback`,
    logoutUrl: process.env.SAML_LOGOUT_URL || 'https://pfeddev.wal-mart.com/idp/SLO.saml2',
    logoutCallbackUrl:
      process.env.SAML_LOGOUT_CALLBACK_URL ||
      `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/auth/saml/logout/callback`,
  },

  pingfed: {
    enabled: ['pingfed', 'both'].includes(process.env.AUTH_PROVIDER || 'github'),
    clientId: process.env.PINGFED_CLIENT_ID || 'oneview-ee-pingfed',
    clientSecret: process.env.PINGFED_CLIENT_SECRET || 'c6aa0cc2-9456-4ca4-bf71-20ef82b62b1c',
    authorizationURL:
      process.env.PINGFED_AUTHORIZATION_URL ||
      'https://pfeddev.wal-mart.com/as/authorization.oauth2',
    tokenURL: process.env.PINGFED_TOKEN_URL || 'https://pfeddev.wal-mart.com/as/token.oauth2',
    userInfoURL:
      process.env.PINGFED_USERINFO_URL || 'https://pfeddev.wal-mart.com/idp/userinfo.openid',
    callbackURL:
      process.env.PINGFED_CALLBACK_URL ||
      'https://signal.walmartlabs.com/api/auth/pingfed/callback',
  },
};

// Debug logging (remove in production)
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Auth Config Debug:', {
    authProvider: process.env.AUTH_PROVIDER,
    githubClientId: process.env.GITHUB_CLIENT_ID ? '✅ Set' : '❌ Missing',
    githubClientSecret: process.env.GITHUB_CLIENT_SECRET ? '✅ Set' : '❌ Missing',
    githubEnabled: authConfig.github.enabled,
    azureEnabled: authConfig.azure.enabled,
    samlEnabled: authConfig.saml.enabled,
    samlEntryPoint: authConfig.saml.entryPoint ? '✅ Set' : '❌ Missing',
    samlIssuer: authConfig.saml.issuer ? '✅ Set' : '❌ Missing',
    pingfedEnabled: authConfig.pingfed.enabled,
    pingfedClientId: authConfig.pingfed.clientId ? '✅ Set' : '❌ Missing',
    pingfedClientSecret: authConfig.pingfed.clientSecret ? '✅ Set' : '❌ Missing',
  });
}

// Validation function to ensure required config is present
export const validateAuthConfig = (): void => {
  const errors: string[] = [];

  if (authConfig.github.enabled) {
    if (!authConfig.github.clientId)
      errors.push('GITHUB_CLIENT_ID is required when GitHub auth is enabled');
    if (!authConfig.github.clientSecret)
      errors.push('GITHUB_CLIENT_SECRET is required when GitHub auth is enabled');
  }

  if (authConfig.azure.enabled) {
    if (!authConfig.azure.clientId)
      errors.push('AZURE_CLIENT_ID is required when Azure AD auth is enabled');
    if (!authConfig.azure.clientSecret)
      errors.push('AZURE_CLIENT_SECRET is required when Azure AD auth is enabled');
    if (!authConfig.azure.tenantId)
      errors.push('AZURE_TENANT_ID is required when Azure AD auth is enabled');
  }

  if (authConfig.saml.enabled) {
    if (!authConfig.saml.entryPoint)
      errors.push('SAML_ENTRY_POINT is required when SAML auth is enabled');
    if (!authConfig.saml.issuer) errors.push('SAML_ISSUER is required when SAML auth is enabled');
    if (!authConfig.saml.callbackURL)
      errors.push('SAML_CALLBACK_URL is required when SAML auth is enabled');
  }

  if (authConfig.pingfed.enabled) {
    if (!authConfig.pingfed.clientId)
      errors.push('PINGFED_CLIENT_ID is required when PingFed auth is enabled');
    if (!authConfig.pingfed.clientSecret)
      errors.push('PINGFED_CLIENT_SECRET is required when PingFed auth is enabled');
    if (!authConfig.pingfed.authorizationURL)
      errors.push('PINGFED_AUTHORIZATION_URL is required when PingFed auth is enabled');
    if (!authConfig.pingfed.tokenURL)
      errors.push('PINGFED_TOKEN_URL is required when PingFed auth is enabled');
    if (!authConfig.pingfed.userInfoURL)
      errors.push('PINGFED_USERINFO_URL is required when PingFed auth is enabled');
  }

  if (
    !authConfig.github.enabled &&
    !authConfig.azure.enabled &&
    !authConfig.saml.enabled &&
    !authConfig.pingfed.enabled
  ) {
    errors.push('At least one authentication provider must be enabled');
  }

  if (errors.length > 0) {
    throw new Error(`Authentication configuration errors:\n${errors.join('\n')}`);
  }
};
