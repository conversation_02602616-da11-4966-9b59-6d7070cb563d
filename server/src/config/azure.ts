import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env file
dotenv.config({ path: path.join(__dirname, '../../../.env') });

export const azureConfig = {
  azureAD: {
    tenantId: process.env.AZURE_TENANT_ID || '',
    clientId: process.env.AZURE_CLIENT_ID || '',
    clientSecret: process.env.AZURE_CLIENT_SECRET || '',
  },
  keyVault: {
    name: process.env.KEY_VAULT_NAME || '',
    url: process.env.KEY_VAULT_URL || `https://${process.env.KEY_VAULT_NAME}.vault.azure.net/`,
  },
};

// Validation helper
export const validateAzureConfig = () => {
  const { azureAD, keyVault } = azureConfig;
  
  if (!azureAD.tenantId || !azureAD.clientId || !azureAD.clientSecret) {
    throw new Error('Missing required Azure AD credentials in environment variables');
  }
  
  if (!keyVault.name) {
    throw new Error('Missing KEY_VAULT_NAME in environment variables');
  }
  
  return true;
}; 