import { CosmosClient } from '@azure/cosmos';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env file
dotenv.config({ path: path.join(__dirname, '../../../.env') });

const cosmosConfig = {
  endpoint: process.env.COSMOSDB_ENDPOINT!,
  key: process.env.COSMOSDB_KEY!,
  databaseId: process.env.COSMOSDB_DATABASE!,
  containers: {
    teams: process.env.COSMOSDB_CONTAINER_TEAMS!
  }
};

// Validate required environment variables
if (!cosmosConfig.endpoint || !cosmosConfig.key || !cosmosConfig.databaseId) {
  throw new Error('Missing required Cosmos DB environment variables');
}

const cosmosClient = new CosmosClient({
  endpoint: cosmosConfig.endpoint,
  key: cosmosConfig.key
});

export { cosmosClient, cosmosConfig }; 