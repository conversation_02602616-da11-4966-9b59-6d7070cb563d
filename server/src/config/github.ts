import keyVaultClient from '../utils/keyVaultClient';

interface GitHubConfig {
  headers: {
    'Authorization': string;
    'Accept': string;
    'Content-Type': string;
    'X-GitHub-Api-Version': string;
  };
  baseUrl: string;
}

let config: GitHubConfig | null = null;

export async function loadGitHubConfig(): Promise<GitHubConfig> {
  try {
    console.log('[GitHub] Loading GitHub configuration from Key Vault...');
    
    const [token, baseUrl] = await Promise.all([
      keyVaultClient.getSecret('github-token'),
      keyVaultClient.getSecret('github-base-url')
    ]);

    config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json',
        'X-GitHub-Api-Version': '2022-11-28'
      },
      baseUrl: baseUrl.trim() || 'https://api.github.com' // Fallback to public GitHub API
    };

    console.log('[GitHub] ✅ GitHub configuration loaded successfully');
    return config;
  } catch (error) {
    console.error('[GitHub] ❌ Failed to load GitHub configuration:', error);
    throw new Error(`Failed to load GitHub configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export function getGitHubConfig(): GitHubConfig {
  if (!config) {
    throw new Error('GitHub configuration not loaded. Call loadGitHubConfig first.');
  }
  return config;
}

// Create GitHub config with explicit base URL (for testing)
export function createGitHubConfig(baseUrl: string, token: string): GitHubConfig {
  return {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/vnd.github.v3+json',
      'Content-Type': 'application/json',
      'X-GitHub-Api-Version': '2022-11-28'
    },
    baseUrl: baseUrl.trim()
  };
} 