import dotenv from 'dotenv';
import path from 'path';
import { authConfig } from './auth';
import { azureConfig } from './azure';

// Load environment variables from root .env file
dotenv.config({ path: path.join(__dirname, '../../../.env') });

// Calculate session security settings
const isSecureEnvironment =
  process.env.NODE_ENV === 'production' ||
  process.env.STAGE_NAME === 'prod' ||
  process.env.BACKEND_URL?.startsWith('https://');

// Log session security configuration
console.log('🍪 Session cookie security settings:', {
  nodeEnv: process.env.NODE_ENV,
  stageName: process.env.STAGE_NAME,
  backendUrl: process.env.BACKEND_URL,
  secureCookes: isSecureEnvironment,
});

export const config = {
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Database
  databaseUrl: process.env.DATABASE_URL || '',

  // Cosmos DB
  cosmosdb: {
    endpoint: process.env.COSMOSDB_ENDPOINT || '',
    key: process.env.COSMOSDB_KEY || '',
    database: process.env.COSMOSDB_DATABASE || '',
    containers: {
      releases: process.env.COSMOSDB_CONTAINER_RELEASES || '',
      teams: process.env.COSMOSDB_CONTAINER_TEAMS || '',
    },
  },

  // Azure Key Vault
  azure: azureConfig,

  // URLs - centralized configuration
  urls: {
    frontend: process.env.FRONTEND_URL || 'http://localhost:3000',
    backend: process.env.BACKEND_URL || 'http://localhost:3001',
  },

  // Authentication (now centralized)
  auth: authConfig,

  // Legacy GitHub config (keeping for backward compatibility)
  github: authConfig.github,

  // JWT (now from auth config)
  jwt: {
    secret: authConfig.jwtSecret,
    refreshSecret: authConfig.jwtRefreshSecret,
    expiresIn: authConfig.jwtExpiresIn,
    refreshExpiresIn: authConfig.jwtRefreshExpiresIn,
  },

  // Session (now from auth config)
  session: {
    secret: authConfig.sessionSecret,
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: isSecureEnvironment,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      httpOnly: true,
      sameSite: (isSecureEnvironment ? 'none' : 'lax') as 'none' | 'lax' | 'strict',
      domain: isSecureEnvironment ? '.walmartlabs.com' : undefined, // Set domain for production
    },
    // Use file store for production to avoid memory leaks
    name: 'engineering-excellence-session',
  },

  // CORS - using centralized frontend URL
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  // Security
  bcrypt: {
    saltRounds: 12,
  },
};

export const isDevelopment = config.nodeEnv === 'development';
export const isProduction = config.nodeEnv === 'production';
export const isTest = config.nodeEnv === 'test';
