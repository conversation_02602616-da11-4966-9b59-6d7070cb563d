import keyVaultClient from '../utils/keyVaultClient';

interface SonarQubeConfig {
  baseUrl: string;
  apiUrl: string;
  headers: {
    'Content-Type': string;
    'Authorization': string;
  };
}

let tokenCache: string | null = null;

/**
 * Load SonarQube token from Azure Key Vault (cached for performance)
 */
export async function loadSonarQubeToken(): Promise<string> {
  if (tokenCache) {
    return tokenCache;
  }

  try {
    console.log('🔑 Loading SonarQube token from Azure Key Vault...');
    tokenCache = await keyVaultClient.getSecret('sonarqube-token');
    console.log('✅ SonarQube token loaded successfully');
    return tokenCache;
  } catch (error) {
    console.error('❌ Failed to load SonarQube token:', error);
    throw error;
  }
}

/**
 * Create SonarQube configuration for a specific team/project
 * @param baseUrl - SonarQube instance URL from team metadata
 */
export async function createSonarQubeConfig(baseUrl: string): Promise<SonarQubeConfig> {
  if (!baseUrl) {
    throw new Error('SonarQube base URL is required');
  }

  try {
    const token = await loadSonarQubeToken();
    
    // Remove trailing slashes and ensure proper format
    const cleanBaseUrl = baseUrl.trim().replace(/\/+$/, '');
    
    const config: SonarQubeConfig = {
      baseUrl: cleanBaseUrl,
      apiUrl: `${cleanBaseUrl}/api`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + Buffer.from(`${token}:`).toString('base64')
      }
    };

    console.log(`✅ SonarQube configuration created for: ${cleanBaseUrl}`);
    return config;
  } catch (error) {
    console.error('❌ Failed to create SonarQube configuration:', error);
    throw error;
  }
}

/**
 * Clear token cache (useful for testing or token rotation)
 */
export function clearTokenCache(): void {
  tokenCache = null;
  console.log('🔄 SonarQube token cache cleared');
}

// Legacy support - load default config from Key Vault (deprecated)
let legacyConfig: SonarQubeConfig | null = null;

export async function loadSonarQubeConfig(): Promise<SonarQubeConfig> {
  console.warn('⚠️ loadSonarQubeConfig() is deprecated. Use createSonarQubeConfig(baseUrl) instead.');
  
  try {
    const [baseUrl, token] = await Promise.all([
      keyVaultClient.getSecret('sonarqube-base-url'),
      keyVaultClient.getSecret('sonarqube-token')
    ]);

    // Remove trailing slashes and ensure proper format
    const cleanBaseUrl = baseUrl.trim().replace(/\/+$/, '');
    
    legacyConfig = {
      baseUrl: cleanBaseUrl,
      apiUrl: `${cleanBaseUrl}/api`,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + Buffer.from(`${token}:`).toString('base64')
      }
    };

    console.log('✅ Legacy SonarQube configuration loaded successfully');
    return legacyConfig;
  } catch (error) {
    console.error('❌ Failed to load legacy SonarQube configuration:', error);
    throw error;
  }
}

export function getConfig(): SonarQubeConfig {
  if (!legacyConfig) {
    throw new Error('Legacy SonarQube configuration not loaded. Call loadSonarQubeConfig first or use createSonarQubeConfig.');
  }
  return legacyConfig;
} 