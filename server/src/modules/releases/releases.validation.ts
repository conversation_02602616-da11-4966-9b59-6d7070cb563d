import Joi from 'joi';

const supportedPlatformsSchema = Joi.object({
  mobile: Joi.object({
    enabled: Joi.boolean().required(),
    ios: Joi.boolean().required(),
    android: Joi.boolean().required()
  }).required(),
  web: Joi.object({
    enabled: Joi.boolean().required(),
    desktop: Joi.boolean().required(),
    mobileWeb: Joi.boolean().required()
  }).required(),
  backend: Joi.object({
    enabled: Joi.boolean().required()
  }).required()
}).required().custom((value, helpers) => {
  // Business logic: A platform should only be "enabled" if at least one sub-option is selected
  if (value.mobile.enabled && !value.mobile.ios && !value.mobile.android) {
    return helpers.error('any.invalid', { 
      message: 'Mobile platform enabled but no sub-options (iOS/Android) selected' 
    });
  }
  
  if (value.web.enabled && !value.web.desktop && !value.web.mobileWeb) {
    return helpers.error('any.invalid', { 
      message: 'Web platform enabled but no sub-options (Desktop/Mobile Web) selected' 
    });
  }
  
  // At least one platform must be selected
  const hasMobile = value.mobile.ios || value.mobile.android;
  const hasWeb = value.web.desktop || value.web.mobileWeb;
  const hasBackend = value.backend.enabled;
  
  if (!hasMobile && !hasWeb && !hasBackend) {
    return helpers.error('any.invalid', { 
      message: 'At least one platform must be selected' 
    });
  }
  
  return value;
});

const supportedPlatformsUpdateSchema = Joi.object({
  mobile: Joi.object({
    enabled: Joi.boolean().required(),
    ios: Joi.boolean().required(),
    android: Joi.boolean().required()
  }).optional(),
  web: Joi.object({
    enabled: Joi.boolean().required(),
    desktop: Joi.boolean().required(),
    mobileWeb: Joi.boolean().required()
  }).optional(),
  backend: Joi.object({
    enabled: Joi.boolean().required()
  }).optional()
}).optional().custom((value, helpers) => {
  if (!value) return value; // If no platforms provided, skip validation
  
  // Business logic: A platform should only be "enabled" if at least one sub-option is selected
  if (value.mobile && value.mobile.enabled && !value.mobile.ios && !value.mobile.android) {
    return helpers.error('any.invalid', { 
      message: 'Mobile platform enabled but no sub-options (iOS/Android) selected' 
    });
  }
  
  if (value.web && value.web.enabled && !value.web.desktop && !value.web.mobileWeb) {
    return helpers.error('any.invalid', { 
      message: 'Web platform enabled but no sub-options (Desktop/Mobile Web) selected' 
    });
  }
  
  // At least one platform must be selected (only check if platforms are provided)
  const hasMobile = value.mobile && (value.mobile.ios || value.mobile.android);
  const hasWeb = value.web && (value.web.desktop || value.web.mobileWeb);
  const hasBackend = value.backend && value.backend.enabled;
  
  if (!hasMobile && !hasWeb && !hasBackend) {
    return helpers.error('any.invalid', { 
      message: 'At least one platform must be selected' 
    });
  }
  
  return value;
});

export const createReleaseSchema = Joi.object({
  releaseName: Joi.string().min(1).max(200).required().messages({
    'string.empty': 'Release name is required',
    'string.min': 'Release name must be at least 1 character',
    'string.max': 'Release name cannot exceed 200 characters'
  }),
  
  teamId: Joi.string().guid({ version: 'uuidv4' }).required().messages({
    'string.empty': 'Team ID is required',
    'string.guid': 'Team ID must be a valid UUID'
  }),
  
  releaseRolloutDate: Joi.date().iso().required().messages({
    'date.base': 'Release date must be a valid date',
    'date.format': 'Release date must be in ISO format'
  }),
  
  releaseType: Joi.string().valid('minor', 'major', 'patch', 'hotfix').required().messages({
    'any.only': 'Release type must be one of: minor, major, patch, hotfix'
  }),
  
  releaseBranch: Joi.string().min(1).max(100).required().messages({
    'string.empty': 'Release branch is required',
    'string.min': 'Release branch must be at least 1 character',
    'string.max': 'Release branch cannot exceed 100 characters'
  }),
  
  supportedPlatforms: supportedPlatformsSchema,
  
  description: Joi.string().max(1000).optional().allow('').messages({
    'string.max': 'Description cannot exceed 1000 characters'
  })
});

export const updateReleaseSchema = Joi.object({
  releaseName: Joi.string().min(1).max(200).optional().messages({
    'string.empty': 'Release name cannot be empty',
    'string.min': 'Release name must be at least 1 character',
    'string.max': 'Release name cannot exceed 200 characters'
  }),
  
  teamId: Joi.string().guid({ version: 'uuidv4' }).optional().messages({
    'string.guid': 'Team ID must be a valid UUID'
  }),
  
  releaseRolloutDate: Joi.date().iso().optional().messages({
    'date.base': 'Release date must be a valid date',
    'date.format': 'Release date must be in ISO format'
  }),
  
  releaseType: Joi.string().valid('minor', 'major', 'patch', 'hotfix').optional().messages({
    'any.only': 'Release type must be one of: minor, major, patch, hotfix'
  }),
  
  releaseBranch: Joi.string().min(1).max(100).optional().messages({
    'string.empty': 'Release branch cannot be empty',
    'string.min': 'Release branch must be at least 1 character',
    'string.max': 'Release branch cannot exceed 100 characters'
  }),
  
  supportedPlatforms: supportedPlatformsUpdateSchema,
  
  description: Joi.string().max(1000).optional().allow('').messages({
    'string.max': 'Description cannot exceed 1000 characters'
  })
});

export const releaseIdSchema = Joi.object({
  id: Joi.string().guid({ version: 'uuidv4' }).required().messages({
    'string.empty': 'Release ID is required',
    'string.guid': 'Release ID must be a valid UUID'
  })
});

export const releaseQuerySchema = Joi.object({
  startDate: Joi.date().iso().optional().messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format'
  }),
  
  endDate: Joi.date().iso().optional().messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format'
  }),
  
  teamId: Joi.string().guid({ version: 'uuidv4' }).optional().messages({
    'string.guid': 'Team ID must be a valid UUID'
  })
}); 