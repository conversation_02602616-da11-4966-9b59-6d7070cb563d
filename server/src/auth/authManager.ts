import passport from 'passport';
import { GitHubAuthProvider } from './providers/github.provider';
import { AzureAuthProvider } from './providers/azure.provider';
import { SamlAuthProvider } from './providers/saml.provider';
import { PingFedAuthProvider } from './providers/pingfed.provider';
import { authConfig, validateAuthConfig } from '../config/auth';
import { logger } from '../utils/logger';
import { User } from '../types/api';

export interface AuthProviderInfo {
  name: string;
  loginUrl: string;
  icon: string;
  enabled: boolean;
}

// Shared user store for all providers
const userStore = new Map<string, User>();

export class AuthManager {
  private githubProvider: GitHubAuthProvider;
  private azureProvider: AzureAuthProvider;
  private samlProvider: SamlAuthProvider;
  private pingfedProvider: PingFedAuthProvider;

  constructor() {
    this.githubProvider = new GitHubAuthProvider();
    this.azureProvider = new AzureAuthProvider();
    this.samlProvider = new SamlAuthProvider();
    this.pingfedProvider = new PingFedAuthProvider();

    // Inject this instance into providers to avoid circular dependencies
    this.pingfedProvider.setAuthManager(this);
    logger.info('AuthManager injected into PingFedAuthProvider');
  }

  initialize(): void {
    try {
      // Validate configuration first
      validateAuthConfig();

      logger.info('Initializing Authentication Manager', {
        provider: authConfig.provider,
        githubEnabled: authConfig.github.enabled,
        azureEnabled: authConfig.azure.enabled,
        samlEnabled: authConfig.saml.enabled,
        pingfedEnabled: authConfig.pingfed.enabled,
      });

      // Configure passport serialization
      this.configurePassportSerialization();

      // Configure enabled providers
      if (authConfig.github.enabled) {
        this.githubProvider.configure();
      }

      if (authConfig.azure.enabled) {
        this.azureProvider.configure();
      }

      if (authConfig.saml.enabled) {
        this.samlProvider.configure();
      }

      if (authConfig.pingfed.enabled) {
        this.pingfedProvider.configure();
        // Re-inject authManager after configuration to ensure it's available
        this.pingfedProvider.setAuthManager(this);
      }

      logger.info('Authentication Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Authentication Manager:', error);
      throw error;
    }
  }

  private configurePassportSerialization(): void {
    passport.serializeUser((user: any, done) => {
      done(null, user.id);
    });

    passport.deserializeUser((id: string, done) => {
      try {
        // Get full user data from store
        const user = userStore.get(id);

        if (user) {
          done(null, user);
        } else {
          done(new Error('User not found'), null);
        }
      } catch (error) {
        logger.error('Error deserializing user:', error);
        done(error, null);
      }
    });
  }

  // Method to store user data
  storeUser(user: User): void {
    userStore.set(user.id, user);
  }

  // Method to get user data
  getUser(id: string): User | undefined {
    return userStore.get(id);
  }

  // Method to find user by PingFed ID
  findUserByPingFedId(pingfedId: string): User | undefined {
    return Array.from(userStore.values()).find((user) => user.pingfedId === pingfedId);
  }

  getEnabledProviders(): AuthProviderInfo[] {
    const providers: AuthProviderInfo[] = [];

    if (this.githubProvider.isEnabled()) {
      providers.push({
        name: this.githubProvider.getName(),
        loginUrl: this.githubProvider.getLoginUrl(),
        icon: this.githubProvider.getIcon(),
        enabled: true,
      });
    }

    if (this.azureProvider.isEnabled()) {
      providers.push({
        name: this.azureProvider.getName(),
        loginUrl: this.azureProvider.getLoginUrl(),
        icon: this.azureProvider.getIcon(),
        enabled: true,
      });
    }

    if (this.samlProvider.isEnabled()) {
      providers.push({
        name: this.samlProvider.getName(),
        loginUrl: this.samlProvider.getLoginUrl(),
        icon: this.samlProvider.getIcon(),
        enabled: true,
      });
    }

    if (this.pingfedProvider.isEnabled()) {
      providers.push({
        name: this.pingfedProvider.getName(),
        loginUrl: this.pingfedProvider.getLoginUrl(),
        icon: this.pingfedProvider.getIcon(),
        enabled: true,
      });
    }

    return providers;
  }

  getGitHubProvider(): GitHubAuthProvider {
    return this.githubProvider;
  }

  getAzureProvider(): AzureAuthProvider {
    return this.azureProvider;
  }

  getSamlProvider(): SamlAuthProvider {
    return this.samlProvider;
  }

  isGitHubEnabled(): boolean {
    return this.githubProvider.isEnabled();
  }

  isAzureEnabled(): boolean {
    return this.azureProvider.isEnabled();
  }

  isSamlEnabled(): boolean {
    return this.samlProvider.isEnabled();
  }

  getPingFedProvider(): PingFedAuthProvider {
    return this.pingfedProvider;
  }

  isPingFedEnabled(): boolean {
    return this.pingfedProvider.isEnabled();
  }

  getCurrentProvider(): string {
    return authConfig.provider;
  }
}

// Create singleton instance
export const authManager = new AuthManager();
