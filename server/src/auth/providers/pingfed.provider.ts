import { authConfig } from '../../config/auth';
import { logger } from '../../utils/logger';
import { User, UserRole, AuthProvider, PingFedProfile } from '../../types/api';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

export class PingFedAuthProvider {
  private codeVerifierStore = new Map<string, string>();
  private authManager: any; // Will be injected to avoid circular dependency

  constructor() {
    // Initialize authManager reference - will be set by AuthManager
  }

  setAuthManager(authManager: any): void {
    this.authManager = authManager;
    logger.info('PingFedAuthProvider: AuthManager reference set', {
      hasAuthManager: !!this.authManager,
    });
  }

  configure(): void {
    if (!authConfig.pingfed.enabled) {
      return;
    }

    logger.info('Configuring PingFed OAuth provider');
  }

  isEnabled(): boolean {
    return authConfig.pingfed.enabled;
  }

  getName(): string {
    return 'PingFed';
  }

  getIcon(): string {
    return 'pingfed';
  }

  private generateCodeVerifier(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  private generateCodeChallenge(verifier: string): string {
    return crypto.createHash('sha256').update(verifier).digest('base64url');
  }

  getAuthCodeUrl(state?: string): string {
    if (!authConfig.pingfed.enabled) {
      throw new Error('PingFed OAuth provider not configured');
    }

    const codeVerifier = this.generateCodeVerifier();
    if (state) {
      this.codeVerifierStore.set(state, codeVerifier);
    }

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: authConfig.pingfed.clientId,
      redirect_uri: authConfig.pingfed.callbackURL,
      scope: 'openid full',
      state: state || Math.random().toString(36).substring(7),
      code_challenge: this.generateCodeChallenge(codeVerifier),
      code_challenge_method: 'S256',
    });

    return `${authConfig.pingfed.authorizationURL}?${params.toString()}`;
  }

  private getStoredCodeVerifier(state?: string): string {
    if (!state) {
      throw new Error('State parameter is required for PKCE');
    }
    const codeVerifier = this.codeVerifierStore.get(state);
    if (!codeVerifier) {
      throw new Error('No code verifier found for state');
    }
    this.codeVerifierStore.delete(state); // Clean up after use
    return codeVerifier;
  }

  async handleCallback(code: string, state?: string): Promise<User> {
    if (!authConfig.pingfed.enabled) {
      throw new Error('PingFed OAuth provider not configured');
    }

    try {
      logger.info('PingFed callback started', {
        code: code ? '✅ Present' : '❌ Missing',
        state: state || 'No state',
        clientId: authConfig.pingfed.clientId,
        callbackURL: authConfig.pingfed.callbackURL,
      });

      logger.info('Starting token exchange process...');

      // Exchange authorization code for access token
      const tokenResponse = await this.exchangeCodeForToken(code, state);

      logger.info('PingFed OAuth callback received', {
        sub: tokenResponse.sub,
        hasAccessToken: !!tokenResponse.access_token,
      });

      // Get user profile from PingFed
      const userProfile = await this.getUserProfile(tokenResponse.access_token);

      // Check if user already exists (delegate to auth manager)
      let existingUser: User | undefined;
      if (this.authManager) {
        existingUser = this.authManager.findUserByPingFedId(userProfile.sub);
      }

      if (existingUser) {
        logger.info('Existing PingFed user found', { userId: existingUser.id });
        return existingUser;
      }

      // Create new user from PingFed profile
      const newUser: User = {
        id: uuidv4(),
        email: userProfile.email || '',
        firstName: userProfile.given_name || '',
        lastName: userProfile.family_name || '',
        role: UserRole.USER,
        isActive: true,
        authProvider: AuthProvider.PINGFED,
        pingfedId: userProfile.sub,
        displayName:
          userProfile.name || `${userProfile.given_name} ${userProfile.family_name}`.trim(),
        username: userProfile.preferred_username,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Store user in auth manager (no local storage needed)
      try {
        if (this.authManager) {
          this.authManager.storeUser(newUser);
          logger.info('User stored in AuthManager successfully', { userId: newUser.id });
        } else {
          logger.warn('AuthManager not available, attempting fallback user storage');
          // Fallback: try to get authManager from singleton
          try {
            const { authManager } = await import('../authManager');
            authManager.storeUser(newUser);
            logger.info('User stored via fallback AuthManager', { userId: newUser.id });
          } catch (fallbackError) {
            logger.error('Failed to store user via fallback', fallbackError);
            // The authentication can still proceed without user storage
          }
        }
      } catch (storeError) {
        logger.error('Error storing user in AuthManager:', storeError);
        // Continue with authentication even if storage fails
      }

      logger.info('New user created from PingFed OAuth', {
        userId: newUser.id,
        username: newUser.username,
        displayName: newUser.displayName,
      });

      logger.info('PingFed authentication completed successfully, returning user');
      return newUser;
    } catch (error) {
      logger.error('Error in PingFed OAuth callback:', error);
      // Ensure we don't throw unhandled promise rejections
      if (error instanceof Error) {
        throw new Error(`PingFed authentication failed: ${error.message}`);
      } else {
        throw new Error('PingFed authentication failed: Unknown error');
      }
    }
  }

  private async exchangeCodeForToken(code: string, state?: string): Promise<any> {
    try {
      const tokenParams = new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: authConfig.pingfed.callbackURL,
        client_id: authConfig.pingfed.clientId,
        client_secret: authConfig.pingfed.clientSecret,
        code_verifier: this.getStoredCodeVerifier(state),
      });

      const response = await fetch(authConfig.pingfed.tokenURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'wm_qos.correlation_id': Date.now().toString(),
          Accept: 'application/json',
        },
        body: tokenParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('Token exchange failed', {
          status: response.status,
          statusText: response.statusText,
          errorText,
        });
        throw new Error(`Token exchange failed: ${response.statusText} - ${errorText}`);
      }

      const tokenData = (await response.json()) as {
        access_token?: string;
        id_token?: string;
        token_type?: string;
        expires_in?: number;
        [key: string]: any;
      };

      // Parse the ID token to get user info
      if (tokenData.id_token) {
        const idTokenPayload = this.parseJWT(tokenData.id_token);
        return {
          access_token: tokenData.access_token,
          ...idTokenPayload,
        };
      }

      return tokenData;
    } catch (error) {
      logger.error('Error in exchangeCodeForToken:', error);
      throw error;
    }
  }

  private parseJWT(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(function (c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      logger.error('Error parsing JWT token:', error);
      throw new Error('Invalid JWT token');
    }
  }

  private async getUserProfile(accessToken: string): Promise<PingFedProfile> {
    try {
      // PingFed expects access_token as body parameter, not Authorization header
      const userInfoParams = new URLSearchParams({
        access_token: accessToken,
      });

      const response = await fetch(authConfig.pingfed.userInfoURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'wm_qos.correlation_id': Date.now().toString(),
          Accept: 'application/json',
        },
        body: userInfoParams.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('Failed to get user profile', {
          status: response.status,
          statusText: response.statusText,
          errorText,
        });
        throw new Error(`Failed to get user profile: ${response.statusText} - ${errorText}`);
      }

      const profile = (await response.json()) as PingFedProfile;
      logger.info('Successfully retrieved user profile', {
        sub: profile.sub,
        email: profile.email,
        name: profile.name,
      });
      return profile;
    } catch (error) {
      logger.error('Error fetching PingFed user profile:', error);
      throw error;
    }
  }

  getLoginUrl(): string {
    return '/api/auth/pingfed';
  }

  getCallbackUrl(): string {
    return '/api/auth/pingfed/callback';
  }
}
