import passport from 'passport';
import { Strategy as GitHubStrategy } from 'passport-github2';
import { authConfig } from '../../config/auth';
import { logger } from '../../utils/logger';
import { User, UserRole, AuthProvider, GitHubProfile } from '../../types/api';
import { v4 as uuidv4 } from 'uuid';

export class GitHubAuthProvider {
  private userStore = new Map<string, User>();

  configure(): void {
    if (!authConfig.github.enabled) {
      return;
    }

    logger.info('Configuring GitHub OAuth provider');

    passport.use(new GitHubStrategy({
      clientID: authConfig.github.clientId,
      clientSecret: authConfig.github.clientSecret,
      callbackURL: authConfig.github.callbackURL
    }, async (accessToken: string, refreshToken: string, profile: GitHubProfile, done: Function) => {
      try {
        logger.info('GitHub OAuth callback received', { 
          githubId: profile.id, 
          username: profile.username 
        });

        // Check if user already exists
        let existingUser = Array.from(this.userStore.values()).find(u => u.githubId === profile.id);
        
        if (existingUser) {
          logger.info('Existing GitHub user found', { userId: existingUser.id });
          return done(null, existingUser);
        }

        // Create new user from GitHub profile
        const newUser: User = {
          id: uuidv4(),
          email: profile.emails?.[0]?.value || `${profile.username}@github.local`,
          firstName: profile.displayName?.split(' ')[0] || profile.username,
          lastName: profile.displayName?.split(' ').slice(1).join(' ') || '',
          role: UserRole.USER,
          isActive: true,
          authProvider: AuthProvider.GITHUB,
          githubId: profile.id,
          username: profile.username,
          displayName: profile.displayName || profile.username,
          avatar: profile.photos?.[0]?.value || '',
          profileUrl: profile.profileUrl,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Store user
        this.userStore.set(newUser.id, newUser);

        // Also store in auth manager (import it to avoid circular dependency)
        const { authManager } = await import('../authManager');
        authManager.storeUser(newUser);

        logger.info('New user created from GitHub OAuth', { 
          userId: newUser.id, 
          username: newUser.username,
          displayName: newUser.displayName 
        });

        return done(null, newUser);
      } catch (error) {
        logger.error('Error in GitHub OAuth strategy:', error);
        return done(error, null);
      }
    }));
  }

  isEnabled(): boolean {
    return authConfig.github.enabled;
  }

  getLoginUrl(): string {
    return '/api/auth/github';
  }

  getCallbackUrl(): string {
    return '/api/auth/github/callback';
  }

  getName(): string {
    return 'GitHub';
  }

  getIcon(): string {
    return 'github';
  }
} 