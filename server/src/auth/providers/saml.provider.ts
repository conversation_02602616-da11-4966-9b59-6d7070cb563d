import passport from 'passport';
import { Strategy as SamlStrategy } from '@node-saml/passport-saml';
import { authConfig } from '../../config/auth';
import { logger } from '../../utils/logger';
import { User, UserRole, AuthProvider, SamlProfile } from '../../types/api';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

export class SamlAuthProvider {
  private userStore = new Map<string, User>();

  configure(): void {
    if (!authConfig.saml.enabled) {
      return;
    }

    logger.info('Configuring SAML provider');

    // Read the certificate file
    const certPath = path.join(__dirname, '../../../cert/oneview-ee-saml-connection-idp-cert.crt');
    
    let cert: string;
    try {
      cert = fs.readFileSync(certPath, 'utf8');
    } catch (error) {
      logger.error('Error reading SAML certificate:', error);
      throw new Error('SAML certificate not found');
    }

    passport.use(new SamlStrategy({
      callbackUrl: authConfig.saml.callbackURL,
      entryPoint: authConfig.saml.entryPoint,
      issuer: authConfig.saml.issuer,
      idpCert: cert,
      acceptedClockSkewMs: -1,
      disableRequestedAuthnContext: true,
      forceAuthn: false,
      skipRequestCompression: true,
      authnRequestBinding: 'HTTP-POST',
      signatureAlgorithm: 'sha256',
      digestAlgorithm: 'sha256',
      validateInResponseTo: undefined,
      requestIdExpirationPeriodMs: 28800000, // 8 hours
      logoutUrl: authConfig.saml.logoutUrl,
      logoutCallbackUrl: authConfig.saml.logoutCallbackUrl,
      wantAssertionsSigned: false,
      wantAuthnResponseSigned: false,
      maxAssertionAgeMs: 3600000, // 1 hour
      disableRequestAcsUrl: false,
      allowCreate: true
    }, async (profile: any, done: Function) => {
      try {
        logger.info('SAML authentication callback received', { 
          nameId: profile.nameId,
          email: profile.email || profile['SAML_MAIL'],
          attributes: profile
        });

        // Extract user information from SAML response
        const email = profile.email || profile['SAML_MAIL'] || profile.nameId;
        const title = profile['SAML_TITLE'] || '';
        
        // Parse name from email or title
        const emailParts = email.split('@')[0].split('.');
        const firstName = emailParts[0] || '';
        const lastName = emailParts.length > 1 ? emailParts.slice(1).join(' ') : '';

        // Check if user already exists
        let existingUser = Array.from(this.userStore.values()).find(u => 
          u.samlId === profile.nameId || u.email === email
        );
        
        if (existingUser) {
          logger.info('Existing SAML user found', { userId: existingUser.id });
          return done(null, existingUser);
        }

        // Create new user from SAML profile
        const newUser: User = {
          id: uuidv4(),
          email: email,
          firstName: firstName,
          lastName: lastName,
          role: UserRole.USER,
          isActive: true,
          authProvider: AuthProvider.SAML,
          samlId: profile.nameId,
          displayName: `${firstName} ${lastName}`.trim() || email,
          jobTitle: title,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Store user (in production, save to database)
        this.userStore.set(newUser.id, newUser);

        // Also store in auth manager (import it to avoid circular dependency)
        const { authManager } = await import('../authManager');
        authManager.storeUser(newUser);

        logger.info('New user created from SAML authentication', { 
          userId: newUser.id, 
          email: newUser.email,
          displayName: newUser.displayName
        });

        return done(null, newUser);
      } catch (error) {
        logger.error('Error in SAML authentication strategy:', error);
        return done(error, null);
      }
    }, async (profile: any, done: Function) => {
      // Handle logout
      try {
        logger.info('SAML logout callback received', { nameId: profile.nameId });
        return done(null, true);
      } catch (error) {
        logger.error('Error in SAML logout callback:', error);
        return done(error, null);
      }
    }));
  }

  isEnabled(): boolean {
    return authConfig.saml.enabled;
  }

  getLoginUrl(): string {
    return '/api/auth/saml';
  }

  getCallbackUrl(): string {
    return '/api/auth/saml/callback';
  }

  getLogoutUrl(): string {
    return '/api/auth/saml/logout';
  }

  getName(): string {
    return 'SAML';
  }

  getIcon(): string {
    return 'saml';
  }

  // Method to get SAML metadata
  getMetadata(): string {
    if (!authConfig.saml.enabled) {
      throw new Error('SAML provider not enabled');
    }

    // This would typically generate service provider metadata
    // For now, return a basic metadata structure
    return `<?xml version="1.0" encoding="UTF-8"?>
<md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata" entityID="${authConfig.saml.issuer}">
  <md:SPSSODescriptor AuthnRequestsSigned="false" WantAssertionsSigned="false" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
    <md:AssertionConsumerService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="${authConfig.saml.callbackURL}" index="1" isDefault="true"/>
  </md:SPSSODescriptor>
</md:EntityDescriptor>`;
  }
} 