import { ConfidentialClientApplication, Configuration, AuthenticationResult } from '@azure/msal-node';
import { authConfig } from '../../config/auth';
import { logger } from '../../utils/logger';
import { User, UserRole, AuthProvider, AzureProfile } from '../../types/api';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

export class AzureAuthProvider {
  private userStore = new Map<string, User>();
  private msalInstance: ConfidentialClientApplication | null = null;
  private codeVerifierStore = new Map<string, string>();

  configure(): void {
    if (!authConfig.azure.enabled) {
      return;
    }

    logger.info('Configuring Azure AD OAuth provider');

    const msalConfig: Configuration = {
      auth: {
        clientId: authConfig.azure.clientId,
        clientSecret: authConfig.azure.clientSecret,
        authority: `https://login.microsoftonline.com/${authConfig.azure.tenantId}`,
      },
      system: {
        loggerOptions: {
          loggerCallback: (level, message, containsPii) => {
            if (containsPii) return;
            logger.debug(`MSAL: ${message}`);
          },
          piiLoggingEnabled: false,
          logLevel: 3, // LogLevel.Info
        },
      },
    };

    this.msalInstance = new ConfidentialClientApplication(msalConfig);
  }

  getAuthCodeUrl(state?: string): Promise<string> {
    if (!this.msalInstance) {
      throw new Error('Azure AD provider not configured');
    }

    const codeVerifier = this.generateCodeVerifier();
    if (state) {
      this.codeVerifierStore.set(state, codeVerifier);
    }

    const authCodeUrlParameters = {
      scopes: ['openid', 'profile', 'email', 'user.read'],
      redirectUri: authConfig.azure.callbackURL,
      state: state,
      codeChallenge: this.generateCodeChallenge(codeVerifier),
      codeChallengeMethod: 'S256'
    };

    return this.msalInstance.getAuthCodeUrl(authCodeUrlParameters);
  }

  private generateCodeChallenge(codeVerifier: string): string {
    const base64Digest = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64');
    return base64Digest
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  private generateCodeVerifier(): string {
    return crypto
      .randomBytes(32)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  private getStoredCodeVerifier(state?: string): string {
    if (!state) {
      throw new Error('State parameter is required for PKCE');
    }
    const codeVerifier = this.codeVerifierStore.get(state);
    if (!codeVerifier) {
      throw new Error('No code verifier found for state');
    }
    this.codeVerifierStore.delete(state); // Clean up after use
    return codeVerifier;
  }

  async handleCallback(code: string, state?: string): Promise<User> {
    if (!this.msalInstance) {
      throw new Error('Azure AD provider not configured');
    }

    try {
      const tokenRequest = {
        code: code,
        scopes: ['openid', 'profile', 'email', 'user.read'],
        redirectUri: authConfig.azure.callbackURL,
        codeVerifier: this.getStoredCodeVerifier(state)
      };

      const response: AuthenticationResult = await this.msalInstance.acquireTokenByCode(tokenRequest);
      
      if (!response.account) {
        throw new Error('No account information received from Azure AD');
      }

      logger.info('Azure AD OAuth callback received', { 
        azureId: response.account.homeAccountId, 
        upn: response.account.username 
      });

      // Get user profile from Microsoft Graph
      const userProfile = await this.getUserProfile(response.accessToken!);
      
      // Get user photo from Microsoft Graph (if available)
      const avatarUrl = await this.getUserPhoto(response.accessToken!);

      // Check if user already exists
      let existingUser = Array.from(this.userStore.values()).find(u => u.azureId === response.account!.homeAccountId);
      
      if (existingUser) {
        logger.info('Existing Azure AD user found', { userId: existingUser.id });
        return existingUser;
      }

      // Create new user from Azure AD profile
      const newUser: User = {
        id: uuidv4(),
        email: userProfile.mail || userProfile.userPrincipalName,
        firstName: userProfile.givenName || '',
        lastName: userProfile.surname || '',
        role: UserRole.USER,
        isActive: true,
        authProvider: AuthProvider.AZURE,
        azureId: response.account.homeAccountId,
        upn: response.account.username,
        tenantId: response.account.tenantId,
        displayName: userProfile.displayName,
        avatar: avatarUrl,
        jobTitle: userProfile.jobTitle,
        department: userProfile.department,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Store user (in production, save to database)
      this.userStore.set(newUser.id, newUser);

      // Also store in auth manager (import it to avoid circular dependency)
      const { authManager } = await import('../authManager');
      authManager.storeUser(newUser);

      logger.info('New user created from Azure AD OAuth', { 
        userId: newUser.id, 
        upn: newUser.upn,
        displayName: newUser.displayName
      });

      return newUser;
    } catch (error) {
      logger.error('Error in Azure AD OAuth callback:', error);
      throw error;
    }
  }

  private async getUserProfile(accessToken: string): Promise<AzureProfile> {
    try {
      const response = await fetch('https://graph.microsoft.com/v1.0/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to get user profile: ${response.statusText}`);
      }

      const profile = await response.json() as {
        id: string;
        displayName: string;
        mail: string;
        userPrincipalName: string;
        givenName: string;
        surname: string;
        jobTitle?: string;
        department?: string;
      };
      
      return {
        id: profile.id,
        displayName: profile.displayName,
        mail: profile.mail,
        userPrincipalName: profile.userPrincipalName,
        givenName: profile.givenName,
        surname: profile.surname,
        jobTitle: profile.jobTitle,
        department: profile.department
      };
    } catch (error) {
      logger.error('Error fetching Azure AD user profile:', error);
      throw error;
    }
  }

  private async getUserPhoto(accessToken: string): Promise<string | undefined> {
    try {
      const response = await fetch('https://graph.microsoft.com/v1.0/me/photo/$value', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!response.ok) {
        // User doesn't have a profile photo or permission denied
        return undefined;
      }

      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');
      
      // Create a data URL for the image
      const mimeType = response.headers.get('content-type') || 'image/jpeg';
      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      logger.error('Error fetching Azure AD user photo:', error);
      return undefined;
    }
  }

  isEnabled(): boolean {
    return authConfig.azure.enabled;
  }

  getLoginUrl(): string {
    return '/api/auth/azure';
  }

  getCallbackUrl(): string {
    return '/api/auth/azure/callback';
  }

  getName(): string {
    return 'Microsoft';
  }

  getIcon(): string {
    return 'microsoft';
  }
} 