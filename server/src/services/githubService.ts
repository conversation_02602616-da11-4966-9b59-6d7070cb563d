import axios from 'axios';
import { getGitHubConfig } from '../config/github';

export interface GitHubPullRequest {
  number: number;
  title: string;
  author: string;
  avatar_url?: string;
  status: 'open' | 'closed' | 'merged';
  url: string;
  created_at: string;
  closed_at?: string;
  merged_at?: string;
  base_branch: string;
  head_branch: string;
}

export interface GitHubUser {
  login: string;
  name?: string;
  email?: string;
  avatar_url: string;
  html_url: string;
}

export interface PullRequestFilters {
  state?: 'open' | 'closed' | 'all';
  base?: string;
  startDate?: string;
  endDate?: string;
  per_page?: number;
}

// GitHub API response interfaces
interface GitHubPRResponse {
  number: number;
  title: string;
  state: string;
  html_url: string;
  created_at: string;
  closed_at?: string;
  merged_at?: string;
  merged?: boolean;
  user: {
    login: string;
    avatar_url: string;
  };
  base: {
    ref: string;
  };
  head: {
    ref: string;
  };
}

interface GitHubUserResponse {
  login: string;
  name?: string;
  email?: string;
  avatar_url: string;
  html_url: string;
}

// Health check cache for adaptive timeouts
interface ApiHealthResult {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}

const healthCache = new Map<string, { result: ApiHealthResult; timestamp: number }>();
const HEALTH_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

class GitHubService {
  /**
   * Check GitHub API health for adaptive timeout configuration
   */
  private async checkGitHubHealth(): Promise<ApiHealthResult> {
    const cacheKey = 'github-health';
    const cached = healthCache.get(cacheKey);

    // Return cached result if still valid
    if (cached && Date.now() - cached.timestamp < HEALTH_CACHE_DURATION) {
      return cached.result;
    }

    const startTime = Date.now();

    try {
      const config = getGitHubConfig();
      const response = await axios.get(`${config.baseUrl}/rate_limit`, {
        headers: config.headers,
        timeout: 3000, // Quick health check timeout
        validateStatus: (status) => status < 500,
      });

      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: true,
        responseTime,
      };

      // Cache the result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[GitHub] Health check: API healthy (${responseTime}ms)`);

      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: false,
        responseTime,
        error: error.message || 'Unknown error',
      };

      // Cache the negative result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[GitHub] Health check: API unhealthy (${error.message})`);

      return result;
    }
  }

  /**
   * Get adaptive timeout based on GitHub API health
   */
  private async getAdaptiveTimeout(): Promise<number> {
    try {
      const healthResult = await this.checkGitHubHealth();

      if (healthResult.isHealthy) {
        // API is healthy - use longer timeout for better reliability
        if (healthResult.responseTime < 1000) {
          console.log('[GitHub] Using 30s timeout - API is responsive');
          return 30000; // 30 seconds
        } else {
          console.log('[GitHub] Using 45s timeout - API is slow but healthy');
          return 45000; // 45 seconds
        }
      } else {
        // API is not responding well - use short timeout for quick fallback
        console.log('[GitHub] Using 5s timeout - API appears unhealthy');
        return 30000; // 5 seconds
      }
    } catch (error) {
      // Health check failed - use short timeout
      console.log('[GitHub] Using 5s timeout - health check failed');
      return 15000; // 5 seconds
    }
  }

  async getRepository(fullRepoPath: string) {
    try {
      const config = getGitHubConfig();
      const timeout = await this.getAdaptiveTimeout();

      const response = await axios.get(`${config.baseUrl}/repos/${fullRepoPath}`, {
        headers: config.headers,
        timeout,
      });
      return response.data;
    } catch (error) {
      console.error(`[GitHub] Failed to get repository ${fullRepoPath}:`, error);
      throw error;
    }
  }

  async getBranches(fullRepoPath: string) {
    try {
      const config = getGitHubConfig();
      const timeout = await this.getAdaptiveTimeout();

      const response = await axios.get(`${config.baseUrl}/repos/${fullRepoPath}/branches`, {
        headers: config.headers,
        timeout,
      });
      return response.data;
    } catch (error) {
      console.error(`[GitHub] Failed to get branches for ${fullRepoPath}:`, error);
      throw error;
    }
  }

  async getPullRequestsByDateRange(
    fullRepoPath: string,
    filters: PullRequestFilters = {}
  ): Promise<GitHubPullRequest[]> {
    try {
      const { startDate, endDate, state = 'all', base = 'main', per_page = 100 } = filters;

      console.log(`[GitHub] Fetching PRs for ${fullRepoPath}:`, {
        startDate,
        endDate,
        state,
        base,
      });

      const config = getGitHubConfig();
      const timeout = await this.getAdaptiveTimeout();
      const url = `${config.baseUrl}/repos/${fullRepoPath}/pulls`;

      const response = await axios.get<GitHubPRResponse[]>(url, {
        headers: config.headers,
        params: {
          state,
          base,
          sort: 'created',
          direction: 'desc',
          per_page,
        },
        timeout,
      });

      console.log(`[GitHub] Received ${response.data.length} PRs from GitHub API`);

      // Filter by date range and process PRs
      const prsWithDetails = await Promise.all(
        response.data
          .filter((pr: GitHubPRResponse) => {
            if (!startDate && !endDate) return true;

            const prDate = new Date(pr.created_at);
            const isInRange =
              (!startDate || prDate >= new Date(startDate)) &&
              (!endDate || prDate <= new Date(endDate));
            return isInRange;
          })
          .map(async (pr: GitHubPRResponse) => {
            try {
              // Use a shorter timeout for detailed PR calls
              const detailTimeout = Math.min(timeout, 30000); // Max 10 seconds for details

              // Get detailed PR information to check merge status
              const detailedPR = await axios.get<GitHubPRResponse>(
                `${config.baseUrl}/repos/${fullRepoPath}/pulls/${pr.number}`,
                {
                  headers: config.headers,
                  timeout: detailTimeout,
                }
              );

              // Get author's full name
              const authorName = await this.getUserFullName(pr.user.login);

              // Determine actual status
              let status: 'open' | 'closed' | 'merged';
              if (detailedPR.data.state === 'open') {
                status = 'open';
              } else if (detailedPR.data.merged) {
                status = 'merged';
              } else {
                status = 'closed';
              }

              return {
                number: pr.number,
                title: pr.title,
                author: authorName,
                avatar_url: pr.user.avatar_url,
                status,
                url: pr.html_url,
                created_at: pr.created_at,
                closed_at: detailedPR.data.closed_at,
                merged_at: detailedPR.data.merged_at,
                base_branch: pr.base.ref,
                head_branch: pr.head.ref,
              } as GitHubPullRequest;
            } catch (error) {
              console.error(`[GitHub] Error processing PR #${pr.number}:`, error);
              // Return basic info if detailed fetch fails
              return {
                number: pr.number,
                title: pr.title,
                author: pr.user.login,
                avatar_url: pr.user.avatar_url,
                status: pr.state as 'open' | 'closed',
                url: pr.html_url,
                created_at: pr.created_at,
                base_branch: pr.base.ref,
                head_branch: pr.head.ref,
              } as GitHubPullRequest;
            }
          })
      );

      const sortedPRs = prsWithDetails.sort(
        (a: GitHubPullRequest, b: GitHubPullRequest) => b.number - a.number
      );
      console.log(`[GitHub] Processed ${sortedPRs.length} PRs`);

      return sortedPRs;
    } catch (error) {
      console.error('[GitHub] Failed to get PRs:', error);
      throw error;
    }
  }

  async getUserInfo(username: string): Promise<GitHubUser> {
    try {
      const config = getGitHubConfig();
      const timeout = Math.min(await this.getAdaptiveTimeout(), 10000); // Max 10s for user info

      const response = await axios.get<GitHubUserResponse>(`${config.baseUrl}/users/${username}`, {
        headers: config.headers,
        timeout,
      });

      return {
        login: response.data.login,
        name: response.data.name,
        email: response.data.email,
        avatar_url: response.data.avatar_url,
        html_url: response.data.html_url,
      };
    } catch (error) {
      console.error(`[GitHub] Failed to get user info for ${username}:`, error);
      throw error;
    }
  }

  async getUserFullName(login: string): Promise<string> {
    try {
      const config = getGitHubConfig();
      const timeout = Math.min(await this.getAdaptiveTimeout(), 10000); // Max 10s for user info

      const response = await axios.get<GitHubUserResponse>(`${config.baseUrl}/users/${login}`, {
        headers: config.headers,
        timeout,
      });

      const fullNameWithId = response.data.name || login;

      // Extract name from "Name - ID" format if present
      const nameMatch = fullNameWithId.match(/^(.*?)\s*-\s*[a-z0-9]+$/i);
      const extractedName = nameMatch ? nameMatch[1].trim() : fullNameWithId;

      return extractedName;
    } catch (error) {
      console.error(`[GitHub] Failed to get user details for ${login}:`, error);
      return login; // Fallback to login name
    }
  }

  async getCommitsByDateRange(
    fullRepoPath: string,
    filters: { startDate?: string; endDate?: string; branch?: string } = {}
  ) {
    try {
      const { startDate, endDate, branch = 'main' } = filters;
      const config = getGitHubConfig();
      const timeout = await this.getAdaptiveTimeout();

      const response = await axios.get(`${config.baseUrl}/repos/${fullRepoPath}/commits`, {
        headers: config.headers,
        params: {
          sha: branch,
          since: startDate,
          until: endDate,
          per_page: 100,
        },
        timeout,
      });

      return response.data;
    } catch (error) {
      console.error(`[GitHub] Failed to get commits for ${fullRepoPath}:`, error);
      throw error;
    }
  }

  async getBranchDetails(fullRepoPath: string, branchName: string) {
    try {
      const config = getGitHubConfig();
      const timeout = Math.min(await this.getAdaptiveTimeout(), 30000); // Max 10s for branch details

      const response = await axios.get(
        `${config.baseUrl}/repos/${fullRepoPath}/branches/${branchName}`,
        {
          headers: config.headers,
          timeout,
        }
      );

      return response.data;
    } catch (error) {
      console.error(`[GitHub] Failed to get details for branch ${branchName}:`, error);
      throw error;
    }
  }

  async compareBranches(fullRepoPath: string, baseBranch: string, compareBranch: string) {
    try {
      const config = getGitHubConfig();
      const timeout = await this.getAdaptiveTimeout();

      const response = await axios.get(
        `${config.baseUrl}/repos/${fullRepoPath}/compare/${baseBranch}...${compareBranch}`,
        {
          headers: config.headers,
          timeout,
        }
      );

      return response.data;
    } catch (error) {
      console.error(`[GitHub] Failed to compare branches in ${fullRepoPath}:`, error);
      throw error;
    }
  }
}

export const githubService = new GitHubService();
