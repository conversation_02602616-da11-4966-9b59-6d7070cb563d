import * as yaml from 'js-yaml';
import * as fs from 'fs';
import * as path from 'path';
import { Team } from '../types/database';
import { v4 as uuidv4 } from 'uuid';

export class YamlLoaderService {
  private teamsOnboardingPath: string;

  constructor() {
    // Path to teams-onboarding directory relative to server root
    this.teamsOnboardingPath = path.join(__dirname, '../../../teams-onboarding');
  }

  /**
   * Load all team YAML files from the teams-onboarding directory
   */
  async loadAllTeams(): Promise<Team[]> {
    try {
      console.log('📂 Loading teams from YAML files...');
      console.log(`📍 Looking in directory: ${this.teamsOnboardingPath}`);
      
      if (!fs.existsSync(this.teamsOnboardingPath)) {
        console.warn('⚠️ Teams onboarding directory not found');
        return [];
      }

      const files = fs.readdirSync(this.teamsOnboardingPath);
      const yamlFiles = files.filter(file => file.endsWith('.yaml') || file.endsWith('.yml'));

      if (yamlFiles.length === 0) {
        console.warn('⚠️ No YAML files found in teams-onboarding directory');
        return [];
      }

      console.log(`📄 Found ${yamlFiles.length} YAML files: ${yamlFiles.join(', ')}`);

      const teams: Team[] = [];

      for (const file of yamlFiles) {
        try {
          const team = await this.loadTeamFromFile(file);
          if (team) {
            teams.push(team);
            console.log(`✅ Loaded team: ${team.teamName}`);
          }
        } catch (error) {
          console.error(`❌ Failed to load team from ${file}:`, error);
        }
      }

      console.log(`🎉 Successfully loaded ${teams.length} teams from YAML files`);
      return teams;
    } catch (error) {
      console.error('❌ Failed to load teams from YAML files:', error);
      throw error;
    }
  }

  /**
   * Validate metadata has required fields for single-platform structure
   */
  private validateMetadataStructure(metadata: any, filename: string): boolean {
    // Required fields for all teams
    const requiredFields = [
      'platform',
      'platform_type', 
      'github_repo',
      'repo_default_branch',
      'looper_url',
      'sonarqube_url',
      'sonarqube_project_id'
    ];

    for (const field of requiredFields) {
      if (!metadata[field] || typeof metadata[field] !== 'string') {
        console.warn(`⚠️ Invalid metadata in ${filename}: missing or invalid ${field}`);
        return false;
      }
    }

    // Validate platform values
    const validPlatforms = ['ios', 'android', 'web', 'backend', 'desktop', 'mobile_web'];
    if (!validPlatforms.includes(metadata.platform)) {
      console.warn(`⚠️ Invalid platform in ${filename}: ${metadata.platform} is not valid`);
      return false;
    }

    // Validate platform_type values
    const validPlatformTypes = ['mobile', 'web', 'backend'];
    if (!validPlatformTypes.includes(metadata.platform_type)) {
      console.warn(`⚠️ Invalid platform_type in ${filename}: ${metadata.platform_type} is not valid`);
      return false;
    }

    // Validate platform and platform_type consistency
    const platformTypeMapping: { [key: string]: string } = {
      'ios': 'mobile',
      'android': 'mobile',
      'web': 'web',
      'desktop': 'web',
      'mobile_web': 'web',
      'backend': 'backend'
    };

    if (platformTypeMapping[metadata.platform] !== metadata.platform_type) {
      console.warn(`⚠️ Inconsistent platform/platform_type in ${filename}: ${metadata.platform} should have platform_type ${platformTypeMapping[metadata.platform]}`);
      return false;
    }

    // Validate required URL fields
    const urlFields = ['github_repo', 'looper_url', 'sonarqube_url'];
    for (const field of urlFields) {
      if (!metadata[field] || typeof metadata[field] !== 'string') {
        console.warn(`⚠️ Invalid YAML structure in ${filename}: metadata.${field} must be a non-empty string`);
        return false;
      }
    }

    // Validate optional URL fields
    const optionalUrlFields = ['deployment_url', 'app_store_url', 'play_store_url'];
    for (const field of optionalUrlFields) {
      if (metadata[field] && !this.isValidUrl(metadata[field])) {
        console.warn(`⚠️ Invalid URL in ${filename}: ${field} must be a valid URL`);
        return false;
      }
    }

    return true;
  }

  /**
   * Load a single team from a YAML file with single-platform validation
   */
  private async loadTeamFromFile(filename: string): Promise<Team | null> {
    try {
      const filePath = path.join(this.teamsOnboardingPath, filename);
      const fileContent = fs.readFileSync(filePath, 'utf8');
      
      // Parse YAML content
      const yamlData = yaml.load(fileContent) as any;
      
      if (!yamlData || !yamlData.teamName) {
        console.warn(`⚠️ Invalid YAML structure in ${filename}: missing teamName`);
        return null;
      }

      // Validate required fields
      if (!yamlData.metadata || !yamlData.roles || !yamlData.release_types) {
        console.warn(`⚠️ Invalid YAML structure in ${filename}: missing required fields (metadata, roles, release_types)`);
        return null;
      }

      // Validate metadata structure for single-platform teams
      if (!this.validateMetadataStructure(yamlData.metadata, filename)) {
        return null;
      }

      // Validate roles structure
      if (!this.validateRolesStructure(yamlData.roles, filename)) {
        return null;
      }

      // Validate release types structure
      if (!this.validateReleaseTypesStructure(yamlData.release_types, filename)) {
        return null;
      }

      // Create team object with single-platform structure
      const team: Team = {
        id: uuidv4(),
        teamName: yamlData.teamName,
        metadata: {
          // Platform identification
          platform: yamlData.metadata.platform,
          platform_type: yamlData.metadata.platform_type,
          
          // Core configuration
          github_repo: yamlData.metadata.github_repo,
          repo_default_branch: yamlData.metadata.repo_default_branch,
          looper_url: yamlData.metadata.looper_url,
          sonarqube_url: yamlData.metadata.sonarqube_url,
          sonarqube_project_id: yamlData.metadata.sonarqube_project_id,
          
          // Optional fields
          ...(yamlData.metadata.deployment_url && { deployment_url: yamlData.metadata.deployment_url }),
          ...(yamlData.metadata.app_store_url && { app_store_url: yamlData.metadata.app_store_url }),
          ...(yamlData.metadata.play_store_url && { play_store_url: yamlData.metadata.play_store_url }),
          ...(yamlData.metadata.deployment_environment && { deployment_environment: yamlData.metadata.deployment_environment }),
          ...(yamlData.metadata.slack_webhook && { slack_webhook: yamlData.metadata.slack_webhook }),
          
          // Include any additional metadata fields
          ...Object.keys(yamlData.metadata)
            .filter(key => ![
              'platform', 'platform_type', 'github_repo', 'repo_default_branch', 
              'looper_url', 'sonarqube_url', 'sonarqube_project_id',
              'deployment_url', 'app_store_url', 'play_store_url', 
              'deployment_environment', 'slack_webhook'
            ].includes(key))
            .reduce((acc, key) => ({ ...acc, [key]: yamlData.metadata[key] }), {})
        },
        roles: yamlData.roles,
        release_types: yamlData.release_types
      };

      return team;
    } catch (error) {
      console.error(`❌ Error parsing YAML file ${filename}:`, error);
      return null;
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate roles structure - can be either string[] or {[subTeam]: string[]}
   */
  private validateRolesStructure(roles: any, filename: string): boolean {
    if (!roles || typeof roles !== 'object') {
      console.warn(`⚠️ Invalid roles structure in ${filename}: roles must be an object`);
      return false;
    }

    for (const [roleName, roleData] of Object.entries(roles)) {
      if (Array.isArray(roleData)) {
        // Simple role with array of emails
        if (!roleData.every(email => typeof email === 'string' && this.isValidEmail(email))) {
          console.warn(`⚠️ Invalid role data in ${filename}: ${roleName} contains invalid emails`);
          return false;
        }
      } else if (typeof roleData === 'object' && roleData !== null) {
        // Role with sub-teams
        for (const [subTeamName, subTeamData] of Object.entries(roleData)) {
          if (!Array.isArray(subTeamData) || !subTeamData.every(email => typeof email === 'string' && this.isValidEmail(email))) {
            console.warn(`⚠️ Invalid sub-team data in ${filename}: ${roleName}.${subTeamName} contains invalid emails`);
            return false;
          }
        }
      } else {
        console.warn(`⚠️ Invalid role structure in ${filename}: ${roleName} must be either an array or an object`);
        return false;
      }
    }

    return true;
  }

  /**
   * Validate release types structure
   */
  private validateReleaseTypesStructure(releaseTypes: any, filename: string): boolean {
    if (!releaseTypes || typeof releaseTypes !== 'object') {
      console.warn(`⚠️ Invalid release_types structure in ${filename}: must be an object`);
      return false;
    }

    for (const [releaseTypeName, releaseTypeData] of Object.entries(releaseTypes)) {
      const data = releaseTypeData as any;
      
      // Validate signoff_order
      if (!Array.isArray(data.signoff_order) || data.signoff_order.length === 0) {
        console.warn(`⚠️ Invalid signoff_order in ${filename}: ${releaseTypeName} must have a non-empty array`);
        return false;
      }

      // Validate enforcement
      if (!data.enforcement || typeof data.enforcement !== 'object') {
        console.warn(`⚠️ Invalid enforcement in ${filename}: ${releaseTypeName} must have enforcement rules`);
        return false;
      }

      // Validate that each role in signoff_order has enforcement rules
      for (const role of data.signoff_order) {
        if (!data.enforcement[role] && !data.enforcement.blocking_conditions) {
          console.warn(`⚠️ Missing enforcement rules in ${filename}: ${releaseTypeName} missing rules for role ${role}`);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Basic email validation
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get all role names from a team (flattened)
   */
  public getTeamRoles(team: Team): string[] {
    return Object.keys(team.roles);
  }

  /**
   * Get all sub-teams for a specific role
   */
  public getRoleSubTeams(team: Team, roleName: string): string[] {
    const roleData = team.roles[roleName];
    if (Array.isArray(roleData)) {
      return []; // No sub-teams, just a flat role
    } else if (typeof roleData === 'object' && roleData !== null) {
      return Object.keys(roleData);
    }
    return [];
  }

  /**
   * Get all approvers for a role (including sub-teams)
   */
  public getRoleApprovers(team: Team, roleName: string): string[] {
    const roleData = team.roles[roleName];
    if (Array.isArray(roleData)) {
      return roleData;
    } else if (typeof roleData === 'object' && roleData !== null) {
      // Flatten all sub-team approvers
      return Object.values(roleData).flat();
    }
    return [];
  }
}

export default new YamlLoaderService(); 