/**
 * Azure OpenAI Client with o3-mini Model Integration
 * Clean implementation with signature-based authentication for Walmart LLM Gateway
 */

import crypto from 'crypto';
import axios from 'axios';
import fs from 'fs';
import path from 'path';

export interface AzureOpenAIConfig {
  consumerId: string;
  keyVersion: string;
  privateKey: string;
  baseUrl: string;
  model: string;
  modelVersion: string;
  apiVersion: string;
  environment: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionRequest {
  messages: ChatMessage[];
  maxTokens?: number;
  temperature?: number;
  streaming?: boolean;
}

export interface ChatCompletionResponse {
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class AzureOpenAIService {
  private config: AzureOpenAIConfig;

  constructor(config: AzureOpenAIConfig) {
    this.config = config;
  }

  /**
   * Generate signature and timestamp for authentication
   * Matches Python implementation exactly: digest = SHA256.new(); digest.update(data.encode("utf-8")); sign = PKCS1_v1_5.new(rsa_key).sign(digest)
   */
  private generateSignatureAndTimestamp(): { signature: string; timestamp: string } {
    const timestamp = Date.now().toString();
    const data = `${this.config.consumerId}\n${timestamp}\n${this.config.keyVersion}\n`;

    // Create SHA256 hash of the data exactly like Python
    const digest = crypto.createHash('sha256');
    digest.update(data, 'utf-8');
    const hashDigest = digest.digest();

    // Create RSA private key with proper formatting
    const privateKeyPem = `-----BEGIN RSA PRIVATE KEY-----\n${this.config.privateKey}\n-----END RSA PRIVATE KEY-----`;

    // Try different signing approaches to match Python's PKCS1_v1_5
    let signature: Buffer;

    try {
      // Method 1: Sign the original data with SHA256 (let Node.js handle the hashing)
      signature = crypto.sign('sha256', Buffer.from(data, 'utf-8'), {
        key: privateKeyPem,
        padding: crypto.constants.RSA_PKCS1_PADDING,
      });

      // Successfully signed with Method 1
    } catch (error) {
      // Fallback to Method 2: Sign the pre-computed hash
      signature = crypto.sign(null, hashDigest, {
        key: privateKeyPem,
        padding: crypto.constants.RSA_PKCS1_PADDING,
      });
    }

    return {
      signature: signature.toString('base64'),
      timestamp,
    };
  }

  /**
   * Generate headers for Walmart LLM Gateway authentication
   */
  private generateHeaders(): Record<string, string> {
    const { signature, timestamp } = this.generateSignatureAndTimestamp();

    const headers = {
      'WM_CONSUMER.ID': this.config.consumerId,
      'WM_SVC.NAME': 'WMTLLMGATEWAY',
      'WM_SVC.ENV': this.config.environment,
      'WM_SEC.KEY_VERSION': this.config.keyVersion,
      'WM_SEC.AUTH_SIGNATURE': signature,
      'WM_CONSUMER.INTIMESTAMP': timestamp,
      'Content-Type': 'application/json',
    };

    // Headers generated successfully

    return headers;
  }

  /**
   * Create chat completion using Azure OpenAI o3-mini model
   */
  public async createChatCompletion(
    request: ChatCompletionRequest
  ): Promise<ChatCompletionResponse> {
    const headers = this.generateHeaders();

    const payload = {
      model: this.config.model,
      'model-version': this.config.modelVersion,
      'api-version': this.config.apiVersion,
      task: 'chat/completions',
      streaming: request.streaming || false,
      'model-params': {
        messages: request.messages.map((msg) => ({
          role: msg.role,
          content: [
            {
              type: 'text',
              text: msg.content,
            },
          ],
        })),
        max_completion_tokens: request.maxTokens || 1000,
        // Note: temperature is not supported by o3-mini model
      },
    };

    // Payload prepared for Azure OpenAI request

    try {
      // Set CA bundle if available (equivalent to Python's ca-bundle.crt logic)
      // Try multiple possible locations for the certificate bundle
      const possibleCaPaths = [
        path.join(process.cwd(), 'server', 'cert', 'ca-bundle.crt'), // Docker container path
        path.join(process.cwd(), 'cert', 'ca-bundle.crt'), // Local development path
        path.join(__dirname, '..', '..', 'cert', 'ca-bundle.crt'), // Relative to compiled JS
        '/opt/app/server/cert/ca-bundle.crt', // Absolute Docker path
      ];

      let caBundlePath: string | null = null;
      for (const possiblePath of possibleCaPaths) {
        if (fs.existsSync(possiblePath)) {
          caBundlePath = possiblePath;
          console.log(`✅ Found CA bundle at: ${caBundlePath}`);
          break;
        }
      }

      let httpsAgent;

      if (caBundlePath) {
        // Use custom CA bundle for SSL verification
        httpsAgent = new (require('https').Agent)({
          ca: fs.readFileSync(caBundlePath),
          rejectUnauthorized: true,
        });
      } else {
        console.warn(
          '⚠️ CA bundle not found, SSL verification may fail in private cloud environments'
        );

        // Check if we should disable SSL verification for private cloud (not recommended for production)
        const disableSSLVerification = process.env.DISABLE_SSL_VERIFICATION === 'true';

        if (disableSSLVerification) {
          console.warn('🚨 SSL verification disabled - this is not recommended for production!');
          httpsAgent = new (require('https').Agent)({
            rejectUnauthorized: false,
          });
        }
      }

      const axiosConfig: any = {
        headers,
        timeout: 30000, // 30 second timeout
        validateStatus: (status: number) => status < 500, // Don't throw on 4xx errors
      };

      // Add custom CA bundle if available
      if (httpsAgent) {
        axiosConfig.httpsAgent = httpsAgent;
      }

      const response = await axios.post<ChatCompletionResponse>(
        this.config.baseUrl,
        payload,
        axiosConfig
      );

      if (response.status !== 200) {
        throw new Error(`Azure OpenAI API error: ${response.status} - ${response.statusText}`);
      }

      return response.data;
    } catch (error: any) {
      if (error.response) {
        const errorMessage =
          error.response?.data?.error?.message || error.response?.data?.message || error.message;
        throw new Error(`Azure OpenAI request failed: ${errorMessage}`);
      }
      throw new Error(`Azure OpenAI request failed: ${error.message || 'Unknown error'}`);
    }
  }

  /**
   * Simple API for chatbots - returns just the AI response text
   */
  public async getAIResponse(
    userMessage: string,
    conversationHistory: ChatMessage[] = []
  ): Promise<string> {
    try {
      const messages: ChatMessage[] = [
        ...conversationHistory,
        { role: 'user', content: userMessage },
      ];

      const result = await this.createChatCompletion({
        messages,
        maxTokens: 1500,
        // Note: temperature not supported by o3-mini
      });

      if (result.choices && result.choices.length > 0) {
        return result.choices[0].message.content;
      } else {
        return "Sorry, I couldn't process your request. Please try again.";
      }
    } catch (error) {
      console.error('Azure OpenAI error:', error);
      return `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
    }
  }

  /**
   * Test the connection to Azure OpenAI
   */
  public async testConnection(): Promise<boolean> {
    try {
      const response = await this.getAIResponse('Hello, this is a test message.');
      return response.length > 0 && !response.startsWith('Error:');
    } catch (error) {
      console.error('Azure OpenAI connection test failed:', error);
      return false;
    }
  }
}

/**
 * Factory function to create Azure OpenAI service from environment variables and Key Vault
 */
export async function createAzureOpenAIService(): Promise<AzureOpenAIService> {
  const keyVaultClient = (await import('../utils/keyVaultClient')).default;

  try {
    console.log('🔑 Loading Azure OpenAI configuration from Key Vault...');

    // Load sensitive values from Key Vault
    const [consumerId, privateKey] = await Promise.all([
      keyVaultClient.getSecret('azure-openai-consumer-id'),
      keyVaultClient.getSecret('azure-openai-private-key'),
    ]);

    const config: AzureOpenAIConfig = {
      consumerId: consumerId || process.env.AZURE_OPENAI_CONSUMER_ID || '',
      keyVersion: process.env.AZURE_OPENAI_KEY_VERSION || '1',
      privateKey: privateKey || process.env.AZURE_OPENAI_PRIVATE_KEY || '',
      baseUrl: process.env.AZURE_OPENAI_BASE_URL || '',
      model: process.env.AZURE_OPENAI_MODEL || 'o3-mini',
      modelVersion: process.env.AZURE_OPENAI_MODEL_VERSION || '2025-01-31',
      apiVersion: process.env.AZURE_OPENAI_API_VERSION || '2024-12-01-preview',
      environment: process.env.AZURE_OPENAI_ENVIRONMENT || 'stage',
    };

    // Validate required configuration
    const requiredFields = ['consumerId', 'privateKey', 'baseUrl'];
    const missingFields = requiredFields.filter(
      (field) => !config[field as keyof AzureOpenAIConfig]
    );

    if (missingFields.length > 0) {
      throw new Error(`Missing required Azure OpenAI configuration: ${missingFields.join(', ')}`);
    }

    // Debug logging for configuration
    console.log('🔧 Azure OpenAI Configuration:', {
      consumerId: config.consumerId ? '✅ Set (from Key Vault)' : '❌ Missing',
      keyVersion: config.keyVersion,
      baseUrl: config.baseUrl ? '✅ Set' : '❌ Missing',
      model: config.model,
      modelVersion: config.modelVersion,
      apiVersion: config.apiVersion,
      environment: config.environment,
      privateKeyLength: config.privateKey ? config.privateKey.length : 0,
      privateKeySource: privateKey ? 'Key Vault' : 'Environment Variable',
    });

    console.log('✅ Azure OpenAI configuration loaded successfully from Key Vault');
    return new AzureOpenAIService(config);
  } catch (error) {
    console.error('❌ Failed to load Azure OpenAI configuration from Key Vault:', error);
    console.log('🔄 Falling back to environment variables...');

    // Fallback to environment variables
    const config: AzureOpenAIConfig = {
      consumerId: process.env.AZURE_OPENAI_CONSUMER_ID || '',
      keyVersion: process.env.AZURE_OPENAI_KEY_VERSION || '1',
      privateKey: process.env.AZURE_OPENAI_PRIVATE_KEY || '',
      baseUrl: process.env.AZURE_OPENAI_BASE_URL || '',
      model: process.env.AZURE_OPENAI_MODEL || 'o3-mini',
      modelVersion: process.env.AZURE_OPENAI_MODEL_VERSION || '2025-01-31',
      apiVersion: process.env.AZURE_OPENAI_API_VERSION || '2024-12-01-preview',
      environment: process.env.AZURE_OPENAI_ENVIRONMENT || 'stage',
    };

    // Validate required configuration
    const requiredFields = ['consumerId', 'privateKey', 'baseUrl'];
    const missingFields = requiredFields.filter(
      (field) => !config[field as keyof AzureOpenAIConfig]
    );

    if (missingFields.length > 0) {
      throw new Error(`Missing required Azure OpenAI configuration: ${missingFields.join(', ')}`);
    }

    console.log('⚠️ Using environment variables for Azure OpenAI configuration');
    return new AzureOpenAIService(config);
  }
}
