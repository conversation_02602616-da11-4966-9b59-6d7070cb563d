/**
 * Centralized Confidence Score Service
 * Contains all scoring logic, weightings, and calculation methods
 */

// Default weightings for confidence score calculation
export const DEFAULT_WEIGHTINGS = {
  codeQuality: 20,
  codeCoverage: 20,
  testResults: 20,
  buildStability: 20,
  changeRequests: 10,
  changeLog: 10,
} as const;

// Type definitions
export interface Weightings {
  codeQuality: number;
  codeCoverage: number;
  testResults: number;
  buildStability: number;
  changeRequests: number;
  changeLog: number;
}

export interface ConfidenceMetrics {
  overallScore: number;
  lastCalculated: string;
  weightings: Weightings;
  trending: {
    direction: 'up' | 'down' | 'stable';
    changePercent: number;
    previousScore?: number;
  };
}

export interface ReleaseMetrics {
  codeQuality?: { score: number; [key: string]: any };
  codeCoverage?: { score: number; [key: string]: any };
  testResults?: { score: number; [key: string]: any };
  buildStability?: { score: number; [key: string]: any };
  changeRequests?: { score: number; [key: string]: any };
  changeLog?: { score: number; [key: string]: any };
  changelog?: { score: number; [key: string]: any }; // Alternative naming
}

/**
 * Centralized Confidence Score Service
 */
export class ConfidenceScoreService {
  /**
   * Calculate Build Stability score using direct success rate mapping
   */
  static calculateBuildStabilityScore(buildData: any): number {
    if (!buildData || !buildData.statistics) return 50; // Neutral score for no data

    const stats = buildData.statistics;

    if (stats.total === 0) {
      return 50; // Neutral score for no builds
    }

    // Direct mapping: success rate = metric score (0-100 points)
    // 100% success rate = 100% metric score
    let score = stats.successRate;

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Calculate SonarQube Code Quality score
   */
  static calculateSonarQubeScore(metrics: any): number {
    // Calculate a weighted score based on key metrics
    let score = 100;

    // Deduct points for bugs (high impact)
    const bugs = parseInt(metrics.bugs || '0');
    if (!isNaN(bugs)) {
      score -= Math.min(bugs * 2, 30);
    }

    // Deduct points for vulnerabilities (critical impact)
    const vulnerabilities = parseInt(metrics.vulnerabilities || '0');
    if (!isNaN(vulnerabilities)) {
      score -= Math.min(vulnerabilities * 5, 40);
    }

    // Deduct points for low coverage
    const coverage = parseFloat(metrics.coverage || '0');
    if (!isNaN(coverage) && coverage < 80) {
      score -= Math.min((80 - coverage) * 0.5, 20);
    }

    // Deduct points for high duplication
    const duplication = parseFloat(metrics.duplicated_lines_density || '0');
    if (!isNaN(duplication) && duplication > 5) {
      score -= Math.min((duplication - 5) * 2, 15);
    }

    // Deduct points for code smells (moderate impact)
    const codeSmells = parseInt(metrics.code_smells || '0');
    if (!isNaN(codeSmells)) {
      score -= Math.min(codeSmells * 0.1, 10);
    }

    const finalScore = Math.max(0, Math.min(100, Math.round(score)));
    return isNaN(finalScore) ? 70 : finalScore; // Default to reasonable score if all calculations fail
  }

  /**
   * Calculate Code Coverage score
   */
  static calculateCoverageScore(metrics: any): number {
    const coverage = parseFloat(metrics.coverage || '0');
    const newCoverage = parseFloat(metrics.new_coverage || metrics.coverage || '0');

    // If both coverage values are missing or 0, check if we have test data
    if (coverage === 0 && newCoverage === 0) {
      const totalTests = parseInt(metrics.tests || '0');
      const linesCovered =
        parseInt(metrics.lines_to_cover || '0') - parseInt(metrics.uncovered_lines || '0');
      const linesToCover = parseInt(metrics.lines_to_cover || '0');

      if (linesToCover > 0) {
        const calculatedCoverage = (linesCovered / linesToCover) * 100;
        return Math.max(0, Math.min(100, Math.round(calculatedCoverage)));
      }

      // If we have tests but no coverage data, assume basic coverage
      if (totalTests > 0) {
        return 50; // Basic fallback score when tests exist but coverage is unavailable
      }

      // No coverage data and no tests - return 0
      return 0;
    }

    // Weight overall coverage more heavily than new code coverage
    const weightedScore = coverage * 0.7 + newCoverage * 0.3;
    const finalScore = Math.max(0, Math.min(100, Math.round(weightedScore)));

    // Ensure we return a valid number
    return isNaN(finalScore) ? 0 : finalScore;
  }

  /**
   * Calculate Test Results score
   */
  static calculateTestResultsScore(metrics: any): number {
    const successDensity = parseFloat(metrics.test_success_density);

    // If success density is not available, calculate from test counts
    if (isNaN(successDensity) || successDensity === 0) {
      const totalTests = parseInt(metrics.tests || '0');
      const testErrors = parseInt(metrics.test_errors || '0');
      const testFailures = parseInt(metrics.test_failures || '0');
      const skippedTests = parseInt(metrics.skipped_tests || '0');

      if (totalTests > 0) {
        const passedTests = totalTests - testErrors - testFailures;
        const effectiveTests = totalTests - skippedTests; // Don't count skipped as failures
        const calculatedSuccessRate = effectiveTests > 0 ? (passedTests / effectiveTests) * 100 : 0;
        return Math.max(0, Math.min(100, Math.round(calculatedSuccessRate)));
      }

      // No test data available - return 0 score
      return 0;
    }

    const finalScore = Math.max(0, Math.min(100, Math.round(successDensity)));
    return isNaN(finalScore) ? 0 : finalScore;
  }

  /**
   * Calculate Change Requests score
   */
  static calculateChangeRequestsScore(changeRequests: any[]): number {
    if (!changeRequests || changeRequests.length === 0) return 100; // Perfect score for no change requests

    const totalRequests = changeRequests.length;
    const resolvedRequests = changeRequests.filter(
      (cr) => cr.state === 'Resolved' || cr.state === 'Closed'
    ).length;
    const openRequests = changeRequests.filter(
      (cr) => cr.state === 'Open' || cr.state === 'New'
    ).length;
    const highPriorityRequests = changeRequests.filter(
      (cr) => cr.priority === 'High' || cr.priority === 'Critical' || cr.priority === '1 - Critical'
    ).length;

    // Calculate score (lower is better for change requests)
    let score = 100; // Start with perfect score

    // Penalty for total number of change requests
    score -= Math.min(totalRequests * 2, 30); // Up to 30 points penalty

    // Penalty for unresolved requests
    score -= Math.min(openRequests * 5, 40); // Up to 40 points penalty

    // Extra penalty for high priority unresolved requests
    score -= Math.min(highPriorityRequests * 8, 30); // Up to 30 points penalty

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Calculate Changelog score
   */
  static calculateChangelogScore(pullRequests: any[]): number {
    if (!pullRequests || pullRequests.length === 0) return 50; // Neutral score for no PRs

    const mergedPRs = pullRequests.filter((pr) => pr.status === 'merged').length;
    const openPRs = pullRequests.filter((pr) => pr.status === 'open').length;
    const closedPRs = pullRequests.filter((pr) => pr.status === 'closed').length;

    // Calculate score based on PR status distribution
    const totalPRs = pullRequests.length;
    const mergedRatio = mergedPRs / totalPRs;
    const openRatio = openPRs / totalPRs;

    // Higher score for more merged PRs, lower for abandoned (closed) PRs
    let score = 50; // Base score
    score += mergedRatio * 40; // Up to 40 points for merged PRs
    score += openRatio * 10; // Up to 10 points for open PRs (work in progress)

    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Main confidence score calculation method
   */
  static calculateConfidenceScore(
    releaseMetrics: ReleaseMetrics,
    weightings: Weightings = DEFAULT_WEIGHTINGS
  ): number {
    let totalScore = 0;
    let totalWeight = 0;

    // Check which metrics are available (all 6 metrics)
    const hasCodeQuality = releaseMetrics.codeQuality?.score !== undefined;
    const hasCodeCoverage = releaseMetrics.codeCoverage?.score !== undefined;
    const hasTestResults = releaseMetrics.testResults?.score !== undefined;
    const hasBuildStability = releaseMetrics.buildStability?.score !== undefined;
    const hasChangeRequests = releaseMetrics.changeRequests?.score !== undefined;
    const hasChangeLog =
      releaseMetrics.changeLog?.score !== undefined ||
      releaseMetrics.changelog?.score !== undefined;

    // Check if we have all core metrics (the 4 most important ones)
    const hasCoreMetrics = hasCodeQuality && hasCodeCoverage && hasTestResults && hasBuildStability;

    // We need at least the 4 core metrics to calculate a meaningful confidence score
    if (!hasCoreMetrics) {
      console.log(
        `⏳ ConfidenceScoreService: Waiting for core metrics. Available: Code Quality(${hasCodeQuality}), Coverage(${hasCodeCoverage}), Test Results(${hasTestResults}), Build Stability(${hasBuildStability})`
      );
      return 0; // Return 0 to indicate processing state
    }

    // Code Quality metrics (SonarQube) - Core metric
    if (hasCodeQuality) {
      const weight = weightings.codeQuality;
      totalScore += releaseMetrics.codeQuality!.score * (weight / 100);
      totalWeight += weight;
    }

    // Code Coverage metrics - Core metric
    if (hasCodeCoverage) {
      const weight = weightings.codeCoverage;
      totalScore += releaseMetrics.codeCoverage!.score * (weight / 100);
      totalWeight += weight;
    }

    // Test Results metrics - Core metric
    if (hasTestResults) {
      const weight = weightings.testResults;
      totalScore += releaseMetrics.testResults!.score * (weight / 100);
      totalWeight += weight;
    }

    // Build Stability metrics (Looper) - Core metric
    if (hasBuildStability) {
      const weight = weightings.buildStability;
      // Use the score if available, otherwise use success rate from build statistics
      const buildScore =
        releaseMetrics.buildStability!.score ||
        (releaseMetrics.buildStability as any)?.statistics?.successRate ||
        0;
      totalScore += buildScore * (weight / 100);
      totalWeight += weight;
    }

    // Change Requests metrics - Optional (add if available)
    if (hasChangeRequests) {
      const weight = weightings.changeRequests;
      // Calculate score if missing
      let changeRequestsScore = releaseMetrics.changeRequests!.score;
      if (changeRequestsScore === undefined) {
        const items =
          (releaseMetrics.changeRequests as any)?.items ||
          (releaseMetrics.changeRequests as any)?.rawData ||
          [];
        if (Array.isArray(items) && items.length === 0) {
          changeRequestsScore = 100; // Perfect score for no change requests
        } else if (Array.isArray(items)) {
          changeRequestsScore = ConfidenceScoreService.calculateChangeRequestsScore(items);
        } else {
          changeRequestsScore = 100; // Default to perfect score
        }
      }
      totalScore += changeRequestsScore * (weight / 100);
      totalWeight += weight;
    }

    // Change Log metrics (GitHub PRs) - Optional (add if available)
    if (hasChangeLog) {
      const weight = weightings.changeLog;
      // Calculate score if missing
      const changeLogData = releaseMetrics.changeLog || releaseMetrics.changelog;
      let changeLogScore = changeLogData!.score;
      if (changeLogScore === undefined) {
        const items = (changeLogData as any)?.rawData || [];
        if (Array.isArray(items) && items.length === 0) {
          changeLogScore = 50; // Neutral score for no PRs
        } else if (Array.isArray(items)) {
          changeLogScore = ConfidenceScoreService.calculateChangelogScore(items);
        } else {
          changeLogScore = 50; // Default to neutral score
        }
      }
      totalScore += changeLogScore * (weight / 100);
      totalWeight += weight;
    }

    return totalWeight > 0 ? Math.round(totalScore) : 0;
  }

  /**
   * Calculate trending information
   */
  static calculateTrending(
    currentScore: number,
    previousScore?: number
  ): ConfidenceMetrics['trending'] {
    if (previousScore === undefined) {
      return { direction: 'stable', changePercent: 0, previousScore };
    }

    const change = currentScore - previousScore;
    const changePercent = previousScore > 0 ? Math.round((change / previousScore) * 100) : 0;

    return {
      direction: change > 2 ? 'up' : change < -2 ? 'down' : 'stable',
      changePercent: Math.abs(changePercent),
      previousScore,
    };
  }

  /**
   * Create complete confidence metrics object
   */
  static createConfidenceMetrics(
    releaseMetrics: ReleaseMetrics,
    weightings: Weightings = DEFAULT_WEIGHTINGS,
    previousScore?: number
  ): ConfidenceMetrics {
    const overallScore = ConfidenceScoreService.calculateConfidenceScore(
      releaseMetrics,
      weightings
    );
    const trending = ConfidenceScoreService.calculateTrending(overallScore, previousScore);

    return {
      overallScore,
      lastCalculated: new Date().toISOString(),
      weightings,
      trending,
    };
  }

  /**
   * Validate weightings sum to 100
   */
  static validateWeightings(weightings: Weightings): boolean {
    const sum =
      weightings.codeQuality +
      weightings.codeCoverage +
      weightings.testResults +
      weightings.buildStability +
      weightings.changeRequests +
      weightings.changeLog;
    return sum === 100;
  }

  /**
   * Get scoring formulas for documentation
   */
  static getScoringFormulas() {
    return {
      codeQuality:
        'Score = 100 - (bugs × 2) - (vulnerabilities × 5) - coverage_penalty - duplication_penalty - (code_smells × 0.1)',
      codeCoverage: 'Score = (Overall Coverage × 0.7) + (New Code Coverage × 0.3)',
      testResults: 'Score = (Passed Tests / Total Tests) × 100',
      buildStability: 'Score = (Successful Builds / Total Builds) × 100',
      changeRequests: 'Score = 100 - min(total×2, 30) - min(open×5, 40) - min(high_priority×8, 30)',
      changeLog: 'Score = 50 + (merged_ratio × 40) + (open_ratio × 10)',
      overall: 'Weighted Sum = Σ(metric_score × weight_percentage) where weights sum to 100%',
    };
  }
}

export default ConfidenceScoreService;
