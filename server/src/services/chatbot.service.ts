import { Release } from '../models/release.model';
import { ReleaseService } from './release.service';
import { SignoffService } from './signoff.service';
import { MetricsService } from './metrics.service';
import { AzureOpenAIService, ChatMessage as AzureChatMessage } from './azureOpenAI.service';
import cosmosService from './cosmosService';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface SignoffStatus {
  progress: number;
  pendingApprovals: number;
  completedApprovals: number;
  totalRequired: number;
  lastUpdated: Date;
  stages?: Array<{
    name: string;
    required: number;
    completed: number;
    progress: number;
    status: 'pending' | 'in_progress' | 'completed';
    isCurrentStage: boolean;
  }>;
}

export interface ChatContext {
  releaseId: string;
  release?: {
    id: string;
    releaseName: string;
    releaseType: string;
    teamId: string;
    teamName: string;
    releaseRolloutDate: string;
    description?: string;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
    supportedPlatforms: any;
    releaseMetrics?: {
      changeRequests?: {
        items?: Array<{
          number: string;
          shortDescription: string;
          state: string;
          openedAt: string;
          resolvedAt?: string;
          priority: number | string;
          assignmentGroup: string;
          assignedTo?: string;
          requestedBy?: string;
          callerId?: string;
          sysId?: string;
        }>;
        score?: number;
        total?: number;
        lastUpdated?: string;
      };
      codeQuality?: {
        score?: number;
        bugs: number;
        codeSmells: number;
        vulnerabilities?: number;
        maintainabilityRating?: string;
        reliabilityRating?: string;
        securityRating?: string;
        details?: {
          linesOfCode?: number;
          complexity?: number;
          duplicatedLines?: number;
          newBugs?: number;
          totalBugs?: number;
        };
      };
      testResults?: {
        score?: number;
        testSuccessDensity: number;
        totalTests: number;
        testFailures: number;
        testErrors?: number;
        skippedTests?: number;
        details?: {
          passedTests?: number;
          avgExecutionTime?: number;
          testHealth?: string;
        };
      };
      codeCoverage?: {
        score?: number;
        coverage?: number;
        linesToCover?: number;
        uncoveredLines?: number;
        tests?: number;
      };
      buildStability?: {
        score?: number;
        averageBuildTime?: number;
        avgBuildTime?: number;
        statistics?: {
          total?: number;
          success?: number;
          failure?: number;
          successRate?: number;
          avgBuildTime?: number;
        };
        rawData?: {
          avgBuildTime?: number;
          averageBuildTime?: number;
          [key: string]: any;
        };
      };
      changeLog?: {
        score?: number;
        totalPRs?: number;
        mergedPRs?: number;
        openPRs?: number;
        closedPRs?: number;
        items?: Array<{
          number: number;
          title: string;
          author: string;
          status: string;
          created_at: string;
          merged_at?: string;
          closed_at?: string;
          url?: string;
        }>;
      };
    };
    approvals?: Array<{
      stage: string;
      status: string;
      approverEmail: string;
      approvedAt: string;
      notes?: string;
      subTeam?: string;
      sha?: string;
      buildNumber?: string;
    }>;
    pcfRequests?: Array<{
      jiraTicket: string;
      summary: string;
      description: string;
      status: string;
      requestedBy: string;
      requestedAt: string;
      approver: string;
      approvedAt: string;
      rejectionReason: string;
    }>;
    confidenceMetrics?: {
      overallScore: number;
      lastCalculated: string;
      weightings: {
        codeQuality: number;
        codeCoverage: number;
        testResults: number;
        buildStability: number;
        changeRequests: number;
        changeLog: number;
      };
      trending: {
        direction: 'up' | 'down' | 'stable';
        changePercent: number;
        previousScore?: number;
      };
    };
  };
  team?: {
    id: string;
    teamName: string;
    metadata: {
      platform: string;
      platform_type: string;
      github_repo: string;
      repo_default_branch: string;
      looper_url?: string;
      looper_team_id?: string;
      sonarqube_url: string;
      sonarqube_project_id: string;
      deployment_url?: string;
      slack_webhook?: string;
      serviceNowId?: string;
    };
    roles: {
      [roleName: string]: string[] | { [subTeamName: string]: string[] };
    };
    release_types: {
      [releaseTypeName: string]: {
        signoff_order: string[];
        enforcement: {
          [roleName: string]: {
            require_all_subteams?: boolean;
            min_signoffs_per_team?: number;
            min_signoffs?: number;
            min_total_signoffs?: number;
          };
        };
      };
    };
  };
  signoffStatus?: SignoffStatus;
  metrics?: any;
}

interface FormattedStage {
  name: string;
  displayName: string;
  required: number;
  completed: number;
  progress: number;
  status: 'pending' | 'in_progress' | 'completed';
  isCurrentStage: boolean;
}

interface ReleaseData {
  id: string;
  releaseName: string;
  releaseType: string;
  teamId: string;
  teamName: string;
  releaseRolloutDate: string;
  description?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  supportedPlatforms: {
    [key: string]: {
      enabled: boolean;
      [key: string]: boolean;
    };
  };
  releaseMetrics?: any;
  approvals?: any[];
  pcfRequests?: any[];
  confidenceMetrics?: {
    overallScore: number;
    lastCalculated: string;
    weightings: {
      codeQuality: number;
      codeCoverage: number;
      testResults: number;
      buildStability: number;
      changeRequests: number;
      changeLog: number;
    };
    trending: {
      direction: 'up' | 'down' | 'stable';
      changePercent: number;
      previousScore?: number;
    };
  };
}

interface CodeQualityDetails {
  linesOfCode?: number;
  complexity?: number;
  duplicatedLines?: number;
  newBugs?: number;
  totalBugs?: number;
}

interface Approval {
  stage: string;
  status: string;
  approverEmail: string;
  approvedAt: string;
  notes?: string;
  subTeam?: string;
  sha?: string;
  buildNumber?: string;
}

export class ChatbotService {
  private azureOpenAI: AzureOpenAIService;
  private releaseService: ReleaseService;
  private signoffService: SignoffService;
  private metricsService: MetricsService;
  private cosmosService: any;

  constructor(
    azureOpenAIService: AzureOpenAIService,
    releaseService: ReleaseService,
    signoffService: SignoffService,
    metricsService: MetricsService,
    cosmosService: any
  ) {
    this.azureOpenAI = azureOpenAIService;
    this.releaseService = releaseService;
    this.signoffService = signoffService;
    this.metricsService = metricsService;
    this.cosmosService = cosmosService;

    console.log('🤖 ChatbotService initialized with Azure OpenAI (o3-mini)');
  }

  private async fetchBuildStabilityFromMetrics(releaseId: string): Promise<any> {
    try {
      // Try to get build stability from the stored metrics
      const releaseMetrics = await this.cosmosService.getReleaseMetrics(releaseId);
      if (releaseMetrics?.releaseMetrics?.buildStability) {
        return releaseMetrics.releaseMetrics.buildStability;
      }
      return null;
    } catch (error) {
      console.error('Error fetching build stability from metrics:', error);
      return null;
    }
  }

  private async buildReleaseContext(releaseId: string): Promise<ChatContext> {
    const context: ChatContext = { releaseId };

    try {
      // Fetch complete release details from cosmos service
      const rawRelease = await this.cosmosService.getRelease(releaseId);
      if (!rawRelease) {
        throw new Error('Release not found');
      }

      // Debug: Log the release metrics structure
      console.log('🔍 Debug - Raw release metrics structure:', JSON.stringify({
        releaseId: rawRelease.id,
        hasReleaseMetrics: !!rawRelease.releaseMetrics,
        releaseMetricsKeys: rawRelease.releaseMetrics ? Object.keys(rawRelease.releaseMetrics) : [],
        hasBuildStability: !!rawRelease.releaseMetrics?.buildStability,
        buildStabilityData: rawRelease.releaseMetrics?.buildStability
      }, null, 2));

      // Fetch complete team details
      const team = await this.cosmosService.getTeam(rawRelease.teamId);
      if (team) {
        context.team = {
          id: team.id,
          teamName: team.teamName,
          metadata: team.metadata,
          roles: team.roles,
          release_types: team.release_types
        };
      }

      // Map all available release data with complete metrics
      const release: ReleaseData = {
        id: rawRelease.id,
        releaseName: rawRelease.releaseName,
        releaseType: rawRelease.releaseType,
        teamId: rawRelease.teamId,
        teamName: rawRelease.teamName,
        releaseRolloutDate: rawRelease.releaseRolloutDate,
        description: rawRelease.description,
        createdBy: rawRelease.createdBy || 'Unknown',
        createdAt: rawRelease.createdAt,
        updatedAt: rawRelease.updatedAt,
        supportedPlatforms: rawRelease.supportedPlatforms,
        approvals: rawRelease.approvals?.map((approval: Approval) => ({
          stage: approval.stage,
          status: approval.status,
          approverEmail: approval.approverEmail,
          approvedAt: approval.approvedAt,
          notes: approval.notes,
          subTeam: approval.subTeam,
          sha: approval.sha,
          buildNumber: approval.buildNumber
        })),
        releaseMetrics: {
          ...rawRelease.releaseMetrics,
          changeLog: rawRelease.releaseMetrics?.changeLog ? {
            score: rawRelease.releaseMetrics.changeLog.score,
            totalPRs: rawRelease.releaseMetrics.changeLog.totalPRs,
            mergedPRs: rawRelease.releaseMetrics.changeLog.mergedPRs,
            openPRs: rawRelease.releaseMetrics.changeLog.openPRs,
            closedPRs: rawRelease.releaseMetrics.changeLog.closedPRs,
            items: rawRelease.releaseMetrics.changeLog.rawData?.map((pr: any) => ({
              number: pr.number,
              title: pr.title,
              author: pr.author,
              status: pr.status,
              created_at: pr.created_at,
              merged_at: pr.merged_at,
              closed_at: pr.closed_at,
              url: pr.url
            }))
          } : undefined,
          changeRequests: rawRelease.releaseMetrics?.changeRequests ? {
            score: rawRelease.releaseMetrics.changeRequests.score,
            total: rawRelease.releaseMetrics.changeRequests.total,
            lastUpdated: rawRelease.releaseMetrics.changeRequests.lastUpdated,
            items: rawRelease.releaseMetrics.changeRequests.rawData?.map((cr: any) => ({
              number: cr.number,
              shortDescription: cr.shortDescription,
              state: cr.state,
              openedAt: cr.openedAt,
              resolvedAt: cr.resolvedAt,
              priority: cr.priority,
              assignmentGroup: cr.assignmentGroup,
              assignedTo: cr.assignedTo,
              requestedBy: cr.requestedBy,
              callerId: cr.callerId,
              sysId: cr.sysId
            }))
          } : undefined
        },
        pcfRequests: rawRelease.pcfRequests || [],
        confidenceMetrics: rawRelease.confidenceMetrics
      };

      context.release = release;

      // Fetch signoff status
      context.signoffStatus = await this.signoffService.getSignoffStatus(releaseId);
      
      // Fetch metrics
      context.metrics = await this.metricsService.getReleaseMetrics(releaseId);

      // If build stability is missing from release metrics, try to fetch it separately
      if (!release.releaseMetrics?.buildStability) {
        try {
          console.log('🔍 Build stability not found in release metrics, trying to fetch separately...');
          const buildStabilityData = await this.fetchBuildStabilityFromMetrics(releaseId);
          if (buildStabilityData) {
            console.log('✅ Found build stability data from metrics service:', buildStabilityData);
            if (!release.releaseMetrics) {
              release.releaseMetrics = {};
            }
            release.releaseMetrics.buildStability = buildStabilityData;
          }
        } catch (error) {
          console.log('❌ Failed to fetch build stability from metrics service:', error);
        }
      }

      return context;
    } catch (error) {
      console.error('Error building release context:', error);
      throw new Error('Failed to build release context');
    }
  }

  private buildSystemPrompt(context: ChatContext, queryType?: string): string {
    const { release, team } = context;

    // Build comprehensive system prompt with all necessary data
    const basePrompt = `You are a helpful assistant for the release "${release?.releaseName}" by team "${team?.teamName}".
You have access to comprehensive information about this release and can answer questions about any aspect of it.

IMPORTANT RESPONSE GUIDELINES:
1. Only provide information that is directly relevant to the user's specific question
2. Do not dump all available information - be selective and focused
3. **ALWAYS format your responses using proper markdown syntax for better readability:**
   - Use **bold text** for important information and labels
   - Use \`code formatting\` for technical terms, IDs, and values
   - Use bullet points (- or *) for lists
   - Use numbered lists (1., 2., 3.) for sequential information
   - Use ## headings for major sections
   - Use ### subheadings for subsections
   - Use > blockquotes for important notes or warnings
4. If information is not available, clearly state that instead of showing related data
5. Keep responses concise and to the point
6. Structure information hierarchically with clear headings and sections

SPECIFIC QUERY HANDLING:
- When asked for a "changelog", ONLY show Pull Requests as they represent actual code changes
- When asked about "deployments" or "change requests", show the Change Request information
- When asked about "PCF" or "post code freeze", show PCF requests with status and approval information
- When asked about "sign-off" or "approval" status, show current progress and sub-team breakdowns
- When asked about "team" information, show team roles, sub-teams, members, and platform details
- When asked about "platforms", show supported platforms and platform-specific configuration
- When asked about "metrics", "quality", or "confidence", show relevant metrics and trending information
- When asked about "builds approved by sub teams", show build URLs and test results from approval data
- When asked about "who can approve", show the authorized approvers for the specific role/sub-team
- When asked about "enforcement" or "rules", show the release type enforcement requirements
- For all other queries, only show information directly related to what was asked`;

    // Add comprehensive context with all release and team data
    const essentialContext = this.getEssentialContext(context);

    // Add query-specific context for enhanced responses
    const queryContext = this.getQuerySpecificContext(context, queryType);

    return basePrompt + essentialContext + queryContext;
  }

  private getEssentialContext(context: ChatContext): string {
    const { release, signoffStatus, team } = context;
    const formatDate = (dateStr?: string) => dateStr ? new Date(dateStr).toLocaleString() : 'Not set';

    // Always include comprehensive release and team data
    return `

COMPLETE RELEASE INFORMATION:
- Name: ${release?.releaseName}
- Team: ${release?.teamName}
- Type: ${release?.releaseType}
- Created By: ${release?.createdBy}
- Created At: ${formatDate(release?.createdAt)}
- Last Updated: ${formatDate(release?.updatedAt)}
- Rollout Date: ${formatDate(release?.releaseRolloutDate)}
${release?.description ? `- Description: ${release.description}` : ''}

TEAM CONFIGURATION:
- Platform: ${team?.metadata?.platform}
- Platform Type: ${team?.metadata?.platform_type}
- GitHub Repo: ${team?.metadata?.github_repo}
- Default Branch: ${team?.metadata?.repo_default_branch}
- ServiceNow ID: ${team?.metadata?.serviceNowId}
- Looper Team ID: ${team?.metadata?.looper_team_id || 'Not configured'}
- SonarQube Project: ${team?.metadata?.sonarqube_project_id}

SUPPORTED PLATFORMS:
${release?.supportedPlatforms ? `
Mobile Platform:
- Enabled: ${release.supportedPlatforms.mobile?.enabled ? 'Yes' : 'No'}
- iOS Support: ${release.supportedPlatforms.mobile?.ios ? 'Yes' : 'No'}
- Android Support: ${release.supportedPlatforms.mobile?.android ? 'Yes' : 'No'}

Web Platform:
- Enabled: ${release.supportedPlatforms.web?.enabled ? 'Yes' : 'No'}
- Desktop Support: ${release.supportedPlatforms.web?.desktop ? 'Yes' : 'No'}
- Mobile Web Support: ${release.supportedPlatforms.web?.mobileWeb ? 'Yes' : 'No'}

Backend Platform:
- Enabled: ${release.supportedPlatforms.backend?.enabled ? 'Yes' : 'No'}` : 'No platform information available'}

TEAM ROLES AND APPROVERS:
${team?.roles ? (() => {
  const roleEntries = Object.entries(team.roles);
  return roleEntries.map(([roleName, roleData]) => {
    if (Array.isArray(roleData)) {
      return `${roleName.toUpperCase()} (${roleData.length} members):
${roleData.map((member: string) => `  - ${member}`).join('\n')}`;
    } else if (typeof roleData === 'object' && roleData !== null) {
      const subTeams = Object.entries(roleData as Record<string, string[]>);
      return `${roleName.toUpperCase()} Sub-teams (${subTeams.length} teams):
${subTeams.map(([subTeam, members]) => `  ${subTeam.toUpperCase()} (${members.length} members):
${members.map((member: string) => `    - ${member}`).join('\n')}`).join('\n')}`;
    }
    return `${roleName.toUpperCase()}: ${roleData}`;
  }).join('\n\n');
})() : 'No team roles information available'}

RELEASE TYPE ENFORCEMENT (${release?.releaseType?.toUpperCase() || 'UNKNOWN'}):
${team?.release_types?.[release?.releaseType || 'major'] ? (() => {
  const releaseConfig = team.release_types[release?.releaseType || 'major'];
  const enforcement = releaseConfig.enforcement || {};

  return `
Signoff Order: ${releaseConfig.signoff_order?.join(' → ') || 'Not defined'}

Enforcement Rules:
${Object.entries(enforcement).map(([role, rules]) => {
  if (role === 'blocking_conditions') return '';
  const rulesList = [];
  if (rules.require_all_subteams !== undefined) {
    rulesList.push(`Require all sub-teams: ${rules.require_all_subteams ? 'Yes' : 'No'}`);
  }
  if (rules.min_signoffs_per_team) {
    rulesList.push(`Min signoffs per team: ${rules.min_signoffs_per_team}`);
  }
  if (rules.min_signoffs) {
    rulesList.push(`Min signoffs: ${rules.min_signoffs}`);
  }
  if (rules.min_total_signoffs) {
    rulesList.push(`Min total signoffs: ${rules.min_total_signoffs}`);
  }
  return `- ${role.toUpperCase()}: ${rulesList.join(', ') || 'No specific rules'}`;
}).filter(Boolean).join('\n')}

Blocking Conditions:
${enforcement.blocking_conditions ?
  Array.isArray(enforcement.blocking_conditions) ?
    enforcement.blocking_conditions.map((condition: any) =>
      `- ${condition.description || condition.condition || 'Unknown condition'}`
    ).join('\n') :
    '- No blocking conditions defined' :
  '- No blocking conditions defined'}`;
})() : 'No enforcement rules defined for this release type'}

CURRENT SIGNOFF STATUS:
- Overall Progress: ${signoffStatus?.progress || 0}%
- Completed Approvals: ${signoffStatus?.completedApprovals || 0}/${signoffStatus?.totalRequired || 0}
- Pending Approvals: ${signoffStatus?.pendingApprovals || 0}
- Last Updated: ${formatDate(signoffStatus?.lastUpdated?.toString())}

Current Stage:
${signoffStatus?.stages?.find(s => s.isCurrentStage) ? `
- Name: ${signoffStatus.stages.find(s => s.isCurrentStage)!.name}
- Progress: ${signoffStatus.stages.find(s => s.isCurrentStage)!.progress}%
- Completed: ${signoffStatus.stages.find(s => s.isCurrentStage)!.completed}/${signoffStatus.stages.find(s => s.isCurrentStage)!.required}` : 'No current stage information available'}

DETAILED APPROVALS:
${release?.approvals ? release.approvals.map(approval => `
- ${approval.subTeam?.toUpperCase() || 'Unknown'}: ${approval.status} by ${approval.approverEmail}
  - Build: ${approval.buildNumber || 'N/A'}
  - Test Results: ${approval.sha || 'N/A'}
  - Notes: ${approval.notes || 'No notes'}
  - Date: ${formatDate(approval.approvedAt)}`).join('\n') : 'No approvals data'}

CODE QUALITY METRICS:
${release?.releaseMetrics?.codeQuality ? `
- Score: ${release.releaseMetrics.codeQuality.score}
- Bugs: ${release.releaseMetrics.codeQuality.bugs}
- New Bugs: ${release.releaseMetrics.codeQuality.details?.newBugs || 0}
- Code Smells: ${release.releaseMetrics.codeQuality.codeSmells}
- Vulnerabilities: ${release.releaseMetrics.codeQuality.vulnerabilities}
- Maintainability: ${release.releaseMetrics.codeQuality.maintainabilityRating}
- Reliability: ${release.releaseMetrics.codeQuality.reliabilityRating}
- Security: ${release.releaseMetrics.codeQuality.securityRating}` : 'No code quality metrics available'}

TEST RESULTS:
${release?.releaseMetrics?.testResults ? `
- Score: ${release.releaseMetrics.testResults.score}
- Success Rate: ${release.releaseMetrics.testResults.testSuccessDensity}%
- Total Tests: ${release.releaseMetrics.testResults.totalTests}
- Failed Tests: ${release.releaseMetrics.testResults.testFailures}
- Errors: ${release.releaseMetrics.testResults.testErrors}
- Skipped: ${release.releaseMetrics.testResults.skippedTests}` : 'No test results available'}

CODE COVERAGE:
${release?.releaseMetrics?.codeCoverage ? `
- Score: ${release.releaseMetrics.codeCoverage.score}
- Coverage: ${release.releaseMetrics.codeCoverage.coverage}%
- Lines to Cover: ${release.releaseMetrics.codeCoverage.linesToCover}
- Uncovered Lines: ${release.releaseMetrics.codeCoverage.uncoveredLines}
- Tests: ${release.releaseMetrics.codeCoverage.tests}` : 'No code coverage information available'}

BUILD STABILITY:
${release?.releaseMetrics?.buildStability ? (() => {
  const buildStability = release.releaseMetrics.buildStability as any;
  const stats = buildStability.statistics || buildStability.rawData?.statistics || {};
  const rawData = buildStability.rawData || {};

  return `
- Score: ${buildStability.score || 'N/A'}
- Total Builds: ${stats.total || 'N/A'}
- Successful Builds: ${stats.success || 'N/A'}
- Failed Builds: ${stats.failure || 'N/A'}
- Success Rate: ${stats.successRate || 'N/A'}%
- Average Build Time: ${stats.avgBuildTime ? this.formatBuildTime(Math.round(stats.avgBuildTime)) : 'N/A'}
- Last Build Date: ${rawData.lastBuildDate || 'N/A'}
- First Build Date: ${rawData.firstBuildDate || 'N/A'}
- Branch: ${buildStability.branch || rawData.branch || 'N/A'}
- Last Updated: ${formatDate(buildStability.lastUpdated)}`;
})() : 'No build stability information available'}

CONFIDENCE METRICS:
${release?.confidenceMetrics ? `
- Overall Score: ${release.confidenceMetrics.overallScore}
- Last Calculated: ${formatDate(release.confidenceMetrics.lastCalculated)}
- Trending: ${release.confidenceMetrics.trending.direction} (${release.confidenceMetrics.trending.changePercent > 0 ? '+' : ''}${release.confidenceMetrics.trending.changePercent}%)
- Previous Score: ${release.confidenceMetrics.trending.previousScore || 'N/A'}
- Weightings:
  * Code Quality: ${release.confidenceMetrics.weightings.codeQuality}%
  * Code Coverage: ${release.confidenceMetrics.weightings.codeCoverage}%
  * Test Results: ${release.confidenceMetrics.weightings.testResults}%
  * Build Stability: ${release.confidenceMetrics.weightings.buildStability}%
  * Change Requests: ${release.confidenceMetrics.weightings.changeRequests}%
  * Change Log: ${release.confidenceMetrics.weightings.changeLog}%` : 'No confidence metrics available'}

PULL REQUESTS:
${release?.releaseMetrics?.changeLog ? `
Summary:
- Total PRs: ${release.releaseMetrics.changeLog.totalPRs || 0}
- Open PRs: ${release.releaseMetrics.changeLog.openPRs || 0}
- Merged PRs: ${release.releaseMetrics.changeLog.mergedPRs || 0}
- Closed PRs: ${release.releaseMetrics.changeLog.closedPRs || 0}

Recent PRs:
${release.releaseMetrics.changeLog.items?.slice(0, 10).map(pr => `
- PR #${pr.number}: ${pr.title}
  - Author: ${pr.author}
  - Status: ${pr.status}
  - Created: ${new Date(pr.created_at).toLocaleString()}
  ${pr.merged_at ? `- Merged: ${new Date(pr.merged_at).toLocaleString()}` : ''}
  ${pr.closed_at && pr.status === 'closed' ? `- Closed: ${new Date(pr.closed_at).toLocaleString()}` : ''}

  - URL: ${pr.url || 'N/A'}`).join('\n') || 'No PRs found'}` : 'No PR data available'}

PCF APPROVERS:
${Array.isArray(team?.roles.pcf_approvers) ? team.roles.pcf_approvers.map((approver: string) => `- ${approver}`).join('\n') : 'No PCF approvers defined'}

PCF REQUESTS:
${release?.pcfRequests && release.pcfRequests.length > 0 ? (() => {
  const approved = release.pcfRequests.filter((req: any) => req.status === 'approved');
  const pending = release.pcfRequests.filter((req: any) => req.status === 'pending');
  const rejected = release.pcfRequests.filter((req: any) => req.status === 'rejected');

  const formatPCFRequests = (requests: any[]) => requests.map(req =>
    `- ${req.jiraTicket}: ${req.summary}
  * Status: ${req.status}
  * Requested by: ${req.requestedBy}
  * Requested at: ${formatDate(req.requestedAt)}
  * Description: ${req.description}${req.approver ? `\n  * Approved by: ${req.approver}` : ''}${req.approvedAt ? `\n  * Approved at: ${formatDate(req.approvedAt)}` : ''}${req.rejectionReason ? `\n  * Rejection reason: ${req.rejectionReason}` : ''}`
  ).join('\n\n');

  return `
Total PCF Requests: ${release.pcfRequests.length}

Approved PCF Requests (${approved.length}):
${approved.length > 0 ? formatPCFRequests(approved) : 'No approved PCF requests'}

Pending PCF Requests (${pending.length}):
${pending.length > 0 ? formatPCFRequests(pending) : 'No pending PCF requests'}

Rejected PCF Requests (${rejected.length}):
${rejected.length > 0 ? formatPCFRequests(rejected) : 'No rejected PCF requests'}`;
})() : 'No PCF requests found'}`;
  }

  private getQuerySpecificContext(context: ChatContext, queryType?: string): string {

    switch (queryType) {
      case 'buildApproval':
        return this.getBuildApprovalContext(context);
      case 'signoff':
        return this.getSignoffContext(context);
      case 'metrics':
        return this.getMetricsContext(context);
      case 'mergeTime':
        return this.getMergeTimeContext(context);
      case 'velocity':
        return this.getVelocityContext(context);
      case 'buildTime':
        return this.getBuildTimeContext(context);
      case 'knownIssues':
        return this.getKnownIssuesContext(context);
      case 'team':
        return this.getTeamContext(context);
      case 'pcf':
        return this.getPCFContext(context);
      case 'changelog':
        return this.getChangelogContext(context);
      case 'deployment':
        return this.getDeploymentContext(context);
      default:
        return this.getMinimalContext(context);
    }
  }

  private getBuildApprovalContext(context: ChatContext): string {
    const { release, team } = context;
    const formatDate = (dateStr?: string) => dateStr ? new Date(dateStr).toLocaleString() : 'Not set';

    if (!release?.approvals || !team?.roles.functional_qa) {
      return '\n\nNo build approval information available.';
    }

    const approvedBuilds = release.approvals
      .filter(approval => approval.status === 'approved' && approval.subTeam)
      .map(approval => `
**${approval.subTeam!.toUpperCase()} Team Build:**
- Build URL: ${approval.buildNumber || 'Not available'}
- Test Results: ${approval.sha || 'Not available'}
- Approved by: ${approval.approverEmail}
- Approved on: ${formatDate(approval.approvedAt)}
- Notes: ${approval.notes || 'No notes'}`)
      .join('\n');

    return `\n\nAPPROVED BUILDS BY SUB-TEAMS:\n${approvedBuilds || 'No approved builds found'}`;
  }

  private getSignoffContext(context: ChatContext): string {
    const { release, signoffStatus, team } = context;
    const formatDate = (dateStr?: string) => dateStr ? new Date(dateStr).toLocaleString() : 'Not set';

    return `\n\nSIGNOFF STATUS:
- Progress: ${signoffStatus?.progress || 0}%
- Completed: ${signoffStatus?.completedApprovals || 0}/${signoffStatus?.totalRequired || 0}
- Pending: ${signoffStatus?.pendingApprovals || 0}
- Last Updated: ${formatDate(signoffStatus?.lastUpdated?.toString())}

${this.formatSubTeamApprovals(release, team)}`;
  }

  private getMetricsContext(context: ChatContext): string {
    const { release } = context;

    return `\n\nMETRICS SUMMARY:
${release?.releaseMetrics?.codeQuality ? `
**Code Quality:** Score ${release.releaseMetrics.codeQuality.score}
- Bugs: ${release.releaseMetrics.codeQuality.bugs}
- Vulnerabilities: ${release.releaseMetrics.codeQuality.vulnerabilities}
- Code Smells: ${release.releaseMetrics.codeQuality.codeSmells}` : ''}

${release?.releaseMetrics?.testResults ? `
**Test Results:** Score ${release.releaseMetrics.testResults.score}
- Success Rate: ${release.releaseMetrics.testResults.testSuccessDensity}%
- Total Tests: ${release.releaseMetrics.testResults.totalTests}
- Failed: ${release.releaseMetrics.testResults.testFailures}` : ''}

${release?.releaseMetrics?.codeCoverage ? `
**Code Coverage:** ${release.releaseMetrics.codeCoverage.coverage}%
- Lines to Cover: ${release.releaseMetrics.codeCoverage.linesToCover}
- Uncovered: ${release.releaseMetrics.codeCoverage.uncoveredLines}` : ''}

${release?.confidenceMetrics ? `
**Confidence Score:** ${release.confidenceMetrics.overallScore}
- Trending: ${release.confidenceMetrics.trending.direction} (${release.confidenceMetrics.trending.changePercent > 0 ? '+' : ''}${release.confidenceMetrics.trending.changePercent}%)` : ''}`;
  }

  private getTeamContext(context: ChatContext): string {
    const { team } = context;

    if (!team?.roles) return '\n\nNo team information available.';

    const roleEntries = Object.entries(team.roles);
    const teamInfo = roleEntries.map(([roleName, roleData]) => {
      if (Array.isArray(roleData)) {
        return `**${roleName.toUpperCase()}** (${roleData.length} members):
${roleData.map(member => `  - ${member}`).join('\n')}`;
      } else if (typeof roleData === 'object' && roleData !== null) {
        const subTeams = Object.entries(roleData as Record<string, string[]>);
        return `**${roleName.toUpperCase()}** Sub-teams:
${subTeams.map(([subTeam, approvers]) =>
  `  **${subTeam.toUpperCase()}** (${approvers.length} approvers):
${approvers.map(approver => `    - ${approver}`).join('\n')}`
).join('\n')}`;
      }
      return `**${roleName.toUpperCase()}**`;
    }).join('\n\n');

    return `\n\nTEAM INFORMATION:
- Platform: ${team.metadata?.platform}
- GitHub Repo: ${team.metadata?.github_repo}

ROLES AND APPROVERS:
${teamInfo}`;
  }

  private getPCFContext(context: ChatContext): string {
    const { release, team } = context;
    const formatDate = (dateStr?: string) => dateStr ? new Date(dateStr).toLocaleString() : 'Not set';

    const pcfInfo = release?.pcfRequests && release.pcfRequests.length > 0 ?
      release.pcfRequests.map((req: any) => `
- **${req.jiraTicket}**: ${req.summary}
  - Status: ${req.status}
  - Requested by: ${req.requestedBy}
  - Requested: ${formatDate(req.requestedAt)}`).join('\n') : 'No PCF requests found';

    const approvers = Array.isArray(team?.roles.pcf_approvers) ?
      team.roles.pcf_approvers.join(', ') : 'No PCF approvers defined';

    return `\n\nPCF INFORMATION:
**Authorized Approvers:** ${approvers}

**PCF Requests:**
${pcfInfo}`;
  }

  private getChangelogContext(context: ChatContext): string {
    const { release } = context;

    const prs = release?.releaseMetrics?.changeLog?.items || [];
    const mergedPRs = prs.filter(pr => pr.status === 'merged');

    if (mergedPRs.length === 0) return '\n\nNo merged pull requests found.';

    const prList = mergedPRs.slice(0, 10).map(pr =>
      `- **PR #${pr.number}**: ${pr.title}
  - Author: ${pr.author}
  - Created: ${new Date(pr.created_at).toLocaleString()}
  - Merged: ${pr.merged_at ? new Date(pr.merged_at).toLocaleString() : 'Unknown'}
  ${pr.closed_at && pr.status === 'closed' ? `- Closed: ${new Date(pr.closed_at).toLocaleString()}` : ''}
  - URL: ${pr.url || 'N/A'}`).join('\n');

    return `\n\nCHANGELOG (Merged PRs):
${prList}${mergedPRs.length > 10 ? `\n\n... and ${mergedPRs.length - 10} more PRs` : ''}`;
  }

  private getMergeTimeContext(context: ChatContext): string {
    const { release } = context;
    const prs = release?.releaseMetrics?.changeLog?.items || [];
    const mergedPRs = prs.filter(pr => pr.status === 'merged' && pr.created_at && pr.merged_at);

    if (mergedPRs.length === 0) {
      return '\n\nNo merged PRs with complete timestamp data found for merge time analysis.';
    }

    // Calculate merge times in milliseconds
    const mergeTimes = mergedPRs.map(pr => {
      const createdTime = new Date(pr.created_at).getTime();
      const mergedTime = new Date(pr.merged_at!).getTime();
      const durationMs = mergedTime - createdTime;
      return {
        pr,
        durationMs,
        durationHours: durationMs / (1000 * 60 * 60),
        durationDays: durationMs / (1000 * 60 * 60 * 24)
      };
    });

    // Calculate statistics
    const totalDurationMs = mergeTimes.reduce((sum, item) => sum + item.durationMs, 0);
    const averageDurationMs = totalDurationMs / mergeTimes.length;

    // Sort for median and extremes
    const sortedTimes = mergeTimes.sort((a, b) => a.durationMs - b.durationMs);
    const medianTime = sortedTimes[Math.floor(sortedTimes.length / 2)];
    const fastestTime = sortedTimes[0];
    const slowestTime = sortedTimes[sortedTimes.length - 1];

    // Format duration helper
    const formatDuration = (ms: number): string => {
      const hours = ms / (1000 * 60 * 60);
      const days = ms / (1000 * 60 * 60 * 24);

      if (days >= 1) {
        return `${days.toFixed(1)} days`;
      } else {
        return `${hours.toFixed(1)} hours`;
      }
    };

    return `

MERGE TIME ANALYSIS:

**Summary Statistics:**
- Total Merged PRs Analyzed: ${mergedPRs.length}
- **Average Merge Time: ${formatDuration(averageDurationMs)}**
- Median Merge Time: ${formatDuration(medianTime.durationMs)}
- Fastest Merge: ${formatDuration(fastestTime.durationMs)}
- Slowest Merge: ${formatDuration(slowestTime.durationMs)}

**Examples:**
- **Fastest**: PR #${fastestTime.pr.number} - ${formatDuration(fastestTime.durationMs)}
  "${fastestTime.pr.title}" by ${fastestTime.pr.author}

- **Slowest**: PR #${slowestTime.pr.number} - ${formatDuration(slowestTime.durationMs)}
  "${slowestTime.pr.title}" by ${slowestTime.pr.author}

**Recent Merge Times:**
${mergeTimes.slice(-5).map(item =>
  `- PR #${item.pr.number}: ${formatDuration(item.durationMs)}
    "${item.pr.title}" by ${item.pr.author}`
).join('\n')}`;
  }

  private getVelocityContext(context: ChatContext): string {
    const { release } = context;
    const prs = release?.releaseMetrics?.changeLog?.items || [];
    const mergedPRs = prs.filter(pr => pr.status === 'merged' && pr.merged_at);

    if (mergedPRs.length === 0) {
      return '\n\nNo merged PRs found for velocity analysis.';
    }

    // Get the date range from the changelog metadata
    const changeLog = release?.releaseMetrics?.changeLog;
    const dateRange = (changeLog as any)?.dateRange;

    let startDate: Date;
    let endDate: Date;
    let periodDays: number;

    if (dateRange?.start && dateRange?.end) {
      // Use the date range from metadata
      startDate = new Date(dateRange.start);
      endDate = new Date(dateRange.end);
      periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    } else {
      // Calculate from actual PR merge dates
      const mergeDates = mergedPRs.map(pr => new Date(pr.merged_at!).getTime());
      const earliestMerge = Math.min(...mergeDates);
      const latestMerge = Math.max(...mergeDates);

      startDate = new Date(earliestMerge);
      endDate = new Date(latestMerge);
      periodDays = Math.ceil((latestMerge - earliestMerge) / (1000 * 60 * 60 * 24));

      // If all PRs were merged on the same day, set period to 1 day
      if (periodDays === 0) periodDays = 1;
    }

    // Calculate velocity
    const velocity = mergedPRs.length / periodDays;

    // Calculate daily breakdown
    const dailyBreakdown = new Map<string, number>();
    mergedPRs.forEach(pr => {
      const mergeDate = new Date(pr.merged_at!).toDateString();
      dailyBreakdown.set(mergeDate, (dailyBreakdown.get(mergeDate) || 0) + 1);
    });

    // Get peak and low days
    const dailyCounts = Array.from(dailyBreakdown.values());
    const maxDaily = Math.max(...dailyCounts);
    const minDaily = Math.min(...dailyCounts);
    const avgDaily = dailyCounts.reduce((sum, count) => sum + count, 0) / dailyCounts.length;

    return `

PR VELOCITY ANALYSIS:

**Summary Statistics:**
- **PR Velocity: ${velocity.toFixed(2)} PRs per day**
- Total Merged PRs: ${mergedPRs.length}
- Analysis Period: ${periodDays} days (${startDate.toDateString()} to ${endDate.toDateString()})
- Peak Daily Merges: ${maxDaily} PRs
- Lowest Daily Merges: ${minDaily} PRs
- Average Daily Merges: ${avgDaily.toFixed(1)} PRs

**Daily Breakdown:**
${Array.from(dailyBreakdown.entries())
  .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
  .map(([date, count]) => `- ${date}: ${count} PR${count !== 1 ? 's' : ''}`)
  .join('\n')}

**Recent Merged PRs:**
${mergedPRs.slice(-5).map(pr =>
  `- PR #${pr.number}: ${pr.title}
    Merged: ${new Date(pr.merged_at!).toLocaleString()} by ${pr.author}`
).join('\n')}`;
  }

  private getKnownIssuesContext(context: ChatContext): string {
    const { release } = context;

    // Look for known issues in approval notes
    const approvals = release?.approvals || [];
    const allNotes: string[] = [];

    // Collect all notes from approvals
    approvals.forEach(approval => {
      if (approval.notes && approval.notes.trim()) {
        allNotes.push(`**${approval.stage}** (${approval.status}): ${approval.notes}`);
      }
    });

    // Also check build stability data if available
    const buildStability = (release as any)?.buildStability;
    if (buildStability?.knownIssues && Array.isArray(buildStability.knownIssues) && buildStability.knownIssues.length > 0) {
      buildStability.knownIssues.forEach((issue: any) => {
        allNotes.push(`**Build Stability Issue**: ${issue}`);
      });
    }

    if (allNotes.length === 0) {
      return `

KNOWN ISSUES ANALYSIS:

No known issues have been reported for release "${release?.releaseName}" by the ${release?.teamName} team.

**Checked Sources:**
- Approval stage notes: No issues reported
- Build stability reports: No issues found
- Stage-specific feedback: All clear

**Current Approval Status:**
${approvals.map(approval =>
  `- ${approval.stage}: ${approval.status}${approval.notes ? ' (has notes)' : ' (no notes)'}`
).join('\n')}`;
    }

    return `

KNOWN ISSUES REPORTED:

**Release:** ${release?.releaseName}
**Team:** ${release?.teamName}

**Issues Found in Approval Notes:**
${allNotes.map((note, index) => `${index + 1}. ${note}`).join('\n\n')}

**Summary:**
- Total issues/notes found: ${allNotes.length}
- Sources: Approval stage notes and build stability reports
- These issues were reported during the approval process

**Recommendation:** Review these issues with the team before proceeding with the release.`;
  }

  private getDeploymentContext(context: ChatContext): string {
    const { release } = context;
    const changeRequests = release?.releaseMetrics?.changeRequests;

    if (!changeRequests || !changeRequests.items || changeRequests.items.length === 0) {
      return `

CHANGE REQUESTS INFORMATION:

No change requests found for release "${release?.releaseName}" by the ${release?.teamName} team.

**Status:** No deployment/change request activity detected
**Last Updated:** ${changeRequests?.lastUpdated ? new Date(changeRequests.lastUpdated).toLocaleString() : 'Not available'}`;
    }

    const items = changeRequests.items;
    const total = changeRequests.total || items.length;
    const score = changeRequests.score;

    // Group by state
    const byState = items.reduce((acc: any, cr: any) => {
      const state = cr.state || 'Unknown';
      if (!acc[state]) acc[state] = [];
      acc[state].push(cr);
      return acc;
    }, {});

    // Group by priority
    const highPriority = items.filter((cr: any) =>
      cr.priority === '1' || cr.priority === '2' ||
      cr.priority === 'High' || cr.priority === 'Critical'
    );

    return `

CHANGE REQUESTS INFORMATION:

**Summary:**
- Total Change Requests: ${total}
- Score: ${score ? `${score}/100` : 'Not calculated'}
- High Priority Requests: ${highPriority.length}
- Last Updated: ${changeRequests.lastUpdated ? new Date(changeRequests.lastUpdated).toLocaleString() : 'Not available'}

**By State:**
${Object.entries(byState).map(([state, requests]: [string, any]) =>
  `- ${state}: ${requests.length} request${requests.length !== 1 ? 's' : ''}`
).join('\n')}

**Recent Change Requests:**
${items.slice(0, 5).map((cr: any) => `
- **${cr.number}**: ${cr.shortDescription}
  - Priority: ${cr.priority}
  - State: ${cr.state}
  - Opened: ${new Date(cr.openedAt).toLocaleString()}
  ${cr.resolvedAt ? `- Resolved: ${new Date(cr.resolvedAt).toLocaleString()}` : ''}
  - Assignment Group: ${cr.assignmentGroup}
  ${cr.assignedTo ? `- Assigned To: ${cr.assignedTo}` : ''}`
).join('\n')}

${items.length > 5 ? `\n... and ${items.length - 5} more change requests` : ''}`;
  }

  private formatBuildTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds} seconds`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0
        ? `${minutes} minutes ${remainingSeconds} seconds`
        : `${minutes} minutes`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      let result = `${hours} hour${hours !== 1 ? 's' : ''}`;
      if (minutes > 0) result += ` ${minutes} minute${minutes !== 1 ? 's' : ''}`;
      if (remainingSeconds > 0) result += ` ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;

      return result;
    }
  }

  private getBuildTimeContext(context: ChatContext): string {
    const { release } = context;
    const buildStability = release?.releaseMetrics?.buildStability;

    if (!buildStability) {
      return `

BUILD TIME ANALYSIS:

No build stability data available for release "${release?.releaseName}" by the ${release?.teamName} team.

**Status:** Build time information not found
**Recommendation:** Ensure build metrics are being collected and stored`;
    }

    // Check for average build time in different possible fields
    const avgBuildTime = buildStability.averageBuildTime ||
                        buildStability.avgBuildTime ||
                        buildStability.statistics?.avgBuildTime ||
                        buildStability.rawData?.avgBuildTime ||
                        buildStability.rawData?.averageBuildTime ||
                        (buildStability as any).average_build_time ||
                        (buildStability as any).buildTime;

    if (!avgBuildTime) {
      return `

BUILD TIME ANALYSIS:

Build stability data exists but no average build time found for release "${release?.releaseName}".

**Available Build Data:**
${Object.keys(buildStability).map(key => `- ${key}: ${(buildStability as any)[key]}`).join('\n')}

**Status:** Average build time not calculated or stored`;
    }

    const formattedTime = this.formatBuildTime(avgBuildTime);

    return `

**Average Build Time:** ${formattedTime}`;
  }

  private getMinimalContext(context: ChatContext): string {
    const { release, signoffStatus } = context;

    return `\n\nQUICK STATUS:
- Progress: ${signoffStatus?.progress || 0}%
- PRs: ${release?.releaseMetrics?.changeLog?.totalPRs || 0} total
- Confidence: ${release?.confidenceMetrics?.overallScore || 'N/A'}`;
  }

  private formatSubTeamApprovals(release: any, team: any): string {
    if (!release?.approvals || !team?.roles.functional_qa) {
      return 'No sub-team approval information available.';
    }

    const formatDate = (dateStr?: string) => dateStr ? new Date(dateStr).toLocaleString() : 'Not set';
    const functionalQA = team.roles.functional_qa as { [key: string]: string[] };

    const approvedTeams = release.approvals
      .filter((approval: any) => approval.status === 'approved' && approval.subTeam)
      .map((approval: any) => `- **${approval.subTeam.toUpperCase()}**: Approved by ${approval.approverEmail} on ${formatDate(approval.approvedAt)}`)
      .join('\n');

    const pendingTeams = Object.keys(functionalQA)
      .filter(subTeam => !release.approvals.some((approval: any) =>
        approval.status === 'approved' && approval.subTeam?.toLowerCase() === subTeam.toLowerCase()))
      .map(subTeam => `- **${subTeam.toUpperCase()}**: Pending`)
      .join('\n');

    return `
**Approved Sub-teams:**
${approvedTeams || 'None'}

**Pending Sub-teams:**
${pendingTeams || 'None'}`;
  }

  public async processMessage(
    releaseId: string,
    message: string,
    conversationHistory: ChatMessage[] = []
  ): Promise<ChatMessage> {
    try {
      // Build context for the release
      const context = await this.buildReleaseContext(releaseId);
      
      // Determine the type of query
      const lowerMessage = message.toLowerCase();
      const isChangelogQuery = lowerMessage.includes('changelog');
      const isDeploymentQuery = lowerMessage.includes('deployment') || lowerMessage.includes('change request');
      const isNotesQuery = lowerMessage.includes('notes') || lowerMessage.includes('note');
      const isBuildApprovalQuery = (lowerMessage.includes('build') &&
                                   (lowerMessage.includes('approval') || lowerMessage.includes('approved') ||
                                    lowerMessage.includes('sub team') || lowerMessage.includes('subteam')));
      const isSignoffQuery = (lowerMessage.includes('sign') || lowerMessage.includes('approval') ||
                            lowerMessage.includes('progress')) && !isNotesQuery && !isBuildApprovalQuery;
      const isPCFQuery = lowerMessage.includes('pcf') || lowerMessage.includes('post code freeze');
      const isPCFApproversQuery = lowerMessage.includes('pcf approver');
      const isPRQuery = lowerMessage.includes('pr') || lowerMessage.includes('pull request');
      const isMergeTimeQuery = (lowerMessage.includes('merge') && lowerMessage.includes('time')) ||
                               (lowerMessage.includes('average') && lowerMessage.includes('merge')) ||
                               lowerMessage.includes('merge duration');
      const isVelocityQuery = lowerMessage.includes('velocity') ||
                              (lowerMessage.includes('pr') && lowerMessage.includes('per day')) ||
                              (lowerMessage.includes('pull request') && lowerMessage.includes('per day')) ||
                              lowerMessage.includes('prs per day');
      const isKnownIssuesQuery = lowerMessage.includes('known issue') ||
                                 lowerMessage.includes('issues reported') ||
                                 lowerMessage.includes('reported issue') ||
                                 (lowerMessage.includes('issue') && lowerMessage.includes('so far'));
      const isBuildTimeQuery = (lowerMessage.includes('build') && lowerMessage.includes('time')) ||
                               (lowerMessage.includes('avg') && lowerMessage.includes('build')) ||
                               lowerMessage.includes('build duration') ||
                               lowerMessage.includes('average build');
      const isTeamQuery = lowerMessage.includes('team') || lowerMessage.includes('role') || lowerMessage.includes('member');
      const isApproverQuery = lowerMessage.includes('approve') || lowerMessage.includes('approver') ||
                              (lowerMessage.includes('who') && lowerMessage.includes('can'));
      const isEnforcementQuery = lowerMessage.includes('enforcement') || lowerMessage.includes('rule') ||
                                 lowerMessage.includes('requirement') || lowerMessage.includes('blocking');
      const isPlatformQuery = lowerMessage.includes('platform') || lowerMessage.includes('mobile') ||
                             lowerMessage.includes('web') || lowerMessage.includes('backend') ||
                             lowerMessage.includes('ios') || lowerMessage.includes('android');
      const isMetricsQuery = (lowerMessage.includes('metric') || lowerMessage.includes('quality') ||
                            lowerMessage.includes('coverage') || lowerMessage.includes('test') ||
                            lowerMessage.includes('build') || lowerMessage.includes('confidence')) && !isBuildApprovalQuery;
      const isReleaseInfoQuery = lowerMessage.includes('release') && (lowerMessage.includes('info') ||
                                lowerMessage.includes('detail') || lowerMessage.includes('about'));
      
      // Add specific handling instructions based on query type
      let focusedMessage = message;
      if (isChangelogQuery) {
        focusedMessage = `${message}\n\nNote: For changelog requests, only show Pull Requests as they represent actual code changes. Do not include Change Requests or deployment information.`;
      } else if (isDeploymentQuery) {
        focusedMessage = `${message}\n\nNote: For deployment/change request information, show the Change Requests. Do not include Pull Requests or code changes.`;
      } else if (isBuildApprovalQuery) {
        focusedMessage = `${message}\n\nNote: For build approval queries:
1. Show **build information** from approved sub-teams only
2. Include **build numbers/URLs** from the buildNumber field in approvals
3. Include **test results URLs** from the sha field in approvals
4. Show **approval notes** and **ticket references** from the notes field
5. Group information by **sub-team** for clarity
6. Show **approval timestamps** and **approver information**
7. Do NOT show Pull Request information - focus on actual build data from approvals
8. Format clearly with sub-team headings and build details`;
      } else if (isNotesQuery) {
        focusedMessage = `${message}\n\nNote: For notes queries:
1. Show the exact notes content as provided in the approval data
2. If asking for notes from a specific sub-team approval, show only that sub-team's notes field content
3. Use clear formatting with the sub-team name and display the notes content exactly as stored
4. The notes field contains the actual notes - do not interpret it as a reference or ID
5. If the notes field is empty or null, then state no notes are available
6. Do not include full approval status or other details unless specifically requested`;
      } else if (isSignoffQuery) {
        focusedMessage = `${message}\n\nNote: For sign-off/approval queries:
1. Use clear markdown formatting with headings and progress indicators
2. Show the **current approval progress** percentage from signoffStatus.progress (this is the authoritative source)
3. Show **number of completed approvals** out of total required (e.g., "**3 of 5** approvals completed")
4. Show **number of pending approvals** that still need completion
5. Include the **last update time**
6. Break down approvals by **sub-team** using clear sections:
   * Show which **sub-teams have approved** (with checkmarks or status indicators)
   * Show which **sub-teams are still pending**
   * Include **approver name** and **timestamp** for completed approvals (use \`code formatting\` for emails)
7. If no required approvals are defined (totalRequired = 0), clearly explain that while still showing any completed approvals
8. Do not state that all approvals are complete unless completedApprovals equals totalRequired AND totalRequired is greater than 0
9. Always display the progress percentage as provided by signoffStatus.progress, do not recalculate it
10. Use bullet points and clear status indicators for better readability`;
      } else if (isPCFApproversQuery) {
        focusedMessage = `${message}\n\nNote: For PCF approvers queries:
1. Show the list of authorized PCF approvers from the team's roles configuration
2. Only show the PCF approvers list, not PCF requests or other information
3. Format the list clearly with each approver on a new line`;
      } else if (isPCFQuery) {
        focusedMessage = `${message}\n\nNote: For PCF (Post Code Freeze) requests:
1. Use clear markdown formatting with headings and structured lists
2. Show all **PCF requests** associated with this release
3. For each PCF request, include:
   - **JIRA ticket** number (use \`code formatting\`)
   - **Summary/title**
   - **Current status** (approved/rejected/pending)
   - **Requested by** and **timestamp**
   - If approved/rejected, show **who** and **when**
4. Group requests by **status** (approved, pending, rejected)
5. Show **total count** of PCF requests
6. Only show PCF requests, not pull requests or code changes
7. Use bullet points and clear section headings`;
      } else if (isMergeTimeQuery) {
        focusedMessage = `${message}\n\nNote: For merge time analysis:
1. Calculate the actual average merge time from the available PR data
2. Show the calculation with specific numbers (hours, days)
3. Include statistics like:
   - Total merged PRs analyzed
   - Fastest merge time
   - Slowest merge time
   - Median merge time
4. Use the created_at and merged_at timestamps to calculate durations
5. Present results in a clear, readable format (e.g., "2.5 hours", "1.2 days")
6. Show a few examples of actual PRs with their merge times`;
      } else if (isVelocityQuery) {
        focusedMessage = `${message}\n\nNote: For PR velocity analysis:
1. Calculate the actual PR velocity (PRs per day) from the available data
2. Use the date range from the changelog or calculate from actual merge dates
3. Show specific numbers and statistics:
   - Total merged PRs in the period
   - Number of days in the analysis period
   - Exact velocity calculation (PRs/day)
   - Peak and low merge days
4. Include a daily breakdown showing merge activity
5. Present results clearly (e.g., "2.6 PRs per day")
6. Show recent merged PRs as examples`;
      } else if (isKnownIssuesQuery) {
        focusedMessage = `${message}\n\nNote: For known issues queries:
1. Check ALL approval stage notes for any reported issues
2. Look for issues in build stability reports
3. Search through stage-specific feedback and comments
4. If no issues found, clearly state that no issues have been reported
5. Organize issues by source (approval stage, build stability, etc.)
6. Provide actionable information about each issue found`;
      } else if (isBuildTimeQuery) {
        focusedMessage = `${message}\n\nNote: For build time queries:
1. Provide a direct, concise answer with the average build time in readable format
2. Show the formatted time (e.g., "1 hour 12 minutes") instead of raw seconds
3. Keep the response brief and focused on what was asked
4. Only include additional context if specifically requested`;
      } else if (isPRQuery) {
        focusedMessage = `${message}\n\nNote: For Pull Request queries:
1. Show detailed information for each PR:
   - PR number
   - Title
   - Author
   - Status (open/merged/closed)
   - Created date
   - Merged date (if applicable)
   - URL (always include the GitHub/GitLab URL)
2. Group PRs by status (open/merged/closed)
3. Show summary statistics (total/open/merged/closed counts)
4. Format dates in a readable way
5. If asking about open PRs specifically, prioritize showing open PRs first
6. Always include the PR URL for easy access`;
      } else if (isEnforcementQuery) {
        focusedMessage = `${message}\n\nNote: For enforcement/rules queries:
1. Show **current enforcement rules** for the ${context.release?.releaseType || 'current'} release type
2. Include **signoff order** and **approval requirements**
3. Show **minimum signoffs** required for each role
4. Include **blocking conditions** that must be met
5. Explain **sub-team requirements** (all vs. minimum)
6. Use clear formatting with role names and specific numbers`;
      } else if (isTeamQuery) {
        focusedMessage = `${message}\n\nNote: For team-related queries:
1. Use markdown formatting with clear headings and structure
2. Show team information including:
   - **Team name** and **platform**
   - Team **roles** and **sub-teams**
   - **Members** for each role/sub-team (use bullet points)
   - **Platform configuration**
3. If asking about specific roles, focus on those roles
4. Include enforcement rules for release types if relevant
5. Show authorized approvers for different stages
6. Use \`code formatting\` for email addresses and technical IDs`;
      } else if (isPlatformQuery) {
        focusedMessage = `${message}\n\nNote: For platform-related queries:
1. Use clear markdown formatting with headings and bullet points
2. Show **supported platforms** for this release:
   - **Mobile** (iOS/Android support)
   - **Web** (Desktop/Mobile Web support)
   - **Backend** support
3. Include team's **platform configuration**
4. Show platform-specific metadata if relevant
5. Use \`code formatting\` for technical configuration values`;
      } else if (isMetricsQuery) {
        focusedMessage = `${message}\n\nNote: For metrics-related queries:
1. Use clear markdown formatting with headings and structured data
2. Show **relevant metrics** based on the question:
   - **Code quality** metrics (bugs, vulnerabilities, code smells)
   - **Test results** and **coverage**
   - **Build stability**
   - **Confidence metrics** and **trending**
3. Include **scores** and **ratings** where available
4. Show **trending information** if asking about trends
5. Format numbers clearly with appropriate units
6. Use \`code formatting\` for metric values and percentages`;
      } else if (isReleaseInfoQuery) {
        focusedMessage = `${message}\n\nNote: For general release information queries:
1. Show comprehensive release details:
   - Basic information (name, type, dates, team)
   - Supported platforms
   - Current status and progress
   - Key metrics summary
2. Include team context if relevant
3. Show recent activity or updates`;
      } else {
        focusedMessage = `${message}\n\nRemember to provide only information that is directly relevant to this specific question. Be concise and focused.`;
      }
      
      // Determine query type for dynamic system prompt
      let queryType = 'default';
      if (isBuildApprovalQuery) queryType = 'buildApproval';
      else if (isSignoffQuery) queryType = 'signoff';
      else if (isMetricsQuery) queryType = 'metrics';
      else if (isMergeTimeQuery) queryType = 'mergeTime';
      else if (isVelocityQuery) queryType = 'velocity';
      else if (isBuildTimeQuery) queryType = 'buildTime';
      else if (isKnownIssuesQuery) queryType = 'knownIssues';
      else if (isEnforcementQuery || isApproverQuery || isTeamQuery) queryType = 'team';
      else if (isPCFQuery) queryType = 'pcf';
      else if (isChangelogQuery) queryType = 'changelog';
      else if (isDeploymentQuery) queryType = 'deployment';

      // Build dynamic system prompt based on query type
      const systemPrompt = this.buildSystemPrompt(context, queryType);
      
      // Prepare messages for the API call
      const messages = [
        { role: 'system', content: systemPrompt },
        ...conversationHistory,
        { role: 'user', content: focusedMessage }
      ];

      // Call Azure OpenAI API with specific parameters
      const azureMessages: AzureChatMessage[] = messages.map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }));

      const response = await this.azureOpenAI.getAIResponse(
        focusedMessage,
        azureMessages.slice(0, -1) // Remove the last message since it's passed separately
      );

      return {
        role: 'assistant',
        content: response
      };
    } catch (error) {
      console.error('Error processing chat message:', error);

      // Preserve the original error for better error handling upstream
      if (error instanceof Error) {
        throw error;
      }

      throw new Error('Failed to process chat message');
    }
  }
} 