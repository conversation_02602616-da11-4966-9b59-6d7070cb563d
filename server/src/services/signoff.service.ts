export interface SignoffStage {
  name: string;
  status: 'pending' | 'in_progress' | 'completed';
  required: number;
  completed: number;
  progress: number;
  isCurrentStage: boolean;
}

export interface SignoffStatus {
  progress: number;
  pendingApprovals: number;
  completedApprovals: number;
  totalRequired: number;
  currentStage: string;
  stages: SignoffStage[];
  lastUpdated: Date;
}

export interface SignoffService {
  getSignoffStatus(releaseId: string): Promise<SignoffStatus>;
  // Add other methods as needed
} 