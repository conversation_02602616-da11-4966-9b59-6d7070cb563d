import axios from 'axios';
import https from 'https';
import { loadLooperConfig } from '../config/looper';
import { Team } from '../types/database';
import ConfidenceScoreService from './confidenceScoreService';

// Build history interfaces
export interface BuildHistoryItem {
  id: string;
  buildStatus: 'SUCCESS' | 'FAILURE' | 'ABORTED' | 'UNSTABLE';
  buildNumber: number;
  jobName: string;
  branchName: string;
  prNumber: number;
  processId: string;
  flowName: string;
  processType: string;
  buildTimeInSecs: number;
  createdAt: number; // Unix timestamp
  createdBy: string | null;
}

export interface BuildHistoryResponse {
  content: BuildHistoryItem[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: any;
    offset: number;
    unpaged: boolean;
    paged: boolean;
  };
  total: number;
  totalPages: number;
  totalElements: number;
  last: boolean;
  numberOfElements: number;
  first: boolean;
  size: number;
  number: number;
  sort: any;
  empty: boolean;
}

export interface BuildStatistics {
  total: number;
  success: number;
  failure: number;
  aborted: number;
  unstable: number;
  successRate: number;
  failureRate: number;
  avgBuildTime: number;
  lastBuildDate: string;
  firstBuildDate: string;
}

export interface ProcessedBuildData {
  statistics: BuildStatistics;
  builds: BuildHistoryItem[];
  teamId: string;
  branch: string;
}

// Health check cache for adaptive timeouts
interface ApiHealthResult {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}

const healthCache = new Map<string, { result: ApiHealthResult; timestamp: number }>();
const HEALTH_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

class LooperService {
  private baseUrl: string | null = null;
  private initialized: boolean = false;

  /**
   * Initialize the service with Looper configuration
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      const config = await loadLooperConfig();
      this.baseUrl = config.baseUrl;
      this.initialized = true;
      console.log('[Looper] ✅ Service initialized successfully');
      
      // Handle SSL certificate issues in corporate environments
      process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0';
    } catch (error) {
      console.error('[Looper] ❌ Failed to initialize:', error);
      throw new Error('Looper service is not configured');
    }
  }

  /**
   * Check Looper API health for adaptive timeout configuration
   */
  private async checkLooperHealth(): Promise<ApiHealthResult> {
    const cacheKey = 'looper-health';
    const cached = healthCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && (Date.now() - cached.timestamp) < HEALTH_CACHE_DURATION) {
      return cached.result;
    }

    const startTime = Date.now();
    
    try {
      if (!this.baseUrl) {
        throw new Error('Looper service not initialized');
      }

      // Use a simple endpoint for health check
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: 3000, // Quick health check timeout
        validateStatus: (status) => status < 500,
      });
      
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: true,
        responseTime,
      };
      
      // Cache the result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[Looper] Health check: API healthy (${responseTime}ms)`);
      
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: false,
        responseTime,
        error: error.message || 'Unknown error',
      };
      
      // Cache the negative result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[Looper] Health check: API unhealthy (${error.message})`);
      
      return result;
    }
  }

  /**
   * Get adaptive timeout based on Looper API health
   */
  private async getAdaptiveTimeout(): Promise<number> {
    try {
      const healthResult = await this.checkLooperHealth();
      
      if (healthResult.isHealthy) {
        // API is healthy - use longer timeout for better reliability
        if (healthResult.responseTime < 1000) {
          console.log('[Looper] Using 30s timeout - API is responsive');
          return 30000; // 30 seconds
        } else {
          console.log('[Looper] Using 45s timeout - API is slow but healthy');
          return 45000; // 45 seconds
        }
      } else {
        // API is not responding well - use short timeout for quick fallback
        console.log('[Looper] Using 5s timeout - API appears unhealthy');
        return 5000; // 5 seconds
      }
    } catch (error) {
      // Health check failed - use short timeout
      console.log('[Looper] Using 5s timeout - health check failed');
      return 5000; // 5 seconds
    }
  }

  /**
   * Get build history from Looper API
   */
  async getBuildHistory(
    teamId: string,
    branch: string,
    size: number = 100
  ): Promise<BuildHistoryResponse> {
    await this.initialize();

    if (!this.baseUrl) {
      throw new Error('Looper configuration not available');
    }

    try {
      const url = `${this.baseUrl}/job/buildhistory`;
      const timeout = await this.getAdaptiveTimeout();
      
      console.log(`🔍 Fetching build history from Looper: ${url}`);
      
      const response = await axios.get<BuildHistoryResponse>(url, {
        params: {
          id: teamId,
          branch: encodeURIComponent(branch),
          size: size.toString()
        },
        timeout,
        headers: {
          'accept': 'application/json, text/plain, */*',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
      });

      console.log(`✅ Raw API response status: ${response.status}`);
      console.log(`✅ Raw API response data:`, JSON.stringify(response.data, null, 2));
      console.log(`✅ Successfully fetched ${response.data.content?.length || 0} builds from Looper`);
      return response.data;

    } catch (error: any) {
      console.error('❌ Error fetching build history from Looper:', error.message);
      
      // Check for certificate-related errors
      if (error.message.includes('self-signed certificate') || error.message.includes('certificate')) {
        throw new Error(`SSL Certificate Error: Cannot verify SSL certificate for Looper API. This is common in corporate environments. Please ensure you are connected to the corporate network or VPN.`);
      }
      
      // Check for network-related errors
      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
        throw new Error(`Network Error: Cannot connect to Looper API. Please ensure you are connected to the appropriate network or VPN.`);
      }
      
      if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
        throw new Error(`Timeout Error: Looper API request timed out. The service may be unavailable or you may need VPN access.`);
      }
      
      if (error.response?.status === 401 || error.response?.status === 403) {
        throw new Error(`Authentication Error: Access denied to Looper API. Please check your credentials and permissions.`);
      }
      
      if (error.response?.status === 404) {
        throw new Error(`Not Found: Build history not found for team ${teamId} on branch ${branch}`);
      }
      
      if (error.response?.status >= 500) {
        throw new Error(`Server Error: Looper API is experiencing issues (${error.response.status})`);
      }

      // Generic error
      throw new Error(`Looper API Error: ${error.message}`);
    }
  }

  /**
   * Calculate build statistics from build history
   */
  calculateStatistics(builds: BuildHistoryItem[]): BuildStatistics {
    if (builds.length === 0) {
      return {
        total: 0,
        success: 0,
        failure: 0,
        aborted: 0,
        unstable: 0,
        successRate: 0,
        failureRate: 0,
        avgBuildTime: 0,
        lastBuildDate: '',
        firstBuildDate: ''
      };
    }

    const statusCounts = builds.reduce((acc, build) => {
      acc[build.buildStatus.toLowerCase()] = (acc[build.buildStatus.toLowerCase()] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const total = builds.length;
    const success = statusCounts.success || 0;
    const failure = statusCounts.failure || 0;
    const aborted = statusCounts.aborted || 0;
    const unstable = statusCounts.unstable || 0;

    const successRate = total > 0 ? (success / total) * 100 : 0;
    const failureRate = total > 0 ? (failure / total) * 100 : 0;

    const avgBuildTime = total > 0 
      ? builds.reduce((sum, build) => sum + build.buildTimeInSecs, 0) / total 
      : 0;

    // Sort builds by creation date to find first and last
    const sortedBuilds = [...builds].sort((a, b) => a.createdAt - b.createdAt);
    const firstBuildDate = new Date(sortedBuilds[0].createdAt).toISOString();
    const lastBuildDate = new Date(sortedBuilds[sortedBuilds.length - 1].createdAt).toISOString();

    return {
      total,
      success,
      failure,
      aborted,
      unstable,
      successRate: Math.round(successRate * 100) / 100, // Round to 2 decimal places
      failureRate: Math.round(failureRate * 100) / 100,
      avgBuildTime: Math.round(avgBuildTime),
      lastBuildDate,
      firstBuildDate
    };
  }

  /**
   * Get build statistics for a team using team metadata
   */
  async getBuildStabilityForTeam(
    team: Team,
    branch: string
  ): Promise<ProcessedBuildData> {
    console.log(`🔍 getBuildStabilityForTeam called for team: ${team.teamName}, branch: ${branch}`);
    
    if (!team.metadata.looper_team_id) {
      throw new Error(`Team ${team.teamName} does not have looper_team_id configured in metadata`);
    }

    console.log(`🔍 Calling getBuildHistory with looper_team_id: ${team.metadata.looper_team_id}`);
    
    try {
      const buildHistory = await this.getBuildHistory(
        team.metadata.looper_team_id,
        branch
      );

      console.log(`✅ getBuildHistory returned:`, {
        contentLength: buildHistory.content?.length || 0,
        total: buildHistory.total,
        totalElements: buildHistory.totalElements
      });

      const statistics = this.calculateStatistics(buildHistory.content);
      
      console.log(`📊 Calculated statistics:`, statistics);

      return {
        statistics,
        builds: buildHistory.content,
        teamId: team.metadata.looper_team_id,
        branch
      };
    } catch (error: any) {
      console.error(`❌ getBuildStabilityForTeam error for team ${team.teamName}:`, error.message);
      // Make sure we re-throw the error instead of swallowing it
      throw error;
    }
  }

  /**
   * Calculate a quality score based on build statistics using centralized service
   */
  calculateQualityScore(statistics: BuildStatistics): number {
    return ConfidenceScoreService.calculateBuildStabilityScore({ statistics });
  }
}

// Export singleton instance
export default new LooperService(); 