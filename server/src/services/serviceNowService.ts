import axios from "axios";
import keyVaultClient from "../utils/keyVaultClient";

interface ChangeRequest {
  number: string;
  shortDescription: string;
  openedAt: string;
  resolvedAt?: string;
  priority: string;
  assignmentGroup: string;
  state: string;
  sysId: string;
  assignedTo?: string;
  requestedBy?: string;
  callerId?: string;
}

interface ChangeRequestsResponse {
  total: number;
  changeRequests: ChangeRequest[];
}

interface ServiceNowHit {
  _source: {
    number: string;
    short_description: string;
    opened_at: string;
    resolved_at?: string;
    priority: string;
    assignment_group_dv: string;
    state_dv: string;
    sys_id: string;
    assigned_to_dv?: string;
    requested_by?: string;
    caller_id_dv?: string;
  };
}

interface ServiceNowResponse {
  hits: {
    total: {
      value: number;
    };
    hits: ServiceNowHit[];
  };
}

// Health check cache for adaptive timeouts
interface ApiHealthResult {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}

const healthCache = new Map<string, { result: ApiHealthResult; timestamp: number }>();
const HEALTH_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

class ServiceNowService {
  private baseUrl: string | null = null;
  private authToken: string | null = null;
  private initialized: boolean = false;

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log("Initializing ServiceNow service with KeyVault secrets...");
      
      // Fetch secrets from KeyVault
      const [baseUrl, token] = await Promise.all([
        keyVaultClient.getSecret("servicenow-base-url"),
        keyVaultClient.getSecret("servicenow-token"),
      ]);

      this.baseUrl = baseUrl;
      this.authToken = `Basic ${token}`;
      this.initialized = true;

      console.log("ServiceNow service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize ServiceNow service:", error);
      throw error;
    }
  }

  /**
   * Check ServiceNow API health for adaptive timeout configuration
   */
  private async checkServiceNowHealth(): Promise<ApiHealthResult> {
    const cacheKey = 'servicenow-health';
    const cached = healthCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && (Date.now() - cached.timestamp) < HEALTH_CACHE_DURATION) {
      return cached.result;
    }

    const startTime = Date.now();
    
    try {
      if (!this.baseUrl || !this.authToken) {
        throw new Error('ServiceNow service not initialized');
      }

      // Use a simple search to check health
      const response = await axios.get(`${this.baseUrl}/snow_streaming_change_request/_search`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: this.authToken,
        },
        params: { size: 1 }, // Minimal query for health check
        timeout: 3000, // Quick health check timeout
        validateStatus: (status) => status < 500,
      });
      
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: true,
        responseTime,
      };
      
      // Cache the result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[ServiceNow] Health check: API healthy (${responseTime}ms)`);
      
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: false,
        responseTime,
        error: error.message || 'Unknown error',
      };
      
      // Cache the negative result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[ServiceNow] Health check: API unhealthy (${error.message})`);
      
      return result;
    }
  }

  /**
   * Get adaptive timeout based on ServiceNow API health
   */
  private async getAdaptiveTimeout(): Promise<number> {
    try {
      const healthResult = await this.checkServiceNowHealth();
      
      if (healthResult.isHealthy) {
        // API is healthy - use longer timeout for better reliability
        if (healthResult.responseTime < 1000) {
          console.log('[ServiceNow] Using 30s timeout - API is responsive');
          return 30000; // 30 seconds
        } else {
          console.log('[ServiceNow] Using 45s timeout - API is slow but healthy');
          return 45000; // 45 seconds
        }
      } else {
        // API is not responding well - use short timeout for quick fallback
        console.log('[ServiceNow] Using 5s timeout - API appears unhealthy');
        return 5000; // 5 seconds
      }
    } catch (error) {
      // Health check failed - use short timeout
      console.log('[ServiceNow] Using 5s timeout - health check failed');
      return 5000; // 5 seconds
    }
  }

  async getChangeRequests(assignmentGroup: string): Promise<ChangeRequestsResponse> {
    try {
      // Ensure service is initialized
      await this.initialize();

      console.log(
        "Fetching change requests for assignment group:",
        assignmentGroup
      );

      const url = `${this.baseUrl}/snow_streaming_change_request/_search`;
      const timeout = await this.getAdaptiveTimeout();

      const requestBody = {
        size: 1000,
        _source: [
          "number",
          "opened_at",
          "resolved_at",
          "priority",
          "assignment_group_dv",
          "state_dv",
          "short_description",
          "correlation_display",
          "sys_id",
          "assigned_to_dv",
          "requested_by",
          "caller_id_dv",
        ],
        query: {
          bool: {
            must: [
              {
                match: {
                  assignment_group_dv: {
                    query: assignmentGroup,
                    operator: "and",
                  },
                },
              },
              {
                terms: {
                  priority: ["0", "1", "2", "3", "4", "5"],
                },
              },
              {
                terms: {
                  "state_dv.keyword": [
                    "Assigned",
                    "Work in Progress",
                    "Pending",
                    "Closed",
                    "Resolved",
                  ],
                },
              },
              {
                range: {
                  opened_at: {
                    gte: "now-30d/d",
                  },
                },
              },
            ],
          },
        },
      };

      console.log(
        "Making request to ServiceNow with body:",
        JSON.stringify(requestBody, null, 2)
      );

      // Temporarily disable SSL certificate verification for internal Walmart servers
      const originalTLSReject = process.env.NODE_TLS_REJECT_UNAUTHORIZED;
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

      try {
        const response = await axios.post<ServiceNowResponse>(url, requestBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: this.authToken!,
          },
          timeout,
        });

        console.log("Received response from ServiceNow:", {
          status: response.status,
          totalHits: response.data.hits.total.value,
        });

        return {
          total: response.data.hits.total.value,
          changeRequests: response.data.hits.hits.map((hit: ServiceNowHit) => ({
            number: hit._source.number,
            shortDescription: hit._source.short_description,
            openedAt: hit._source.opened_at,
            resolvedAt: hit._source.resolved_at,
            priority: hit._source.priority,
            assignmentGroup: hit._source.assignment_group_dv,
            state: hit._source.state_dv,
            sysId: hit._source.sys_id,
            assignedTo: hit._source.assigned_to_dv,
            requestedBy: hit._source.requested_by,
            callerId: hit._source.caller_id_dv,
          })),
        };
      } finally {
        // Restore original TLS setting
        if (originalTLSReject !== undefined) {
          process.env.NODE_TLS_REJECT_UNAUTHORIZED = originalTLSReject;
        } else {
          delete process.env.NODE_TLS_REJECT_UNAUTHORIZED;
        }
      }
    } catch (error: any) {
      console.error("Failed to fetch change requests:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
      throw error;
    }
  }
}

export default new ServiceNowService(); 