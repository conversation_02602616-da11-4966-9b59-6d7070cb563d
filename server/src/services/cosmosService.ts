import { Container, Database } from '@azure/cosmos';
import { cosmosClient, cosmosConfig } from '../config/cosmosdb';
import { Team, ClientRelease, CreateClientReleaseData } from '../types/database';
import { v4 as uuidv4 } from 'uuid';
import yamlLoader from './yamlLoader';
import { logger } from '../utils/logger';
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

// Utility function to safely parse dates without timezone issues
const parseDateSafely = (dateString: string): Date => {
  const dateParts = dateString.split('-');
  if (dateParts.length === 3) {
    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
    const day = parseInt(dateParts[2]);
    return new Date(year, month, day);
  }
  return new Date(dateString);
};

class CosmosService {
  private database: Database;
  private teamsContainer: Container;
  private releasesContainer: Container;

  constructor() {
    this.database = cosmosClient.database(cosmosConfig.databaseId);
    this.teamsContainer = this.database.container(cosmosConfig.containers.teams);
    this.releasesContainer = this.database.container('releases');
  }

  async initializeDatabase(): Promise<void> {
    try {
      // Create database if it doesn't exist
      await cosmosClient.databases.createIfNotExists({
        id: cosmosConfig.databaseId
      });

      // Create teams container if it doesn't exist
      await this.database.containers.createIfNotExists({
        id: cosmosConfig.containers.teams,
        partitionKey: '/id'
      });

      // Create releases container if it doesn't exist
      await this.database.containers.createIfNotExists({
        id: 'releases',
        partitionKey: '/id'
      });

      console.log('✅ Cosmos DB initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Cosmos DB:', error);
      throw error;
    }
  }

  // TEAM OPERATIONS
  async getAllTeams(): Promise<Team[]> {
    try {
      const { resources } = await this.teamsContainer.items
        .query({
          query: 'SELECT * FROM c'
        })
        .fetchAll();
      
      // Sort by team name
      const sortedTeams = resources.sort((a: Team, b: Team) => {
        return a.teamName.localeCompare(b.teamName);
      });
      
      return sortedTeams as Team[];
    } catch (error) {
      console.error('Error fetching teams:', error);
      throw error;
    }
  }

  async getTeam(id: string): Promise<Team | null> {
    try {
      const { resource } = await this.teamsContainer.item(id, id).read<Team>();
      return resource || null;
    } catch (error: any) {
      if (error.code === 404) {
        return null;
      }
      console.error('Error fetching team:', error);
      throw error;
    }
  }

  async getTeamByName(teamName: string): Promise<Team | null> {
    try {
      const { resources } = await this.teamsContainer.items
        .query({
          query: 'SELECT * FROM c WHERE LOWER(c.teamName) = LOWER(@teamName)',
          parameters: [{ name: '@teamName', value: teamName }]
        })
        .fetchAll();
      
      return resources.length > 0 ? resources[0] as Team : null;
    } catch (error) {
      console.error('Error fetching team by name:', error);
      throw error;
    }
  }

  async createTeam(teamData: Omit<Team, 'id'>): Promise<Team> {
    try {
      // Check if team with same name already exists
      const existingTeam = await this.getTeamByName(teamData.teamName);
      if (existingTeam) {
        throw new Error(`Team with name "${teamData.teamName}" already exists`);
      }

      const team: Team = {
        id: uuidv4(),
        ...teamData
      };

      const { resource } = await this.teamsContainer.items.create(team);
      return resource as Team;
    } catch (error) {
      console.error('Error creating team:', error);
      throw error;
    }
  }

  async updateTeam(id: string, updates: Partial<Team>): Promise<Team> {
    try {
      const existingTeam = await this.getTeam(id);
      if (!existingTeam) {
        throw new Error(`Team with id ${id} not found`);
      }

      // If teamName is being updated, check for duplicates
      if (updates.teamName && updates.teamName !== existingTeam.teamName) {
        const duplicateTeam = await this.getTeamByName(updates.teamName);
        if (duplicateTeam && duplicateTeam.id !== id) {
          throw new Error(`Team with name "${updates.teamName}" already exists`);
        }
      }

      const updatedTeam = {
        ...existingTeam,
        ...updates,
        id // Ensure ID doesn't change
      };

      const { resource } = await this.teamsContainer
        .item(id, id)
        .replace(updatedTeam);

      return resource as Team;
    } catch (error) {
      console.error('Error updating team:', error);
      throw error;
    }
  }

  async deleteTeam(id: string): Promise<void> {
    try {
      await this.teamsContainer.item(id, id).delete();
    } catch (error) {
      console.error('Error deleting team:', error);
      throw error;
    }
  }

  async findDuplicateTeams(): Promise<{ teamName: string; count: number; teams: Team[] }[]> {
    try {
      const allTeams = await this.getAllTeams();
      const teamGroups = new Map<string, Team[]>();
      
      // Group teams by teamName (case-insensitive)
      for (const team of allTeams) {
        const normalizedName = team.teamName.toLowerCase();
        if (!teamGroups.has(normalizedName)) {
          teamGroups.set(normalizedName, []);
        }
        teamGroups.get(normalizedName)!.push(team);
      }
      
      // Find groups with more than one team
      const duplicates: { teamName: string; count: number; teams: Team[] }[] = [];
      for (const [teamName, teams] of teamGroups) {
        if (teams.length > 1) {
          duplicates.push({
            teamName: teams[0].teamName, // Use original case
            count: teams.length,
            teams
          });
        }
      }
      
      return duplicates;
    } catch (error) {
      console.error('Error finding duplicate teams:', error);
      throw error;
    }
  }

  async cleanupDuplicateTeams(): Promise<{ cleaned: number; kept: number }> {
    try {
      console.log('🧹 Checking for duplicate teams...');
      
      const duplicates = await this.findDuplicateTeams();
      
      if (duplicates.length === 0) {
        console.log('✅ No duplicate teams found');
        return { cleaned: 0, kept: 0 };
      }
      
      console.log(`⚠️ Found ${duplicates.length} sets of duplicate teams`);
      
      let totalCleaned = 0;
      let totalKept = 0;
      
      for (const duplicate of duplicates) {
        console.log(`🔍 Processing duplicates for team: ${duplicate.teamName} (${duplicate.count} entries)`);
        
        // Sort teams by creation date (newest first) - use _ts if available
        const sortedTeams = duplicate.teams.sort((a, b) => {
          const aTime = a._ts || 0;
          const bTime = b._ts || 0;
          return bTime - aTime; // Newest first
        });
        
        // Keep the newest one, delete the rest
        const teamToKeep = sortedTeams[0];
        const teamsToDelete = sortedTeams.slice(1);
        
        console.log(`✅ Keeping team with ID: ${teamToKeep.id} (newest)`);
        totalKept++;
        
        for (const teamToDelete of teamsToDelete) {
          console.log(`🗑️ Deleting duplicate team with ID: ${teamToDelete.id}`);
          await this.deleteTeam(teamToDelete.id!);
          totalCleaned++;
        }
      }
      
      console.log(`🎉 Cleanup completed! Kept: ${totalKept}, Cleaned: ${totalCleaned}`);
      return { cleaned: totalCleaned, kept: totalKept };
    } catch (error) {
      console.error('❌ Error cleaning up duplicate teams:', error);
      throw error;
    }
  }

  async seedTeamsFromYaml(): Promise<void> {
    try {
      console.log('🌱 Seeding teams from YAML files...');
      
      // Check if we already have teams
      const existingTeams = await this.getAllTeams();
      if (existingTeams.length > 0) {
        console.log('📝 Teams already exist. Checking for updates...');
        
        // Load teams from YAML
        const yamlTeams = await yamlLoader.loadAllTeams();
        
        // Check if we need to update existing teams
        for (const yamlTeam of yamlTeams) {
          // Use case-insensitive comparison to find existing team
          const existingTeam = existingTeams.find(t => 
            t.teamName.toLowerCase() === yamlTeam.teamName.toLowerCase()
          );
          
          if (existingTeam) {
            // Update existing team with YAML data
            console.log(`🔄 Updating existing team: ${yamlTeam.teamName}`);
            await this.updateTeam(existingTeam.id!, {
              ...yamlTeam,
              id: existingTeam.id // Keep existing ID
            });
          } else {
            // Create new team
            console.log(`➕ Creating new team: ${yamlTeam.teamName}`);
            await this.createTeam(yamlTeam);
          }
        }
        
        return;
      }
      
      // Load teams from YAML files
      const teams = await yamlLoader.loadAllTeams();
      
      if (teams.length === 0) {
        console.log('⚠️ No teams found in YAML files');
        return;
      }
      
      // Create teams in database
      for (const team of teams) {
        await this.createTeam(team);
        console.log(`✅ Created team: ${team.teamName}`);
      }
      
      console.log(`🎉 Successfully seeded ${teams.length} teams from YAML files`);
    } catch (error) {
      console.error('❌ Failed to seed teams from YAML files:', error);
      // Don't throw - seeding is optional
    }
  }

  // RELEASE OPERATIONS
  async getAllReleases(): Promise<ClientRelease[]> {
    try {
      const { resources } = await this.releasesContainer.items
        .query({
          query: 'SELECT * FROM c WHERE IS_DEFINED(c.releaseName)'
        })
        .fetchAll();
      
      // Migrate old platform structure to new structure if needed
      const migratedReleases = await Promise.all(resources.map(async (release: any) => {
        return await this.migrateRelease(release);
      }));
      
      // Sort by release date (newest first)
      const sortedReleases = migratedReleases.sort((a: ClientRelease, b: ClientRelease) => {
        return parseDateSafely(b.releaseRolloutDate).getTime() - parseDateSafely(a.releaseRolloutDate).getTime();
      });
      
      return sortedReleases as ClientRelease[];
    } catch (error) {
      console.error('Error fetching releases:', error);
      throw error;
    }
  }

  async getRelease(id: string): Promise<ClientRelease | null> {
    try {
      const { resource } = await this.releasesContainer.item(id, id).read<ClientRelease>();
      return resource || null;
    } catch (error: any) {
      if (error.code === 404) {
        return null;
      }
      console.error('Error fetching release:', error);
      throw error;
    }
  }

  async createRelease(releaseData: CreateClientReleaseData, userEmail: string): Promise<ClientRelease> {
    try {
      // Get team name for the release
      const team = await this.getTeam(releaseData.teamId);
      const teamName = team ? team.teamName : 'Unknown Team';

      const release: ClientRelease = {
        id: uuidv4(),
        ...releaseData,
        teamName,
        createdBy: userEmail,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const { resource } = await this.releasesContainer.items.create(release);
      return resource as ClientRelease;
    } catch (error) {
      console.error('Error creating release:', error);
      throw error;
    }
  }

  async updateRelease(id: string, updates: Partial<CreateClientReleaseData>): Promise<ClientRelease> {
    try {
      const existingRelease = await this.getRelease(id);
      if (!existingRelease) {
        throw new Error(`Release with id ${id} not found`);
      }

      // Update team name if teamId changed
      let teamName = existingRelease.teamName;
      if (updates.teamId && updates.teamId !== existingRelease.teamId) {
        const team = await this.getTeam(updates.teamId);
        teamName = team ? team.teamName : 'Unknown Team';
      }

      const updatedRelease = {
        ...existingRelease,
        ...updates,
        teamName,
        updatedAt: new Date().toISOString(),
        id // Ensure ID doesn't change
      };

      const { resource } = await this.releasesContainer
        .item(id, id)
        .replace(updatedRelease);

      return resource as ClientRelease;
    } catch (error) {
      console.error('Error updating release:', error);
      throw error;
    }
  }

  async deleteRelease(id: string): Promise<void> {
    try {
      await this.releasesContainer.item(id, id).delete();
    } catch (error) {
      console.error('Error deleting release:', error);
      throw error;
    }
  }

  async getReleasesByDateRange(startDate: string, endDate: string): Promise<ClientRelease[]> {
    try {
      const { resources } = await this.releasesContainer.items
        .query({
          query: 'SELECT * FROM c WHERE c.releaseRolloutDate >= @startDate AND c.releaseRolloutDate <= @endDate',
          parameters: [
            { name: '@startDate', value: startDate },
            { name: '@endDate', value: endDate }
          ]
        })
        .fetchAll();
      
      return resources as ClientRelease[];
    } catch (error) {
      console.error('Error fetching releases by date range:', error);
      throw error;
    }
  }

  async getReleasesByTeam(teamId: string): Promise<ClientRelease[]> {
    try {
      const { resources } = await this.releasesContainer.items
        .query({
          query: 'SELECT * FROM c WHERE c.teamId = @teamId',
          parameters: [{ name: '@teamId', value: teamId }]
        })
        .fetchAll();
      
      return resources as ClientRelease[];
    } catch (error) {
      console.error('Error fetching releases by team:', error);
      throw error;
    }
  }

  private async migrateRelease(release: any): Promise<any> {
    let hasChanges = false;

    // Handle legacy platform structure
    if (release.platforms && !release.supportedPlatforms) {
      release.supportedPlatforms = {
        mobile: {
          enabled: release.platforms.mobile || false,
          ios: release.platforms.mobile || false,
          android: release.platforms.mobile || false,
        },
        web: {
          enabled: release.platforms.web || false,
          desktop: release.platforms.web || false,
          mobileWeb: false,
        },
        backend: {
          enabled: release.platforms.api || false,
        },
      };
      delete release.platforms;
      hasChanges = true;
    }

    // Handle iOS/iPhone field migration - migrate from iphone back to ios
    if (release.supportedPlatforms?.mobile) {
      if (release.supportedPlatforms.mobile.iphone !== undefined && release.supportedPlatforms.mobile.ios === undefined) {
        console.log(`🔄 Migrating release ${release.releaseName} from 'iphone' to 'ios'`);
        release.supportedPlatforms.mobile.ios = release.supportedPlatforms.mobile.iphone;
        delete release.supportedPlatforms.mobile.iphone;
        hasChanges = true;
      }
    }

    // Add releaseBranch if missing
    if (!release.releaseBranch) {
      release.releaseBranch = 'main';
      hasChanges = true;
    }

    // Update the document if changes were made
    if (hasChanges) {
      try {
        await this.releasesContainer.item(release.id, release.id).replace(release);
        console.log(`✅ Migrated release ${release.releaseName}`);
      } catch (error) {
        console.error(`❌ Failed to migrate release ${release.releaseName}:`, error);
      }
    }

    return release;
  }

  // RELEASE METRICS OPERATIONS
  async storeReleaseMetrics(releaseId: string, teamId: string, metricsType: string, metricsData: any, forceTimestamp: boolean = false): Promise<any> {
    const maxRetries = 5;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Get or create release document
        let release;
        let etag: string | undefined;
        
        try {
          const { resource, etag: currentEtag } = await this.releasesContainer.item(releaseId, releaseId).read();
          release = resource;
          etag = currentEtag;
        } catch (error: any) {
          if (error.code === 404) {
            // Get team info for creating release
            const team = await this.getTeam(teamId);
            if (!team) {
              throw new Error(`Team with id ${teamId} not found`);
            }

            // Create new release document if it doesn't exist
            release = {
              id: releaseId,
              teamId: team.id,
              teamName: team.teamName,
              releaseName: `Release-${releaseId}`,
              releaseType: 'minor' as const,
              releaseRolloutDate: new Date().toISOString(),
              releaseBranch: 'main',
              description: 'Auto-created for metrics storage',
              supportedPlatforms: {
                mobile: { enabled: false, ios: false, android: false },
                web: { enabled: true, desktop: true, mobileWeb: true },
                backend: { enabled: true }
              },
              createdBy: 'system',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              releaseMetrics: {}
            };
            
            const { resource: newRelease } = await this.releasesContainer.items.create(release);
            release = newRelease;
            console.log(`✅ Created new release document: ${releaseId}`);
          } else {
            throw error;
          }
        }

        // Check if the existing metrics data is significantly different from the new data
        const existingMetrics = release.releaseMetrics?.[metricsType];
        const isNewData = this.isMetricsDataDifferent(existingMetrics, metricsData, metricsType);
        
        // Preserve existing timestamp if data hasn't changed AND forceTimestamp is false
        // If forceTimestamp is true (user refresh), always use new timestamp
        let finalMetricsData = { ...metricsData };
        if (!isNewData && existingMetrics?.lastUpdated && !forceTimestamp) {
          finalMetricsData.lastUpdated = existingMetrics.lastUpdated;
          console.log(`📅 Preserving existing timestamp for ${metricsType} - no significant data changes detected`);
        } else if (forceTimestamp) {
          console.log(`🆕 Forcing new timestamp for ${metricsType} - manual refresh detected`);
        } else {
          console.log(`🆕 Using new timestamp for ${metricsType} - new data detected`);
        }

        // Update release with new metrics (preserving existing metrics)
        const updatedRelease = {
          ...release,
          releaseMetrics: {
            ...release.releaseMetrics,
            [metricsType]: finalMetricsData
          },
          lastMetricsUpdate: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Use ETag for optimistic concurrency control
        const replaceOptions = etag ? { accessCondition: { type: 'IfMatch', condition: etag } } : {};
        const { resource: result } = await this.releasesContainer.item(releaseId, releaseId).replace(updatedRelease, replaceOptions);
        
        console.log(`✅ Successfully stored ${metricsType} metrics for release: ${releaseId}${isNewData ? ' (new data)' : ' (preserved timestamp)'}`);
        return result;

      } catch (error: any) {
        // Check if it's a precondition failed error (concurrent modification)
        if (error.code === 412) {
          retryCount++;
          console.log(`⚠️ Concurrent modification detected for ${metricsType} metrics, retrying (${retryCount}/${maxRetries})...`);
          
          if (retryCount >= maxRetries) {
            console.error(`❌ Max retries exceeded for storing ${metricsType} metrics`);
            throw new Error(`Failed to store ${metricsType} metrics after ${maxRetries} retries due to concurrent modifications`);
          }
          
          // Add exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 100));
          continue; // Retry the operation
        }
        
        console.error(`❌ Error storing ${metricsType} metrics:`, error);
        throw error;
      }
    }
  }

  // Helper method to determine if metrics data has significantly changed
  private isMetricsDataDifferent(existingMetrics: any, newMetrics: any, metricsType: string): boolean {
    if (!existingMetrics) {
      return true; // No existing data, so this is new
    }

    // Compare key fields based on metric type
    switch (metricsType) {
      case 'codeQuality':
        return this.compareCodeQualityMetrics(existingMetrics, newMetrics);
      case 'codeCoverage':
        return this.compareCodeCoverageMetrics(existingMetrics, newMetrics);
      case 'testResults':
        return this.compareTestResultsMetrics(existingMetrics, newMetrics);
      case 'buildStability':
        return this.compareBuildStabilityMetrics(existingMetrics, newMetrics);
      case 'changeRequests':
        return this.compareChangeRequestsMetrics(existingMetrics, newMetrics);
      case 'changeLog':
        return this.compareChangelogMetrics(existingMetrics, newMetrics);
      default:
        // For unknown metric types, do a simple JSON comparison
        return JSON.stringify(existingMetrics.rawData || existingMetrics) !== JSON.stringify(newMetrics.rawData || newMetrics);
    }
  }

  private compareCodeQualityMetrics(existing: any, newData: any): boolean {
    const existingRaw = existing.rawData || existing;
    const newRaw = newData.rawData || newData;
    
    // Compare key SonarQube metrics
    const keyFields = ['bugs', 'vulnerabilities', 'code_smells', 'coverage', 'ncloc', 'reliability_rating', 'security_rating', 'sqale_rating'];
    return keyFields.some(field => existingRaw[field] !== newRaw[field]);
  }

  private compareCodeCoverageMetrics(existing: any, newData: any): boolean {
    const existingRaw = existing.rawData || existing;
    const newRaw = newData.rawData || newData;
    
    // Compare key coverage metrics
    const keyFields = ['coverage', 'lines_to_cover', 'uncovered_lines', 'new_coverage', 'new_lines_to_cover'];
    return keyFields.some(field => existingRaw[field] !== newRaw[field]);
  }

  private compareTestResultsMetrics(existing: any, newData: any): boolean {
    const existingRaw = existing.rawData || existing;
    const newRaw = newData.rawData || newData;
    
    // Compare key test metrics
    const keyFields = ['tests', 'test_failures', 'test_errors', 'test_execution_time'];
    return keyFields.some(field => existingRaw[field] !== newRaw[field]);
  }

  private compareBuildStabilityMetrics(existing: any, newData: any): boolean {
    const existingRaw = existing.rawData || existing.processedData || existing;
    const newRaw = newData.rawData || newData.processedData || newData;
    
    // Compare key build metrics
    if (existingRaw.statistics && newRaw.statistics) {
      const keyFields = ['total', 'success', 'failure', 'successRate', 'avgBuildTime'];
      return keyFields.some(field => existingRaw.statistics[field] !== newRaw.statistics[field]);
    }
    
    // Fallback to general comparison
    return JSON.stringify(existingRaw) !== JSON.stringify(newRaw);
  }

  private compareChangeRequestsMetrics(existing: any, newData: any): boolean {
    const existingItems = existing.rawData || existing.items || [];
    const newItems = newData.rawData || newData.items || [];
    
    // Compare count and latest request
    if (existingItems.length !== newItems.length) {
      return true;
    }
    
    // Compare latest request timestamp if available
    if (existingItems.length > 0 && newItems.length > 0) {
      const existingLatest = existingItems.sort((a: any, b: any) => 
        new Date(b.openedAt || b.opened_at).getTime() - new Date(a.openedAt || a.opened_at).getTime()
      )[0];
      const newLatest = newItems.sort((a: any, b: any) => 
        new Date(b.openedAt || b.opened_at).getTime() - new Date(a.openedAt || a.opened_at).getTime()
      )[0];
      
      return existingLatest.number !== newLatest.number || existingLatest.state !== newLatest.state;
    }
    
    return false;
  }

  private compareChangelogMetrics(existing: any, newData: any): boolean {
    const existingItems = existing.rawData || existing.pullRequests || [];
    const newItems = newData.rawData || newData.pullRequests || [];
    
    // Compare count and latest PR
    if (existingItems.length !== newItems.length) {
      return true;
    }
    
    // Compare latest PR timestamp if available
    if (existingItems.length > 0 && newItems.length > 0) {
      const existingLatest = existingItems.sort((a: any, b: any) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )[0];
      const newLatest = newItems.sort((a: any, b: any) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )[0];
      
      return existingLatest.number !== newLatest.number || existingLatest.state !== newLatest.state;
    }
    
    return false;
  }

  async getReleaseMetrics(releaseId: string): Promise<any> {
    try {
      const { resource } = await this.releasesContainer.item(releaseId, releaseId).read();
      
      if (!resource) {
        return null;
      }

      return {
        releaseId: resource.id,
        teamId: resource.teamId,
        teamName: resource.teamName,
        releaseMetrics: resource.releaseMetrics || {},
        confidenceMetrics: resource.confidenceMetrics || {},
        lastMetricsUpdate: resource.lastMetricsUpdate,
        updatedAt: resource.updatedAt
      };
    } catch (error: any) {
      if (error.code === 404) {
        return null;
      }
      console.error('Error fetching release metrics:', error);
      throw error;
    }
  }

  async updateConfidenceScore(releaseId: string): Promise<any> {
    try {
      console.log(`🔄 Updating confidence score for release: ${releaseId}`);

      // Get the full release document, not the transformed version
      const { resource: release } = await this.releasesContainer.item(releaseId, releaseId).read();
      
      if (!release) {
        console.error(`❌ Release not found: ${releaseId}`);
        return null;
      }

      const hasCodeQuality = release.releaseMetrics?.codeQuality?.score !== undefined;
      const hasCodeCoverage = release.releaseMetrics?.codeCoverage?.score !== undefined;
      const hasTestResults = release.releaseMetrics?.testResults?.score !== undefined;
      const hasBuildStability = release.releaseMetrics?.buildStability?.score !== undefined;
      const hasChangeRequests = release.releaseMetrics?.changeRequests?.score !== undefined;
      const hasChangeLog = release.releaseMetrics?.changeLog?.score !== undefined || release.releaseMetrics?.changelog?.score !== undefined;

      console.log('🔍 Confidence score calculation for', releaseId, ':', {
        hasCodeQuality,
        hasCodeCoverage,
        hasTestResults,
        hasBuildStability,
        hasChangeRequests,
        hasChangeLog,
        codeQualityScore: release.releaseMetrics?.codeQuality?.score,
        codeCoverageScore: release.releaseMetrics?.codeCoverage?.score,
        testResultsScore: release.releaseMetrics?.testResults?.score,
        buildStabilityScore: release.releaseMetrics?.buildStability?.score,
        changeRequestsScore: release.releaseMetrics?.changeRequests?.score,
        changeLogScore: release.releaseMetrics?.changeLog?.score || release.releaseMetrics?.changelog?.score
      });

      // Check if we have all core metrics (the 4 most important ones)
      const hasCoreMetrics = hasCodeQuality && hasCodeCoverage && hasTestResults && hasBuildStability;
      
      // Check if we have ALL 6 metrics for a complete confidence score
      const hasAllMetrics = hasCoreMetrics && hasChangeRequests && hasChangeLog;
      
      // We need ALL 6 metrics to calculate the final confidence score
      if (!hasAllMetrics) {
        console.log(`⏳ Waiting for all metrics (${hasCodeQuality ? '✓' : '✗'} CQ, ${hasCodeCoverage ? '✓' : '✗'} CC, ${hasTestResults ? '✓' : '✗'} TR, ${hasBuildStability ? '✓' : '✗'} BS, ${hasChangeRequests ? '✓' : '✗'} CR, ${hasChangeLog ? '✓' : '✗'} CL)`);
        
        // Don't update confidence score yet - keep existing or set to processing state
        const updatedRelease = {
          ...release,
          confidenceMetrics: {
            overallScore: 0, // Use 0 to indicate processing
            lastCalculated: new Date().toISOString(),
            weightings: { 
              codeQuality: 20, 
              codeCoverage: 20, 
              testResults: 20, 
              buildStability: 20, 
              changeRequests: 10, 
              changeLog: 10 
            },
            trending: { direction: 'stable' as const, changePercent: 0 },
            isProcessing: true // Add processing flag
          },
          lastMetricsUpdate: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        const { resource: result } = await this.releasesContainer.item(releaseId, releaseId).replace(updatedRelease);
        console.log(`⏳ Confidence score set to processing state - waiting for all 6 metrics`);
        return result;
      }

      // Calculate using new 6-metric system only when we have core metrics
      let totalScore = 0;
      let totalWeight = 0;
      const newWeightings = { 
        codeQuality: 20, 
        codeCoverage: 20, 
        testResults: 20, 
        buildStability: 20, 
        changeRequests: 10, 
        changeLog: 10 
      };

      // Code Quality metrics (SonarQube) - Core metric
      if (hasCodeQuality) {
        const weight = newWeightings.codeQuality;
        totalScore += release.releaseMetrics.codeQuality.score * (weight / 100);
        totalWeight += weight;
      }

      // Code Coverage metrics - Core metric
      if (hasCodeCoverage) {
        const weight = newWeightings.codeCoverage;
        totalScore += release.releaseMetrics.codeCoverage.score * (weight / 100);
        totalWeight += weight;
      }

      // Test Results metrics - Core metric
      if (hasTestResults) {
        const weight = newWeightings.testResults;
        totalScore += release.releaseMetrics.testResults.score * (weight / 100);
        totalWeight += weight;
      }

      // Build Stability metrics (Looper) - Core metric
      if (hasBuildStability) {
        const weight = newWeightings.buildStability;
        // Use the score directly (already calculated from success rate)
        const buildScore = release.releaseMetrics.buildStability.score || 0;
        totalScore += buildScore * (weight / 100);
        totalWeight += weight;
      }

      // Change Requests metrics - Optional (add if available)
      if (hasChangeRequests) {
        const weight = newWeightings.changeRequests;
        const changeRequestsScore = this.calculateChangeRequestsScore(release.releaseMetrics.changeRequests);
        totalScore += changeRequestsScore * (weight / 100);
        totalWeight += weight;
      }

      // Change Log metrics (GitHub PRs) - Optional (add if available)
      if (hasChangeLog) {
        const weight = newWeightings.changeLog;
        const changeLogData = release.releaseMetrics.changeLog || release.releaseMetrics.changelog;
        const changeLogScore = this.calculateChangeLogScore(changeLogData);
        totalScore += changeLogScore * (weight / 100);
        totalWeight += weight;
      }

      const overallScore = totalWeight > 0 ? Math.round(totalScore) : 0;

      console.log(`🎯 Calculated confidence score: ${overallScore}% (totalWeight: ${totalWeight})`);

      // Get previous score for trending
      const previousScore = release.confidenceMetrics?.overallScore;
      let trending: { direction: 'up' | 'down' | 'stable', changePercent: number, previousScore?: number } = { 
        direction: 'stable', 
        changePercent: 0, 
        previousScore 
      };
      
      if (previousScore !== undefined && previousScore > 0) { // Only calculate trending if we had a real previous score
        const change = overallScore - previousScore;
        const changePercent = Math.round((change / previousScore) * 100);
        trending = {
          direction: change > 2 ? 'up' : change < -2 ? 'down' : 'stable',
          changePercent: Math.abs(changePercent),
          previousScore
        };
      }

      const updatedRelease = {
        ...release,
        confidenceMetrics: {
          overallScore,
          lastCalculated: new Date().toISOString(),
          weightings: newWeightings,
          trending,
          isProcessing: false // Mark as complete
        },
        lastMetricsUpdate: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const { resource: result } = await this.releasesContainer.item(releaseId, releaseId).replace(updatedRelease);
      console.log(`✅ Confidence score updated successfully: ${overallScore}%`);
      return result;
    } catch (error: any) {
      console.error(`❌ Error updating confidence score for ${releaseId}:`, error.message);
      throw error;
    }
  }

  async updateReleaseConfidenceScore(releaseId: string, confidenceScore: number): Promise<void> {
    try {
      const { resource: release } = await this.releasesContainer.item(releaseId, releaseId).read();
      if (!release) {
        throw new Error(`Release with id ${releaseId} not found`);
      }

      const updatedRelease = {
        ...release,
        confidenceScore,
        updatedAt: new Date().toISOString()
      };

      await this.releasesContainer.item(releaseId, releaseId).replace(updatedRelease);
      console.log(`✅ Updated confidence score for release ${releaseId}: ${confidenceScore}`);
    } catch (error) {
      console.error('Error updating release confidence score:', error);
      throw error;
    }
  }

  // Helper function to calculate change requests score if missing
  private calculateChangeRequestsScore(changeRequestsData: any): number {
    if (changeRequestsData.score !== undefined) {
      return changeRequestsData.score;
    }
    
    // Calculate score based on stored data
    const items = changeRequestsData.items || changeRequestsData.rawData || [];
    if (!Array.isArray(items) || items.length === 0) return 100; // Perfect score for no change requests

    const totalRequests = items.length;
    const resolvedRequests = items.filter((cr: any) => 
      cr.state === 'Resolved' || cr.state === 'Closed'
    ).length;
    const openRequests = items.filter((cr: any) => 
      cr.state === 'Open' || cr.state === 'New'
    ).length;
    const highPriorityRequests = items.filter((cr: any) => 
      cr.priority === 'High' || cr.priority === 'Critical' || cr.priority === '1' || cr.priority === '1 - Critical'
    ).length;
    
    // Calculate score (lower is better for change requests)
    let score = 100; // Start with perfect score
    
    // Penalty for total number of change requests
    score -= Math.min(totalRequests * 2, 30); // Up to 30 points penalty
    
    // Penalty for unresolved requests
    score -= Math.min(openRequests * 5, 40); // Up to 40 points penalty
    
    // Extra penalty for high priority unresolved requests
    score -= Math.min(highPriorityRequests * 8, 30); // Up to 30 points penalty
    
    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  // Helper function to calculate changelog score if missing
  private calculateChangeLogScore(changeLogData: any): number {
    if (changeLogData.score !== undefined) {
      return changeLogData.score;
    }
    
    // Calculate score based on stored data
    const items = changeLogData.rawData || [];
    if (!Array.isArray(items) || items.length === 0) return 50; // Neutral score for no PRs

    const mergedPRs = items.filter((pr: any) => pr.status === 'merged').length;
    const openPRs = items.filter((pr: any) => pr.status === 'open').length;
    const closedPRs = items.filter((pr: any) => pr.status === 'closed').length;
    
    // Calculate score based on PR status distribution
    const totalPRs = items.length;
    const mergedRatio = mergedPRs / totalPRs;
    const openRatio = openPRs / totalPRs;
    
    // Higher score for more merged PRs, lower for abandoned (closed) PRs
    let score = 50; // Base score
    score += mergedRatio * 40; // Up to 40 points for merged PRs
    score += openRatio * 10; // Up to 10 points for open PRs (work in progress)
    
    // Ensure score is between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)));
  }
}

export default new CosmosService(); 