import axios from 'axios';
import * as sonarQubeConfig from '../config/sonarqube';
import { Team } from '../types/database';

interface SonarQubeMeasure {
  metric: string;
  value: string;
  period?: {
    index: number;
    value: string;
  };
}

interface SonarQubeComponent {
  key: string;
  name: string;
  measures: SonarQubeMeasure[];
}

interface SonarQubeMetricsResponse {
  component: SonarQubeComponent;
}

interface SonarQubeMetric {
  key: string;
  name: string;
  description?: string;
  domain?: string;
  type: string;
}

interface SonarQubeMetricsListResponse {
  metrics: SonarQubeMetric[];
  total: number;
  p: number;
  ps: number;
}

interface CodeQualityScore {
  score: number;
  metrics: {
    bugs_per_loc: number;
    vulnerabilities_per_loc: number;
    reliability_factor: number;
    security_factor: number;
    maintainability_factor: number;
    complexity_per_loc: number;
  };
  raw_metrics: {
    bugs: number;
    vulnerabilities: number;
    loc: number;
    complexity: number;
    reliability_rating: number;
    security_rating: number;
    maintainability_rating: number;
  };
}

// Health check cache for adaptive timeouts
interface ApiHealthResult {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}

const healthCache = new Map<string, { result: ApiHealthResult; timestamp: number }>();
const HEALTH_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

class SonarQubeService {
  /**
   * Check SonarQube API health for adaptive timeout configuration
   */
  private async checkSonarQubeHealth(baseUrl: string): Promise<ApiHealthResult> {
    const cacheKey = `sonarqube-health-${baseUrl}`;
    const cached = healthCache.get(cacheKey);
    
    // Return cached result if still valid
    if (cached && (Date.now() - cached.timestamp) < HEALTH_CACHE_DURATION) {
      return cached.result;
    }

    const startTime = Date.now();
    
    try {
      const config = await sonarQubeConfig.createSonarQubeConfig(baseUrl);
      const response = await axios.get(`${config.apiUrl}/system/status`, {
        headers: config.headers,
        timeout: 3000, // Quick health check timeout
        validateStatus: (status) => status < 500,
      });
      
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: true,
        responseTime,
      };
      
      // Cache the result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[SonarQube] Health check: API healthy (${responseTime}ms)`);
      
      return result;
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      const result: ApiHealthResult = {
        isHealthy: false,
        responseTime,
        error: error.message || 'Unknown error',
      };
      
      // Cache the negative result
      healthCache.set(cacheKey, { result, timestamp: Date.now() });
      console.log(`[SonarQube] Health check: API unhealthy (${error.message})`);
      
      return result;
    }
  }

  /**
   * Get adaptive timeout based on SonarQube API health
   */
  private async getAdaptiveTimeout(baseUrl: string): Promise<number> {
    try {
      const healthResult = await this.checkSonarQubeHealth(baseUrl);
      
      if (healthResult.isHealthy) {
        // API is healthy - use longer timeout for better reliability
        if (healthResult.responseTime < 1000) {
          console.log('[SonarQube] Using 30s timeout - API is responsive');
          return 30000; // 30 seconds
        } else {
          console.log('[SonarQube] Using 45s timeout - API is slow but healthy');
          return 45000; // 45 seconds
        }
      } else {
        // API is not responding well - use short timeout for quick fallback
        console.log('[SonarQube] Using 5s timeout - API appears unhealthy');
        return 5000; // 5 seconds
      }
    } catch (error) {
      // Health check failed - use short timeout
      console.log('[SonarQube] Using 5s timeout - health check failed');
      return 5000; // 5 seconds
    }
  }

  /**
   * Get project metrics using team metadata configuration (simplified for single-platform teams)
   */
  async getProjectMetricsForTeam(
    team: Team,
    metrics: string[] = [], 
    period?: string
  ): Promise<SonarQubeMetricsResponse> {
    if (!team.metadata.sonarqube_url) {
      throw new Error(`Team ${team.teamName} does not have sonarqube_url configured`);
    }
    
    if (!team.metadata.sonarqube_project_id) {
      throw new Error(`Team ${team.teamName} does not have sonarqube_project_id configured`);
    }

    return this.getProjectMetrics(
      team.metadata.sonarqube_url, 
      team.metadata.sonarqube_project_id, 
      metrics, 
      period
    );
  }

  /**
   * Get code quality score using team metadata configuration (simplified for single-platform teams)
   */
  async getCodeQualityScoreForTeam(team: Team): Promise<CodeQualityScore> {
    if (!team.metadata.sonarqube_url) {
      throw new Error(`Team ${team.teamName} does not have sonarqube_url configured`);
    }
    
    if (!team.metadata.sonarqube_project_id) {
      throw new Error(`Team ${team.teamName} does not have sonarqube_project_id configured`);
    }

    return this.getCodeQualityScore(
      team.metadata.sonarqube_url, 
      team.metadata.sonarqube_project_id
    );
  }

  /**
   * Get available metrics using team metadata configuration (simplified for single-platform teams)
   */
  async getAvailableMetricsForTeam(team: Team): Promise<SonarQubeMetricsListResponse> {
    if (!team.metadata.sonarqube_url) {
      throw new Error(`Team ${team.teamName} does not have sonarqube_url configured`);
    }
    return this.getAvailableMetrics(team.metadata.sonarqube_url);
  }

  // Updated methods that accept baseUrl parameter
  async getProjectMetrics(
    baseUrl: string,
    projectKey: string, 
    metrics: string[] = [], 
    period?: string
  ): Promise<SonarQubeMetricsResponse> {
    if (!baseUrl) throw new Error('SonarQube base URL is required');
    if (!projectKey) throw new Error('Project key is required');
    
    try {
      const config = await sonarQubeConfig.createSonarQubeConfig(baseUrl);
      const timeout = await this.getAdaptiveTimeout(baseUrl);
      
      const url = `${config.apiUrl}/measures/component`;
      const params: any = {
        component: projectKey
      };

      // Only add metricKeys if metrics are specified
      if (metrics && metrics.length > 0) {
        params.metricKeys = metrics.join(',');
      } else {
        // Provide default metrics when none are specified
        const defaultMetrics = [
          'bugs',
          'vulnerabilities', 
          'ncloc',
          'coverage',
          'duplicated_lines_density',
          'reliability_rating',
          'security_rating',
          'sqale_rating'
        ];
        params.metricKeys = defaultMetrics.join(',');
      }

      // Add period if specified
      if (period) {
        params.period = period;
      }

      console.log('🔍 Making request to SonarQube:', url, params);

      const response = await axios.get<SonarQubeMetricsResponse>(url, { 
        headers: config.headers,
        params,
        validateStatus: (status) => status < 500,
        timeout,
      });

      if (response.status !== 200) {
        console.error('❌ SonarQube API error:', {
          status: response.status,
          data: response.data
        });
        throw new Error(`SonarQube API error: ${response.status}`);
      }

      console.log('✅ Successfully retrieved project metrics');
      return response.data;
    } catch (error: any) {
      console.error(`❌ Failed to get metrics for project ${projectKey}:`, {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  async getAvailableMetrics(baseUrl: string): Promise<SonarQubeMetricsListResponse> {
    if (!baseUrl) throw new Error('SonarQube base URL is required');
    
    try {
      const config = await sonarQubeConfig.createSonarQubeConfig(baseUrl);
      const timeout = await this.getAdaptiveTimeout(baseUrl);
      
      const response = await axios.get<SonarQubeMetricsListResponse>(
        `${config.apiUrl}/metrics/search`,
        { 
          headers: config.headers,
          validateStatus: (status) => status < 500,
          timeout
        }
      );

      if (response.status !== 200) {
        throw new Error(`Failed to get metrics: ${response.status}`);
      }

      console.log('✅ Successfully retrieved available metrics');
      return response.data;
    } catch (error: any) {
      console.error('❌ Failed to get available metrics:', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  async getCodeQualityScore(baseUrl: string, projectKey: string): Promise<CodeQualityScore> {
    if (!baseUrl) throw new Error('SonarQube base URL is required');
    if (!projectKey) throw new Error('Project key is required');
    
    try {
      const metrics = [
        'bugs',
        'vulnerabilities',
        'ncloc',
        'complexity',
        'reliability_rating',
        'security_rating',
        'sqale_rating'
      ];

      const response = await this.getProjectMetrics(baseUrl, projectKey, metrics);
      const measures = response.component.measures;

      // Extract values
      const getValue = (metric: string): number => {
        const measure = measures.find(m => m.metric === metric);
        return measure ? parseFloat(measure.value) : 0;
      };

      const bugs = getValue('bugs');
      const vulnerabilities = getValue('vulnerabilities');
      const loc = getValue('ncloc');
      const complexity = getValue('complexity');
      const reliabilityRating = getValue('reliability_rating');
      const securityRating = getValue('security_rating');
      const sqaleRating = getValue('sqale_rating');

      // Calculate factors
      const reliabilityFactor = (5 - reliabilityRating) / 4;
      const securityFactor = (5 - securityRating) / 4;
      const maintainabilityFactor = (5 - sqaleRating) / 4;
      const bugsPerLoc = bugs / loc;
      const vulnerabilitiesPerLoc = vulnerabilities / loc;
      const complexityPerLoc = complexity / loc;

      // Weights
      const W1 = 0.25; // Bugs
      const W2 = 0.20; // Vulnerabilities
      const W3 = 0.20; // Reliability
      const W4 = 0.15; // Security
      const W5 = 0.10; // Maintainability
      const W6 = 0.10; // Complexity

      // Calculate final score
      const score = 100 - (
        (W1 * bugsPerLoc) +
        (W2 * vulnerabilitiesPerLoc) +
        (W3 * (1 - reliabilityFactor)) +
        (W4 * (1 - securityFactor)) +
        (W5 * (1 - maintainabilityFactor)) +
        (W6 * complexityPerLoc)
      ) * 100;

      const result: CodeQualityScore = {
        score: Math.round(score * 100) / 100, // Round to 2 decimal places
        metrics: {
          bugs_per_loc: Math.round(bugsPerLoc * 10000) / 10000,
          vulnerabilities_per_loc: Math.round(vulnerabilitiesPerLoc * 10000) / 10000,
          reliability_factor: Math.round(reliabilityFactor * 100) / 100,
          security_factor: Math.round(securityFactor * 100) / 100,
          maintainability_factor: Math.round(maintainabilityFactor * 100) / 100,
          complexity_per_loc: Math.round(complexityPerLoc * 10000) / 10000
        },
        raw_metrics: {
          bugs,
          vulnerabilities,
          loc,
          complexity,
          reliability_rating: reliabilityRating,
          security_rating: securityRating,
          maintainability_rating: sqaleRating
        }
      };

      console.log('✅ Successfully calculated code quality score');
      return result;
    } catch (error: any) {
      console.error(`❌ Failed to calculate quality score for project ${projectKey}:`, {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  // Legacy methods for backward compatibility (deprecated)
  async getProjectMetricsLegacy(
    projectKey: string, 
    metrics: string[] = [], 
    period?: string
  ): Promise<SonarQubeMetricsResponse> {
    console.warn('⚠️ getProjectMetricsLegacy() is deprecated. Use getProjectMetrics(baseUrl, projectKey, ...) instead.');
    
    if (!projectKey) throw new Error('Project key is required');
    
    try {
      const config = sonarQubeConfig.getConfig();
      
      const url = `${config.apiUrl}/measures/component`;
      const params: any = {
        component: projectKey
      };

      // Only add metricKeys if metrics are specified
      if (metrics && metrics.length > 0) {
        params.metricKeys = metrics.join(',');
      } else {
        // Provide default metrics when none are specified
        const defaultMetrics = [
          'bugs',
          'vulnerabilities', 
          'ncloc',
          'coverage',
          'duplicated_lines_density',
          'reliability_rating',
          'security_rating',
          'sqale_rating'
        ];
        params.metricKeys = defaultMetrics.join(',');
      }

      // Add period if specified
      if (period) {
        params.period = period;
      }

      console.log('🔍 Making request to SonarQube:', url, params);

      const response = await axios.get<SonarQubeMetricsResponse>(url, { 
        headers: config.headers,
        params,
        validateStatus: (status) => status < 500
      });

      if (response.status !== 200) {
        console.error('❌ SonarQube API error:', {
          status: response.status,
          data: response.data
        });
        throw new Error(`SonarQube API error: ${response.status}`);
      }

      console.log('✅ Successfully retrieved project metrics');
      return response.data;
    } catch (error: any) {
      console.error(`❌ Failed to get metrics for project ${projectKey}:`, {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  async getAvailableMetricsLegacy(): Promise<SonarQubeMetricsListResponse> {
    console.warn('⚠️ getAvailableMetricsLegacy() is deprecated. Use getAvailableMetrics(baseUrl) instead.');
    
    try {
      const config = sonarQubeConfig.getConfig();
      
      const response = await axios.get<SonarQubeMetricsListResponse>(
        `${config.apiUrl}/metrics/search`,
        { 
          headers: config.headers,
          validateStatus: (status) => status < 500
        }
      );

      if (response.status !== 200) {
        throw new Error(`Failed to get metrics: ${response.status}`);
      }

      console.log('✅ Successfully retrieved available metrics');
      return response.data;
    } catch (error: any) {
      console.error('❌ Failed to get available metrics:', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  async getCodeQualityScoreLegacy(projectKey: string): Promise<CodeQualityScore> {
    console.warn('⚠️ getCodeQualityScoreLegacy() is deprecated. Use getCodeQualityScore(baseUrl, projectKey) instead.');
    
    try {
      const metrics = [
        'bugs',
        'vulnerabilities',
        'ncloc',
        'complexity',
        'reliability_rating',
        'security_rating',
        'sqale_rating'
      ];

      const response = await this.getProjectMetricsLegacy(projectKey, metrics);
      const measures = response.component.measures;

      // Extract values
      const getValue = (metric: string): number => {
        const measure = measures.find(m => m.metric === metric);
        return measure ? parseFloat(measure.value) : 0;
      };

      const bugs = getValue('bugs');
      const vulnerabilities = getValue('vulnerabilities');
      const loc = getValue('ncloc');
      const complexity = getValue('complexity');
      const reliabilityRating = getValue('reliability_rating');
      const securityRating = getValue('security_rating');
      const sqaleRating = getValue('sqale_rating');

      // Calculate factors
      const reliabilityFactor = (5 - reliabilityRating) / 4;
      const securityFactor = (5 - securityRating) / 4;
      const maintainabilityFactor = (5 - sqaleRating) / 4;
      const bugsPerLoc = bugs / loc;
      const vulnerabilitiesPerLoc = vulnerabilities / loc;
      const complexityPerLoc = complexity / loc;

      // Weights
      const W1 = 0.25; // Bugs
      const W2 = 0.20; // Vulnerabilities
      const W3 = 0.20; // Reliability
      const W4 = 0.15; // Security
      const W5 = 0.10; // Maintainability
      const W6 = 0.10; // Complexity

      // Calculate final score
      const score = 100 - (
        (W1 * bugsPerLoc) +
        (W2 * vulnerabilitiesPerLoc) +
        (W3 * (1 - reliabilityFactor)) +
        (W4 * (1 - securityFactor)) +
        (W5 * (1 - maintainabilityFactor)) +
        (W6 * complexityPerLoc)
      ) * 100;

      const result: CodeQualityScore = {
        score: Math.round(score * 100) / 100, // Round to 2 decimal places
        metrics: {
          bugs_per_loc: Math.round(bugsPerLoc * 10000) / 10000,
          vulnerabilities_per_loc: Math.round(vulnerabilitiesPerLoc * 10000) / 10000,
          reliability_factor: Math.round(reliabilityFactor * 100) / 100,
          security_factor: Math.round(securityFactor * 100) / 100,
          maintainability_factor: Math.round(maintainabilityFactor * 100) / 100,
          complexity_per_loc: Math.round(complexityPerLoc * 10000) / 10000
        },
        raw_metrics: {
          bugs,
          vulnerabilities,
          loc,
          complexity,
          reliability_rating: reliabilityRating,
          security_rating: securityRating,
          maintainability_rating: sqaleRating
        }
      };

      console.log('✅ Successfully calculated code quality score');
      return result;
    } catch (error: any) {
      console.error(`❌ Failed to calculate quality score for project ${projectKey}:`, {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }
}

export default new SonarQubeService(); 