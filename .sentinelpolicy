#
#
# Code Scans
# Docs: https://gecgithub01.walmart.com/ISRM/wmsecestTraining/blob/master/.sentinelpolicy
#

maxmedium=1

#
#
# codescanpolicy  - This determines which preset checkmarx uses for scanning. By default this is OWASP TOP 10 - 2017.
#                  you may be requested to use the PCI preset if your application handles payment card data.  The valid
#                  settings are:
#                  All, OWASP TOP 10 - 2017*, PCI, SANS top 25, Android, HIPAA, Mobile, WordPress, Apple Secure Coding Guide
#
#
# scan policy needs to be only PCI for now to ensure the correct scan is run.
codescanpolicy=PCI

#
# suppressionkey - since line numbers and file names change over time the secrets scanner has been enhanced with a
#                  suppression key feature.  In the notification you will receive a code that can suppress that particular
#                  secret no matter what file, what file line, or what branch it belongs to.  If your file contains NON SECRETS
#                  flagged by the scanner use this efficiently quiet the scanner.  THIS SHOULD NOT be used to disable secrets
#                  detection.  A comment as to why you are suppressing the secret MUST be included.  The format is
#                  KEY_FROM_NOTIFICATION;comment.
#
suppressionkey=1bd0e85df;This is a constant for an key and does not contain any password.

#
#
# safefile - This is used by the secrets scanner to supress entire files that it detects.  This is mostly triggered by
#            filenames which may trigger the detection. The format is filename;comment. All fields
#            are required.
#
safefile=server/src/routes/releases.ts;not sensitive
safefile=server/src/services/githubService.ts;not sensitive information
