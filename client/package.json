{"name": "@engineering-excellence/client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/jspdf": "^1.3.3", "axios": "^1.10.0", "chart.js": "^4.4.9", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.1", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.20", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^2.1.8"}}