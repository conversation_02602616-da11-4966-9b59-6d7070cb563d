import React from 'react';
import type { Approval } from '../../services/signoffs';

interface CompletedApprovalsListProps {
  approvals: Approval[];
  expandedApprovals: Set<string>;
  onToggleExpansion: (approvalId: string) => void;
  stageId: string;
}

const CompletedApprovalsList: React.FC<CompletedApprovalsListProps> = ({
  approvals,
  expandedApprovals,
  onToggleExpansion,
  stageId
}) => {
  const completedApprovals = approvals.filter(a => a.status === 'approved');

  if (completedApprovals.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      <h5 className="font-medium text-gray-900 text-sm flex items-center space-x-2">
        <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Completed Approvals</span>
      </h5>
      {completedApprovals.map((approval) => {
        const isExpanded = expandedApprovals.has(`${stageId}-${approval.id}`);
        
        return (
          <div key={approval.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
            <div className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">
                      {/* Show sub-team name for functional QA, otherwise show stage name */}
                      {stageId === 'functional_qa' && approval.subTeam 
                        ? approval.subTeam.charAt(0).toUpperCase() + approval.subTeam.slice(1)
                        : 'Approval'}
                    </h4>
                    <div className="flex items-center space-x-3 mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-700 border border-emerald-300">
                        ✓ Approved
                      </span>
                      <span className="text-xs text-gray-500 font-medium">
                        {new Date(approval.approvedAt!).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          timeZoneName: 'short'
                        })}
                      </span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => onToggleExpansion(`${stageId}-${approval.id}`)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors flex-shrink-0"
                >
                  <svg className={`w-4 h-4 transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
              
              {/* Improved collapsed preview */}
              {!isExpanded && (
                <div className="mt-3 grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                    <span className="text-gray-600 text-xs font-medium">#</span>
                    <span className="font-mono text-xs text-gray-700 font-medium truncate">
                      {approval.sha ? approval.sha.substring(0, 8) : 'No SHA'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200 min-w-0">
                    <svg className="w-3 h-3 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    {approval.buildNumber && approval.buildNumber.startsWith('http') ? (
                      <a 
                        href={approval.buildNumber} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-semibold text-xs text-blue-700 hover:text-blue-800 truncate block"
                        title={approval.buildNumber}
                      >
                        Build Link
                      </a>
                    ) : (
                      <span className="font-semibold text-xs text-blue-700 truncate">
                        {approval.buildNumber || 'N/A'}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {isExpanded && (
              <div className="border-t border-gray-200 bg-gray-50">
                <div className="p-4 space-y-4">
                  {/* Approver Info */}
                  <div className="bg-white rounded-lg p-3 border border-gray-200">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                        <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span>Approver</span>
                      </span>
                      <span className="text-sm text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded">
                        {approval.approverEmail}
                      </span>
                    </div>
                  </div>
                  
                  {/* Timestamp */}
                  <div className="bg-white rounded-lg p-3 border border-gray-200">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                        <svg className="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>Approved At</span>
                      </span>
                      <span className="text-sm text-amber-600 font-medium bg-amber-50 px-2 py-1 rounded">
                        {new Date(approval.approvedAt!).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          timeZoneName: 'short'
                        })}
                      </span>
                    </div>
                  </div>
                  
                  {/* SHA and Build Number */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white rounded-lg p-3 border border-gray-200">
                      <div className="text-xs text-gray-600 mb-1 flex items-center space-x-1">
                        <span className="text-gray-500 font-mono text-sm">#</span>
                        <span>SHA</span>
                      </div>
                      <div className="font-mono text-sm text-gray-700 bg-gray-50 px-2 py-1 rounded border break-all">
                        {approval.sha || 'No SHA'}
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-lg p-3 border border-gray-200">
                      <div className="text-xs text-gray-600 mb-1 flex items-center space-x-1">
                        <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        <span>Build</span>
                      </div>
                      <div className="bg-blue-50 px-2 py-1 rounded border border-blue-200 min-w-0">
                        {approval.buildNumber && approval.buildNumber.startsWith('http') ? (
                          <a 
                            href={approval.buildNumber} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="font-semibold text-sm text-blue-700 hover:text-blue-800 break-all block"
                            title={approval.buildNumber}
                          >
                            {approval.buildNumber}
                          </a>
                        ) : (
                          <div className="font-semibold text-sm text-blue-700 break-all">
                            {approval.buildNumber || 'No build'}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Notes */}
                  <div className="bg-white rounded-lg p-3 border border-gray-200">
                    <div className="text-xs text-gray-600 mb-2 flex items-center space-x-1">
                      <svg className="w-3 h-3 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span>Notes</span>
                    </div>
                    <div className="text-sm text-gray-700 bg-orange-50 p-2 rounded border border-orange-200 break-words">
                      {approval.notes && approval.notes.startsWith('http') ? (
                        <a 
                          href={approval.notes} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 break-all"
                          title={approval.notes}
                        >
                          {approval.notes}
                        </a>
                      ) : (
                        approval.notes || 'No issues reported'
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default CompletedApprovalsList; 