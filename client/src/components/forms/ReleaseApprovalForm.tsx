import React from 'react';

interface ApprovalFormData {
  buildSha: string;
  buildNumber: string;
  knownIssues: string;
}

interface ApprovalFormProps {
  isAuthenticated: boolean;
  isSubmitting: boolean;
  authLoading: boolean;
  formData: ApprovalFormData;
  onFormDataChange: (data: ApprovalFormData) => void;
  onSubmit: () => void;
  selectedSubTeam?: string;
  onSubTeamChange?: (subTeam: string) => void;
  subTeamOptions?: string[];
  isAuthorized?: boolean;
  authorizationError?: string | null;
  showSubTeamSelector?: boolean;
}

const ApprovalForm: React.FC<ApprovalFormProps> = ({
  isAuthenticated,
  isSubmitting,
  authLoading,
  formData,
  onFormDataChange,
  onSubmit,
  selectedSubTeam = '',
  onSubTeamChange,
  subTeamOptions = [],
  isAuthorized = true,
  authorizationError,
  showSubTeamSelector = false
}) => {
  const handleInputChange = (field: keyof ApprovalFormData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value
    });
  };

  const canSubmit = isAuthenticated && 
                   (!showSubTeamSelector || selectedSubTeam) && 
                   isAuthorized && 
                   !isSubmitting && 
                   !authLoading;

  const getButtonContent = () => {
    if (!isAuthenticated) {
      return (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Sign In Required</span>
        </>
      );
    }

    if (isSubmitting) {
      return (
        <>
          <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Submitting...</span>
        </>
      );
    }

    if (authLoading) {
      return (
        <>
          <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Loading...</span>
        </>
      );
    }

    if (showSubTeamSelector && !selectedSubTeam) {
      return (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Select a Sub-team</span>
        </>
      );
    }

    if (!isAuthorized) {
      return (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Not Authorized</span>
        </>
      );
    }

    return (
      <>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Submit Approval</span>
      </>
    );
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
      <h4 className="font-medium text-gray-900 mb-3 text-sm">
        {showSubTeamSelector ? 'Submit Sub-team Approval' : 'Submit Approval'}
      </h4>
      
      {!isAuthenticated && (
        <div className="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-sm text-amber-800">Authentication required to submit approvals</span>
          </div>
        </div>
      )}

      {authorizationError && (
        <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-sm text-red-800">{authorizationError}</span>
          </div>
        </div>
      )}
      
      <div className="space-y-3">
        {showSubTeamSelector && onSubTeamChange && (
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">Select Sub-team</label>
            <select 
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white"
              value={selectedSubTeam}
              onChange={(e) => onSubTeamChange(e.target.value)}
            >
              <option value="">Choose a sub-team...</option>
              {subTeamOptions.map((option) => (
                <option key={option} value={option}>
                  {option.charAt(0).toUpperCase() + option.slice(1)} Team
                </option>
              ))}
            </select>
          </div>
        )}
        
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Test Suite / Execution Link</label>
          <input
            type="text"
            placeholder="Enter test suite or execution link..."
            value={formData.buildSha}
            onChange={(e) => handleInputChange('buildSha', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Build Link</label>
          <input
            type="text"
            placeholder="Build link..."
            value={formData.buildNumber}
            onChange={(e) => handleInputChange('buildNumber', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Known Issues</label>
          <textarea
            placeholder="Known issues..."
            rows={3}
            value={formData.knownIssues}
            onChange={(e) => handleInputChange('knownIssues', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
          />
        </div>
        
        <button
          onClick={onSubmit}
          className={`w-full px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm flex items-center justify-center space-x-2 ${
            canSubmit
              ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md border border-blue-600'
              : 'bg-gray-100 text-gray-500 cursor-not-allowed border border-gray-200'
          }`}
          disabled={!canSubmit}
        >
          {getButtonContent()}
        </button>
      </div>
    </div>
  );
};

export default ApprovalForm; 