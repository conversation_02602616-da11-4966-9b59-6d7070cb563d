import React from 'react';
import type { Release } from '../../services/release';
import { getPlatformIcons, formatDateSafely } from '../../utils/releaseHelpers';

interface ReleaseGridProps {
  releases: Release[];
  getTeamName: (teamId: string) => string;
  onReleaseClick: (releaseId: string) => void;
}

export const ReleaseGrid: React.FC<ReleaseGridProps> = ({ releases, getTeamName, onReleaseClick }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {releases.map((release) => (
        <div
          key={release.id}
          className="transform transition-all duration-200 hover:scale-105"
        >
          <div
            onClick={() => onReleaseClick(release.id)}
            className="bg-white rounded-xl p-4 border-2 border-gray-200 hover:border-blue-300 transition-all duration-300 hover:shadow-lg cursor-pointer"
          >
            {/* Header with team name and platform icons */}
            <div className="flex items-start justify-between mb-3">
              <h3 className="text-sm font-semibold text-gray-900 leading-tight">
                {getTeamName(release.teamId)}
              </h3>
              <div className="flex space-x-1.5">
                {getPlatformIcons(release.supportedPlatforms).map((platform, index) => (
                  <div
                    key={index}
                    className="w-6 h-6 rounded-lg bg-gray-50 border border-gray-200 flex items-center justify-center hover:bg-gray-100 transition-colors duration-200"
                    title={platform.name}
                  >
                    <span className="text-sm">{platform.icon}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Release name and type badge */}
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-bold text-gray-900 leading-tight pr-2">
                {release.releaseName}
              </h4>
            </div>

            {/* Release Date */}
            <div className="mb-4">
              <div className="flex items-center space-x-2 text-gray-600">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span className="text-sm font-medium">
                  {formatDateSafely(release.releaseRolloutDate)}
                </span>
              </div>
            </div>

            {/* Progress indicators */}
            <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 relative mb-1">
                  <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845
                        a 15.9155 15.9155 0 0 1 0 31.831
                        a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#F3F4F6"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845
                        a 15.9155 15.9155 0 0 1 0 31.831
                        a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#10B981"
                      strokeWidth="3"
                      strokeDasharray={`${(release.confidenceScore || 0)}, 100`}
                      strokeDashoffset={100 - (release.confidenceScore || 0)}
                      className="transition-all duration-300 ease-out"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-700">
                      {release.confidenceScore || 0}%
                    </span>
                  </div>
                </div>
                <span className="text-xs font-medium text-gray-700">Confidence</span>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 relative mb-1">
                  <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845
                        a 15.9155 15.9155 0 0 1 0 31.831
                        a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#F3F4F6"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845
                        a 15.9155 15.9155 0 0 1 0 31.831
                        a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#6366F1"
                      strokeWidth="3"
                      strokeDasharray={`${release.signoffProgress ?? 0}, 100`}
                      strokeLinecap="round"
                      className="transition-all duration-500 ease-out"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-bold text-gray-700">
                      {release.signoffProgress ?? 0}%
                    </span>
                  </div>
                </div>
                <span className="text-xs font-medium text-gray-700">Signoff</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}; 