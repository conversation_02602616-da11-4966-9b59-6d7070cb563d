import React from 'react';
import type { EnhancedRelease } from '../../services/release';
import type { CosmosTeam } from '../../services/api';
import { getPlatformIcons } from '../../utils/releaseHelpers';

interface ReleaseCardProps {
  release: EnhancedRelease;
  teams: CosmosTeam[];
  compact?: boolean;
  onClick?: () => void;
  isProcessing?: boolean;
}

const getReleaseTypeStyles = (type: EnhancedRelease['releaseType']) => {
  const typeStyles: Record<EnhancedRelease['releaseType'], { bg: string; text: string; icon: string; accent: string }> = {
    minor: { 
      bg: "bg-gradient-to-r from-blue-500 to-blue-600", 
      text: "text-white", 
      icon: "🔧",
      accent: "bg-blue-50 border-blue-200 text-blue-600"
    },
    major: { 
      bg: "bg-gradient-to-r from-purple-500 to-purple-600", 
      text: "text-white", 
      icon: "🚀",
      accent: "bg-purple-50 border-purple-200 text-purple-600"
    },
    patch: { 
      bg: "bg-gradient-to-r from-green-500 to-green-600", 
      text: "text-white", 
      icon: "🔨",
      accent: "bg-green-50 border-green-200 text-green-600"
    },
    hotfix: { 
      bg: "bg-gradient-to-r from-orange-500 to-orange-600", 
      text: "text-white", 
      icon: "🔥",
      accent: "bg-orange-50 border-orange-200 text-orange-600"
    }
  };

  return typeStyles[type] || typeStyles.minor;
};

const CircularProgress = ({ 
  value, 
  size = 32, 
  strokeWidth = 3, 
  color = "#3B82F6",
  backgroundColor = "#F3F4F6",
  label,
  showPercentage = true 
}: {
  value: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  label?: string;
  showPercentage?: boolean;
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (value / 100) * circumference;

  return (
    <div className="flex flex-col items-center space-y-1">
      <div className="relative" style={{ width: size, height: size }}>
        <svg className="transform -rotate-90" width={size} height={size}>
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={backgroundColor}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            className="transition-all duration-500 ease-out"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-bold text-gray-700">
            {showPercentage ? `${value}%` : value}
          </span>
        </div>
      </div>
      {label && (
        <span className="text-xs text-gray-500 font-medium">{label}</span>
      )}
    </div>
  );
};

const ReleaseCard: React.FC<ReleaseCardProps> = ({ release, teams, compact = false, onClick, isProcessing }) => {
  const typeStyle = getReleaseTypeStyles(release.releaseType);
  const platformIcons = getPlatformIcons(release.supportedPlatforms);
  const confidenceScore = release.confidenceMetrics?.overallScore || 0;
  const signOffProgress = release.signoffProgress ?? 0;

  const formatTeamName = (teamId: string): string => {
    const team = teams.find(t => t.id === teamId);
    return team?.teamName || 'Unknown Team';
  };

  if (compact) {
    return (
      <div 
        className={`group relative bg-gradient-to-br from-white to-gray-50 rounded-xl p-3 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1 overflow-hidden ${onClick ? 'cursor-pointer' : ''}`}
        onClick={onClick}
      >
        <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-gray-100/50 to-transparent rounded-bl-3xl"></div>
        
        <div className="flex items-start justify-between mb-2 relative z-10">
          <div className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-bold ${typeStyle.bg} ${typeStyle.text} shadow-sm`}>
            <span className="mr-1">{typeStyle.icon}</span>
            {release.releaseType.charAt(0).toUpperCase()}
          </div>
          <div className="flex space-x-1">
            {platformIcons.slice(0, 2).map((platform, index) => (
              <span key={index} className="text-sm opacity-70" title={platform.name}>
                {platform.icon}
              </span>
            ))}
          </div>
        </div>
        
        <h4 className="font-bold text-gray-900 text-sm line-clamp-1 mb-2">
          {release.releaseName}
        </h4>
        
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-500 font-medium truncate">
            {formatTeamName(release.teamId).split(' ')[0]}
          </span>
          <div className="flex space-x-2">
            {isProcessing ? (
              <>
                <div className="flex items-center space-x-1">
                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-gray-300 border-t-blue-500"></div>
                  <span className="text-gray-400 font-bold">...</span>
                </div>
                <span className="text-indigo-600 font-bold">{signOffProgress}%</span>
              </>
            ) : (
              <>
                <span className="text-green-600 font-bold">{confidenceScore}%</span>
                <span className="text-indigo-600 font-bold">{signOffProgress}%</span>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`bg-white rounded-xl p-4 border-2 border-gray-200 hover:border-blue-300 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/30 transform hover:-translate-y-0.5 my-1 ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {/* Header with team name and platform icons */}
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900 leading-tight">
          {formatTeamName(release.teamId)}
        </h3>
        <div className="flex space-x-1.5">
          {platformIcons.map((platform, index) => (
            <div
              key={index}
              className="w-6 h-6 rounded-lg bg-gray-50 border border-gray-200 flex items-center justify-center hover:bg-gray-100 transition-colors duration-200"
              title={platform.name}
            >
              <span className="text-sm">{platform.icon}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Release name and type badge */}
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-bold text-gray-900 leading-tight pr-2">
          {release.releaseName}
        </h4>
        <span className={`inline-block px-3 py-1.5 rounded-lg text-xs font-bold ${typeStyle.accent} border whitespace-nowrap`}>
          {release.releaseType.charAt(0).toUpperCase() + release.releaseType.slice(1)}
        </span>
      </div>

      {/* Progress indicators */}
      <div className="flex items-center justify-between bg-gradient-to-r from-gray-50/70 to-gray-50/30 rounded-lg p-3 border border-gray-100">
        <div className="flex flex-col items-center">
          {isProcessing ? (
            <div className="relative" style={{ width: 45, height: 45 }}>
              <div className="animate-spin rounded-full h-11 w-11 border-4 border-gray-200 border-t-blue-500"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-bold text-gray-400">...</span>
              </div>
            </div>
          ) : (
            <CircularProgress
              value={confidenceScore}
              size={45}
              strokeWidth={4}
              color="#10B981"
              backgroundColor="#F3F4F6"
              showPercentage={true}
            />
          )}
          <span className="text-xs font-medium text-gray-700 mt-2">
            {isProcessing ? 'Processing...' : 'Confidence'}
          </span>
        </div>
        
        <div className="flex flex-col items-center">
          <CircularProgress
            value={signOffProgress}
            size={45}
            strokeWidth={4}
            color="#6366F1"
            backgroundColor="#F3F4F6"
            showPercentage={true}
          />
          <span className="text-xs font-medium text-gray-700 mt-2">Signoff</span>
        </div>
      </div>
    </div>
  );
};

export default ReleaseCard; 