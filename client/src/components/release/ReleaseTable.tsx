import React from 'react';
import type { Release } from '../../services/release';
import { getPlatformIcons, formatDateSafely } from '../../utils/releaseHelpers';
import { useAuth } from '../../hooks/useAuth';

interface ReleaseTableProps {
  releases: Release[];
  getTeamName: (teamId: string) => string;
  onReleaseClick: (releaseId: string) => void;
  onPdfDownload: (release: Release, e: React.MouseEvent) => void;
  onSendEmail: (release: Release, e: React.MouseEvent) => void;
  onEditRelease?: (release: Release, e: React.MouseEvent) => void;
  pdfGenerating: Set<string>;
}

export const ReleaseTable: React.FC<ReleaseTableProps> = ({
  releases,
  getTeamName,
  onReleaseClick,
  onPdfDownload,
  onSendEmail,
  onEditRelease,
  pdfGenerating
}) => {
  const { user, isAuthenticated } = useAuth();
  
  return (
    <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-200/80 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100/80 backdrop-blur-sm">
            <tr className="border-b border-gray-200/60">
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Release</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Team</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Type</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Platforms</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Date</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Progress</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-widest">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white/50 backdrop-blur-sm divide-y divide-gray-100/80">
            {releases.map((release, index) => {
              const platformIcons = getPlatformIcons(release.supportedPlatforms);
              
              // Calculate confidence score with processing state handling
              const confidenceScore = (release as any).confidenceMetrics?.overallScore || 
                                    release.confidenceScore || 
                                    0;
              const isProcessing = (release as any).isConfidenceProcessing || 
                                 ((release as any).confidenceMetrics?.isProcessing) ||
                                 (confidenceScore === 0 && !(release as any).confidenceMetrics?.isProcessing === false);

              const signOffProgress = release.signoffProgress ?? 0;
              
              return (
                <tr
                  key={release.id}
                  onClick={() => onReleaseClick(release.id)}
                  className="group hover:bg-gradient-to-r hover:from-blue-50/80 hover:to-indigo-50/60 cursor-pointer transition-all duration-300 hover:shadow-sm hover:transform hover:scale-[1.005] hover:z-10 relative"
                >
                  <td className="px-6 py-3">
                    <div className="flex items-center space-x-3">
                      <div className={`w-1.5 h-10 rounded-full bg-gradient-to-b ${index % 4 === 0 ? 'from-blue-400 to-blue-600' : index % 4 === 1 ? 'from-purple-400 to-purple-600' : index % 4 === 2 ? 'from-green-400 to-green-600' : 'from-orange-400 to-orange-600'}`}></div>
                      <div>
                        <div className="text-base font-bold text-gray-900 group-hover:text-blue-700 transition-colors duration-300 leading-tight">
                          {release.releaseName}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center border border-gray-200/50 group-hover:border-gray-300/50 transition-all duration-300">
                        <span className="text-xs font-bold text-gray-600">
                          {getTeamName(release.teamId).split(' ').map(word => word[0]).join('').slice(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-bold text-gray-900 leading-tight">{getTeamName(release.teamId)}</div>
                        <div className="text-xs text-gray-500 mt-0.5">Development Team</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-3">
                    <div className="flex items-center">
                      <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-xs font-bold border transition-all duration-300 group-hover:scale-105 ${
                        release.releaseType === 'major' ? 'bg-gradient-to-r from-red-50 to-red-100 border-red-200 text-red-700' :
                        release.releaseType === 'minor' ? 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-700' :
                        release.releaseType === 'patch' ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 text-green-700' :
                        release.releaseType === 'hotfix' ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200 text-orange-700' :
                        'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200 text-gray-700'
                      }`}>
                        <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                          release.releaseType === 'major' ? 'bg-red-400' :
                          release.releaseType === 'minor' ? 'bg-blue-400' :
                          release.releaseType === 'patch' ? 'bg-green-400' :
                          release.releaseType === 'hotfix' ? 'bg-orange-400' :
                          'bg-gray-400'
                        }`}></div>
                        {release.releaseType.charAt(0).toUpperCase() + release.releaseType.slice(1)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-3">
                    <div className="flex items-center space-x-2">
                      {platformIcons.map((platform, idx) => (
                        <div
                          key={idx}
                          className="w-8 h-8 bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:bg-gradient-to-br group-hover:from-blue-50 group-hover:to-indigo-50 group-hover:border-blue-200/50"
                          title={platform.name}
                        >
                          <span className="text-sm">{platform.icon}</span>
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center border border-gray-200/50">
                        <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm font-bold text-gray-900 leading-tight">
                          {formatDateSafely(release.releaseRolloutDate)}
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">Release Date</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-3">
                    <div className="flex items-center space-x-6">
                      <div className="flex flex-col items-center">
                        <div className="w-10 h-10 relative">
                          {isProcessing ? (
                            // Processing indicator
                            <>
                              <svg className="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                                <path
                                  d="M18 2.0845
                                    a 15.9155 15.9155 0 0 1 0 31.831
                                    a 15.9155 15.9155 0 0 1 0 -31.831"
                                  fill="none"
                                  stroke="#F1F5F9"
                                  strokeWidth="2"
                                />
                                <path
                                  d="M18 2.0845
                                    a 15.9155 15.9155 0 0 1 0 31.831
                                    a 15.9155 15.9155 0 0 1 0 -31.831"
                                  fill="none"
                                  stroke="#3B82F6"
                                  strokeWidth="2"
                                  strokeDasharray="20, 100"
                                  strokeLinecap="round"
                                  className="animate-spin origin-center"
                                  style={{ transformOrigin: '50% 50%' }}
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-bold text-blue-600">...</span>
                              </div>
                            </>
                          ) : (
                            // Normal confidence score display
                            <>
                              <svg className="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                                <path
                                  d="M18 2.0845
                                    a 15.9155 15.9155 0 0 1 0 31.831
                                    a 15.9155 15.9155 0 0 1 0 -31.831"
                                  fill="none"
                                  stroke="#F1F5F9"
                                  strokeWidth="2"
                                />
                                <path
                                  d="M18 2.0845
                                    a 15.9155 15.9155 0 0 1 0 31.831
                                    a 15.9155 15.9155 0 0 1 0 -31.831"
                                  fill="none"
                                  stroke="url(#confidenceGradient)"
                                  strokeWidth="2"
                                  strokeDasharray={`${confidenceScore}, 100`}
                                  strokeLinecap="round"
                                />
                                <defs>
                                  <linearGradient id="confidenceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stopColor="#10B981" />
                                    <stop offset="100%" stopColor="#059669" />
                                  </linearGradient>
                                </defs>
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-black text-gray-800">{confidenceScore}%</span>
                              </div>
                            </>
                          )}
                        </div>
                        <span className="text-xs font-bold text-gray-600 mt-1 tracking-wide">
                          {isProcessing ? 'PROCESSING' : 'CONFIDENCE'}
                        </span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-10 h-10 relative">
                          <svg className="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                            <path
                              d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="#F1F5F9"
                              strokeWidth="2"
                            />
                            <path
                              d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="url(#signoffGradient)"
                              strokeWidth="2"
                              strokeDasharray={`${signOffProgress}, 100`}
                              strokeLinecap="round"
                            />
                            <defs>
                              <linearGradient id="signoffGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stopColor="#6366F1" />
                                <stop offset="100%" stopColor="#4F46E5" />
                              </linearGradient>
                            </defs>
                          </svg>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-xs font-black text-gray-800">{signOffProgress}%</span>
                          </div>
                        </div>
                        <span className="text-xs font-bold text-gray-600 mt-1 tracking-wide">SIGNOFF</span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-3">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={(e) => onPdfDownload(release, e)}
                        disabled={pdfGenerating.has(release.id)}
                        className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-300 group-hover:scale-110 ${
                          pdfGenerating.has(release.id)
                            ? 'bg-gradient-to-br from-gray-100 to-gray-200 border-gray-300 text-gray-500 cursor-not-allowed'
                            : 'bg-gradient-to-br from-red-50 to-red-100 border-red-200 text-red-600 hover:text-red-700 hover:bg-gradient-to-br hover:from-red-100 hover:to-red-200'
                        }`}
                        title={pdfGenerating.has(release.id) ? "Generating PDF..." : "Download PDF"}
                      >
                        {pdfGenerating.has(release.id) ? (
                          <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        )}
                      </button>
                      <button
                        onClick={(e) => onSendEmail(release, e)}
                        className="w-8 h-8 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200 flex items-center justify-center text-blue-600 hover:text-blue-700 hover:bg-gradient-to-br hover:from-blue-100 hover:to-blue-200 transition-all duration-300 group-hover:scale-110"
                        title="Send Email"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </button>

                      {/* Edit button - show only for creators */}
                      {isAuthenticated && user && user.email === release.createdBy && onEditRelease && (
                        <button
                          onClick={(e) => onEditRelease(release, e)}
                          className="w-8 h-8 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 flex items-center justify-center text-green-600 hover:text-green-700 hover:bg-gradient-to-br hover:from-green-100 hover:to-green-200 transition-all duration-300 group-hover:scale-110"
                          title={`Edit Release (Created by: ${release.createdBy || 'Unknown'})`}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}; 