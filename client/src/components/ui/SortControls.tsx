import React from 'react';
import type { SortField, SortOrder } from '../../hooks/useFiltersAndSorting';

interface SortOption {
  key: SortField;
  label: string;
}

interface SortControlsProps {
  sortField: SortField;
  sortOrder: SortOrder;
  onSort: (field: SortField) => void;
}

const sortOptions: SortOption[] = [
  { key: 'name', label: 'Name' },
  { key: 'date', label: 'Date' },
  { key: 'team', label: 'Team' },
  { key: 'updatedAt', label: 'Last Updated' }
];

export const SortControls: React.FC<SortControlsProps> = ({ sortField, sortOrder, onSort }) => {
  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-gray-600">Sort by:</span>
      <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
        {sortOptions.map((sort) => (
          <button
            key={sort.key}
            onClick={() => onSort(sort.key)}
            className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-all duration-200 ${
              sortField === sort.key
                ? 'bg-blue-600 text-white'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
          >
            <span>{sort.label}</span>
            {sortField === sort.key && (
              <svg className={`w-3 h-3 transition-transform duration-200 ${sortOrder === 'desc' ? 'rotate-180' : ''}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}; 