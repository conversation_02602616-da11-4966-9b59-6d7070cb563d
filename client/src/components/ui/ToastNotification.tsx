import React from 'react';
import type { ToastState } from '../../hooks/useToast';

interface ToastNotificationProps {
  toast: ToastState;
  onClose: () => void;
}

export const ToastNotification: React.FC<ToastNotificationProps> = ({ toast, onClose }) => {
  if (!toast.show) return null;

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2 duration-300">
      <div className={`rounded-lg p-4 shadow-lg border flex items-center space-x-3 min-w-80 ${
        toast.type === 'success' 
          ? 'bg-green-50 border-green-200 text-green-800'
          : toast.type === 'error'
          ? 'bg-red-50 border-red-200 text-red-800'
          : 'bg-blue-50 border-blue-200 text-blue-800'
      }`}>
        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
          toast.type === 'success' 
            ? 'bg-green-100'
            : toast.type === 'error'
            ? 'bg-red-100'
            : 'bg-blue-100'
        }`}>
          {toast.type === 'success' ? (
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ) : toast.type === 'error' ? (
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
        </div>
        <div className="flex-1">
          <p className="font-medium">{toast.message}</p>
        </div>
        <button
          onClick={onClose}
          className={`p-1 rounded-full transition-colors ${
            toast.type === 'success' 
              ? 'hover:bg-green-100'
              : toast.type === 'error'
              ? 'hover:bg-red-100'
              : 'hover:bg-blue-100'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
}; 