    import React from 'react';

interface EmptyStateProps {
  hasReleases: boolean;
  activeFiltersCount: number;
  onClearFilters: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ hasReleases, activeFiltersCount, onClearFilters }) => {
  return (
    <div className="flex flex-col items-center justify-center h-96 text-center">
      <div className="w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
        <svg className="w-10 h-10 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      </div>
      <h3 className="text-xl font-bold text-gray-700 mb-2">
        {!hasReleases ? 'No releases found' : 'No releases match your filters'}
      </h3>
      <p className="text-gray-500 mb-6">
        {!hasReleases ? 'Create your first release to get started' : 'Try adjusting your search criteria'}
      </p>
      {activeFiltersCount > 0 && (
        <button
          onClick={onClearFilters}
          className="px-6 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-200 font-medium shadow-lg"
        >
          Clear Filters
        </button>
      )}
    </div>
  );
}; 