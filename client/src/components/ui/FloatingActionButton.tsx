import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils';

interface FloatingActionButtonProps {
  singleAction?: boolean;
  actionIcon?: 'rocket' | 'plus' | 'calendar-timer';
  actionLabel?: string;
  onAction?: () => void;
  onCreateRelease?: () => void;
  className?: string;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({ 
  singleAction = true, 
  actionIcon = 'calendar-timer', 
  actionLabel = 'Create Release', 
  onAction,
  onCreateRelease,
  className
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowOptions(false);
      }
    };

    if (showOptions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showOptions]);

  const handleOptionSelect = (callback?: () => void) => {
    if (callback) callback();
    setShowOptions(false);
  };

  const getIcon = (icon: string) => {
    switch (icon) {
      case 'calendar-timer':
        return (
          <svg className="w-8 h-8" fill="none" viewBox="0 0 18 18">
            {/* Calendar body */}
            <rect x="1" y="4" width="15" height="12" rx="1.5" fill="#E5E7EB" stroke="#374151" strokeWidth="1.2"/>
            <rect x="1" y="2.5" width="15" height="3" rx="1.5" fill="#EF4444" stroke="#374151" strokeWidth="1.2"/>
            
            {/* Calendar rings/tabs */}
            <rect x="3.5" y="1" width="1.2" height="3.5" rx="0.6" fill="#374151"/>
            <rect x="6.5" y="1" width="1.2" height="3.5" rx="0.6" fill="#374151"/>
            <rect x="9.5" y="1" width="1.2" height="3.5" rx="0.6" fill="#374151"/>
            <rect x="12.5" y="1" width="1.2" height="3.5" rx="0.6" fill="#374151"/>
            
            {/* Calendar grid dots */}
            <rect x="3" y="7" width="1.5" height="0.8" fill="#374151"/>
            <rect x="6" y="7" width="1.5" height="0.8" fill="#374151"/>
            <rect x="9" y="7" width="1.5" height="0.8" fill="#374151"/>
            
            <rect x="3" y="9.5" width="1.5" height="0.8" fill="#374151"/>
            <rect x="6" y="9.5" width="1.5" height="0.8" fill="#374151"/>
            
            <rect x="3" y="12" width="1.5" height="0.8" fill="#374151"/>
            <rect x="6" y="12" width="1.5" height="0.8" fill="#374151"/>
            
            <rect x="3" y="14.5" width="1.5" height="0.8" fill="#374151"/>
            <rect x="6" y="14.5" width="1.5" height="0.8" fill="#374151"/>
            
            {/* Green circle with plus */}
            <circle cx="13.5" cy="13.5" r="3.8" fill="#22C55E" stroke="#374151" strokeWidth="1.2"/>
            <path d="M13.5 10.5v6M10.5 13.5h6" stroke="#374151" strokeWidth="1.3" strokeLinecap="round"/>
          </svg>
        );
      case 'rocket':
        return (
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
          </svg>
        );
      case 'plus':
        return (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        );
      default:
        return null;
    }
  };

  if (singleAction) {
    return (
      <div className={cn("fixed bottom-6 right-6 z-50", className)} ref={containerRef}>
        <button 
          className="relative bg-white hover:bg-gray-50 border-4 border-blue-600 hover:border-blue-700 p-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 overflow-hidden"
          onClick={onAction || onCreateRelease}
          aria-label={actionLabel}
        >
          <div className="relative z-10">
            {getIcon(actionIcon)}
          </div>
        </button>
      </div>
    );
  }

  return (
    <div className={cn("fixed bottom-6 right-6 z-50", className)} ref={containerRef}>
      {showOptions && (
        <div className="absolute bottom-16 right-0 bg-white rounded-lg shadow-xl border border-gray-200 py-2 min-w-[180px]">
          <button 
            className="w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            onClick={() => handleOptionSelect(onCreateRelease)}
          >
            <svg className="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Create Release</span>
          </button>
        </div>
      )}
      <button 
        className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300"
        onClick={() => setShowOptions(!showOptions)}
        aria-label="Add new"
      >
        {showOptions ? (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        )}
      </button>
    </div>
  );
};

export default FloatingActionButton; 