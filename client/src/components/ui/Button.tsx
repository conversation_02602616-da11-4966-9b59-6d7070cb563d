import React from 'react';
import { cn } from '../../utils';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', children, ...props }, ref) => {
    const getVariantClass = () => {
      switch(variant) {
        case 'destructive':
          return 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';
        case 'outline':
          return 'border border-gray-200 bg-white hover:bg-gray-50 focus:ring-blue-500';
        case 'secondary':
          return 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500';
        case 'ghost':
          return 'hover:bg-gray-100 focus:ring-gray-500';
        case 'link':
          return 'text-blue-600 underline-offset-4 hover:underline focus:ring-blue-500';
        default:
          return 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
      }
    };

    const getSizeClass = () => {
      switch(size) {
        case 'sm':
          return 'h-9 px-3 text-sm';
        case 'lg':
          return 'h-11 px-8 text-base';
        case 'icon':
          return 'h-10 w-10';
        default:
          return 'h-10 px-4 py-2 text-sm';
      }
    };

    return (
      <button
        className={cn(
          'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
          getVariantClass(),
          getSizeClass(),
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button };
