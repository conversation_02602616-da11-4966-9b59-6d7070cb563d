import React from 'react';
import type { FilterState } from '../../hooks/useFiltersAndSorting';
import type { CosmosTeam } from '../../services/api';

interface FilterControlsProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  teams: CosmosTeam[];
  availablePlatforms: string[];
  activeFiltersCount: number;
  onClearFilters: () => void;
}

export const FilterControls: React.FC<FilterControlsProps> = ({
  filters,
  onFiltersChange,
  teams,
  availablePlatforms,
  activeFiltersCount,
  onClearFilters
}) => {
  return (
    <>
      {/* Team Filter */}
      <select
        value={filters.team}
        onChange={(e) => onFiltersChange({ ...filters, team: e.target.value })}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
      >
        <option value="">All Teams</option>
        {teams.map(team => (
          <option key={team.id} value={team.teamName}>{team.teamName}</option>
        ))}
      </select>

      {/* Platform Filter */}
      <select
        value={filters.platform}
        onChange={(e) => onFiltersChange({ ...filters, platform: e.target.value })}
        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
      >
        <option value="">All Platforms</option>
        {availablePlatforms.map(platform => (
          <option key={platform} value={platform}>{platform}</option>
        ))}
      </select>

      {/* Clear Filters */}
      {activeFiltersCount > 0 && (
        <button
          onClick={onClearFilters}
          className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span>Clear ({activeFiltersCount})</span>
        </button>
      )}
    </>
  );
}; 