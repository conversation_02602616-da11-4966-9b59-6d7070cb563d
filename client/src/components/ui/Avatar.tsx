import React from 'react';
import { cn } from '../../utils';

interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  authProvider?: 'github' | 'azure' | 'saml' | 'pingfed';
}

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 'md',
  className,
  authProvider,
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base',
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map((part) => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getProviderColors = () => {
    switch (authProvider) {
      case 'github':
        return 'bg-gray-700 text-white';
      case 'azure':
        return 'bg-blue-600 text-white';
      case 'saml':
        return 'bg-purple-600 text-white';
      case 'pingfed':
        return 'bg-orange-600 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // If we have a valid image source, show it
  if (src) {
    return (
      <img
        src={src}
        alt={alt || name || 'User avatar'}
        className={cn(
          'rounded-full object-cover border-2 border-gray-200',
          sizeClasses[size],
          className
        )}
        onError={(e) => {
          // If image fails to load, hide it to show fallback
          (e.target as HTMLImageElement).style.display = 'none';
        }}
      />
    );
  }

  // Fallback to initials
  const displayName = name || alt || 'U';
  const initials = getInitials(displayName);

  return (
    <div
      className={cn(
        'rounded-full flex items-center justify-center font-medium border-2 border-gray-200',
        sizeClasses[size],
        getProviderColors(),
        className
      )}
      title={displayName}
    >
      {initials}
    </div>
  );
};

export default Avatar;
