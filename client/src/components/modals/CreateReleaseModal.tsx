import React, { useState, useEffect } from 'react';
import { cn } from '../../utils';

interface Team {
  id: string;
  name: string;
}

interface CreateReleaseFormData {
  releaseName: string;
  teamId: string;
  releaseRolloutDate: string;
  releaseType: 'minor' | 'major' | 'patch' | 'hotfix';
  releaseBranch: string;
  supportedPlatforms: {
    mobile: {
      enabled: boolean;
      ios: boolean;
      android: boolean;
    };
    web: {
      enabled: boolean;
      desktop: boolean;
      mobileWeb: boolean;
    };
    backend: {
      enabled: boolean;
    };
  };
  description?: string;
}

interface FormErrors {
  releaseName?: string;
  teamId?: string;
  releaseRolloutDate?: string;
  releaseType?: string;
  releaseBranch?: string;
  supportedPlatforms?: string;
  description?: string;
}

interface CreateReleaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateReleaseFormData) => Promise<void>;
  teams: Team[];
  isLoading?: boolean;
}

const CreateReleaseModal: React.FC<CreateReleaseModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  teams,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<CreateReleaseFormData>({
    releaseName: '',
    teamId: '',
    releaseRolloutDate: '',
    releaseType: 'minor',
    releaseBranch: '',
    supportedPlatforms: {
      mobile: {
        enabled: false,
        ios: false,
        android: false,
      },
      web: {
        enabled: false,
        desktop: false,
        mobileWeb: false,
      },
      backend: {
        enabled: false,
      },
    },
    description: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use isLoading to determine overall loading state
  const isFormDisabled = isLoading || isSubmitting;

  // Helper functions to determine platform selection state
  const isMobilePlatformSelected = () => {
    return formData.supportedPlatforms.mobile.ios || formData.supportedPlatforms.mobile.android;
  };

  const isWebPlatformSelected = () => {
    return formData.supportedPlatforms.web.desktop || formData.supportedPlatforms.web.mobileWeb;
  };

  const isPlatformShowingOptions = (platform: 'mobile' | 'web' | 'backend') => {
    return formData.supportedPlatforms[platform].enabled;
  };

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        releaseName: '',
        teamId: '',
        releaseRolloutDate: '',
        releaseType: 'minor',
        releaseBranch: '',
        supportedPlatforms: {
          mobile: {
            enabled: false,
            ios: false,
            android: false,
          },
          web: {
            enabled: false,
            desktop: false,
            mobileWeb: false,
          },
          backend: {
            enabled: false,
          },
        },
        description: '',
      });
      setErrors({});
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.releaseName.trim()) {
      newErrors.releaseName = 'Release name is required';
    }

    if (!formData.teamId) {
      newErrors.teamId = 'Team selection is required';
    }

    if (!formData.releaseRolloutDate) {
      newErrors.releaseRolloutDate = 'Release date is required';
    }

    if (!formData.releaseBranch.trim()) {
      newErrors.releaseBranch = 'Release branch is required';
    }

    const mobilePlatformSelected = isMobilePlatformSelected();
    const webPlatformSelected = isWebPlatformSelected();
    const backendSelected = formData.supportedPlatforms.backend.enabled;
    const platformsSelected = [mobilePlatformSelected, webPlatformSelected, backendSelected].filter(Boolean).length;

    if (platformsSelected === 0) {
      newErrors.supportedPlatforms = 'Please select one platform';
    } else if (platformsSelected > 1) {
      newErrors.supportedPlatforms = 'Please select only one platform';
    }

    // Check if platform is showing options but no sub-option selected
    if (isPlatformShowingOptions('mobile') && !isMobilePlatformSelected()) {
      newErrors.supportedPlatforms = 'Please select iOS or Android';
    }

    if (isPlatformShowingOptions('web') && !isWebPlatformSelected()) {
      newErrors.supportedPlatforms = 'Please select Desktop or Mobile Web';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Failed to create release:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof CreateReleaseFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const handlePlatformToggle = (platform: 'mobile' | 'web' | 'backend') => {
    setFormData((prev) => {
      // Reset all platforms first
      const resetState = {
        mobile: {
          enabled: false,
          ios: false,
          android: false,
        },
        web: {
          enabled: false,
          desktop: false,
          mobileWeb: false,
        },
        backend: {
          enabled: false,
        }
      };

      if (platform === 'backend') {
        const isCurrentlyEnabled = prev.supportedPlatforms.backend.enabled;
        // If backend is already enabled, disable all platforms
        if (isCurrentlyEnabled) {
          return {
            ...prev,
            supportedPlatforms: resetState
          };
        }
        // If backend is disabled, enable only backend
        return {
          ...prev,
          supportedPlatforms: {
            ...resetState,
            backend: {
              enabled: true
            }
          }
        };
      }

      // For mobile and web platforms, just reset all and enable the platform
      return {
        ...prev,
        supportedPlatforms: {
          ...resetState,
          [platform]: {
            ...prev.supportedPlatforms[platform],
            enabled: true
          }
        }
      };
    });

    // Clear platform error
    if (errors.supportedPlatforms) {
      setErrors((prev) => ({
        ...prev,
        supportedPlatforms: undefined,
      }));
    }
  };

  const handleSubPlatformToggle = (platform: 'mobile' | 'web', subPlatform: string) => {
    setFormData((prev) => {
      let newFormData;

      // Reset all platforms first
      const resetState = {
        mobile: {
          enabled: false,
          ios: false,
          android: false,
        },
        web: {
          enabled: false,
          desktop: false,
          mobileWeb: false,
        },
        backend: {
          enabled: false,
        }
      };

      if (platform === 'mobile') {
        // Set only the selected mobile option
        newFormData = {
          ...prev,
          supportedPlatforms: {
            ...resetState,
            mobile: {
              enabled: true,
              ios: subPlatform === 'ios',
              android: subPlatform === 'android'
            }
          }
        };
      } else if (platform === 'web') {
        // Set only the selected web option
        newFormData = {
          ...prev,
          supportedPlatforms: {
            ...resetState,
            web: {
              enabled: true,
              desktop: subPlatform === 'desktop',
              mobileWeb: subPlatform === 'mobileWeb'
            }
          }
        };
      } else {
        return prev;
      }

      return newFormData;
    });

    // Clear platform error
    if (errors.supportedPlatforms) {
      setErrors((prev) => ({
        ...prev,
        supportedPlatforms: undefined,
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4 text-center">
        {/* Enhanced Backdrop */}
        <div
          className="fixed inset-0 bg-gradient-to-br from-slate-900/80 via-gray-900/60 to-slate-800/80 backdrop-blur-sm transition-opacity"
          onClick={onClose}
        />

        {/* Enhanced Modal */}
        <div
          className="relative w-full max-w-2xl transform overflow-hidden rounded-3xl bg-white shadow-2xl transition-all border border-gray-100"
          style={{
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)',
          }}
        >
          {/* Gradient Header */}
          <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 px-6 py-4">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90"></div>
            <div className="relative flex items-center justify-between">
              <div className="text-left">
                <h3 className="text-xl font-bold text-white tracking-tight">Create New Release</h3>
                <p className="text-blue-100 text-xs mt-0.5 font-medium">
                  Configure your release settings and deployment targets
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-1.5 hover:bg-white/20 rounded-xl transition-all duration-200 group"
                disabled={isFormDisabled}
              >
                <svg
                  className="w-5 h-5 text-white group-hover:text-blue-100 transition-colors"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Form Content */}
          <div className="px-6 py-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Basic Information Section */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <h4 className="text-base font-semibold text-gray-900 text-left">
                    Basic Information
                  </h4>
                </div>

                {/* Release Name */}
                <div className="group">
                  <label
                    htmlFor="releaseName"
                    className="block text-xs font-semibold text-gray-700 mb-1 text-left"
                  >
                    Release Name *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="releaseName"
                      value={formData.releaseName}
                      onChange={(e) => handleInputChange('releaseName', e.target.value)}
                      className={cn(
                        'w-full px-3 py-2 border-2 rounded-xl focus:outline-none transition-all duration-200 bg-gray-50/50 hover:bg-white text-left',
                        'focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white',
                        errors.releaseName
                          ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                          : 'border-gray-200 hover:border-gray-300'
                      )}
                      placeholder="e.g., Release 25.02.31 - New Dashboard Features"
                      disabled={isFormDisabled}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                      <svg
                        className="w-4 h-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                        />
                      </svg>
                    </div>
                  </div>
                  {errors.releaseName && (
                    <p className="text-red-500 text-xs mt-1 flex items-center text-left">
                      <svg
                        className="w-3 h-3 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      {errors.releaseName}
                    </p>
                  )}
                </div>

                {/* Team and Release Type Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {/* Team Selection */}
                  <div className="group">
                    <label
                      htmlFor="teamId"
                      className="block text-xs font-semibold text-gray-700 mb-1 text-left"
                    >
                      Team *
                    </label>
                    <div className="relative">
                      <select
                        id="teamId"
                        value={formData.teamId}
                        onChange={(e) => handleInputChange('teamId', e.target.value)}
                        className={cn(
                          'w-full px-3 py-2 border-2 rounded-xl focus:outline-none transition-all duration-200 bg-gray-50/50 hover:bg-white appearance-none text-left',
                          'focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white',
                          errors.teamId
                            ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                            : 'border-gray-200 hover:border-gray-300'
                        )}
                        disabled={isFormDisabled}
                      >
                        <option value="">Select a team...</option>
                        {teams.map((team) => (
                          <option key={team.id} value={team.id}>
                            {team.name}
                          </option>
                        ))}
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                    </div>
                    {errors.teamId && (
                      <p className="text-red-500 text-xs mt-1 flex items-center text-left">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {errors.teamId}
                      </p>
                    )}
                  </div>

                  {/* Release Type */}
                  <div className="group">
                    <label
                      htmlFor="releaseType"
                      className="block text-xs font-semibold text-gray-700 mb-1 text-left"
                    >
                      Release Type
                    </label>
                    <div className="relative">
                      <select
                        id="releaseType"
                        value={formData.releaseType}
                        onChange={(e) =>
                          handleInputChange(
                            'releaseType',
                            e.target.value as CreateReleaseFormData['releaseType']
                          )
                        }
                        className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:outline-none transition-all duration-200 bg-gray-50/50 hover:bg-white appearance-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white text-left"
                        disabled={isFormDisabled}
                      >
                        <option value="patch">🔧 Patch - Bug fixes</option>
                        <option value="minor">✨ Minor - Feature Updates</option>
                        <option value="major">🚀 Major - New Features</option>
                        <option value="hotfix">🔥 Hotfix - Emergency fixes</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Release Branch and Date Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {/* Release Branch */}
                  <div className="group">
                    <label
                      htmlFor="releaseBranch"
                      className="block text-xs font-semibold text-gray-700 mb-1 text-left"
                    >
                      Release Branch *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="releaseBranch"
                        value={formData.releaseBranch}
                        onChange={(e) => handleInputChange('releaseBranch', e.target.value)}
                        className={cn(
                          'w-full px-3 py-2 border-2 rounded-xl focus:outline-none transition-all duration-200 bg-gray-50/50 hover:bg-white text-left',
                          'focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white',
                          errors.releaseBranch
                            ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                            : 'border-gray-200 hover:border-gray-300'
                        )}
                        placeholder="e.g., release/v2.5.0, main, develop"
                        disabled={isFormDisabled}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                          />
                        </svg>
                      </div>
                    </div>
                    {errors.releaseBranch && (
                      <p className="text-red-500 text-xs mt-1 flex items-center text-left">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {errors.releaseBranch}
                      </p>
                    )}
                  </div>

                  {/* Release Date */}
                  <div className="group">
                    <label
                      htmlFor="releaseDate"
                      className="block text-xs font-semibold text-gray-700 mb-1 text-left"
                    >
                      Release Date *
                    </label>
                    <input
                      type="date"
                      id="releaseDate"
                      value={formData.releaseRolloutDate}
                      onChange={(e) => handleInputChange('releaseRolloutDate', e.target.value)}
                      className={cn(
                        'w-full px-3 py-2 border-2 rounded-xl focus:outline-none transition-all duration-200 bg-gray-50/50 hover:bg-white text-left',
                        'focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white',
                        errors.releaseRolloutDate
                          ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                          : 'border-gray-200 hover:border-gray-300'
                      )}
                      disabled={isFormDisabled}
                    />
                    {errors.releaseRolloutDate && (
                      <p className="text-red-500 text-xs mt-1 flex items-center text-left">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {errors.releaseRolloutDate}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Supported Platforms Section */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                      />
                    </svg>
                  </div>
                  <div className="text-left">
                    <h4 className="text-base font-semibold text-gray-900">Supported Platform</h4>
                    <p className="text-xs text-gray-500">
                      Select deployment target for this release
                    </p>
                  </div>
                </div>

                {/* Main Platform Selection */}
                <div className="grid grid-cols-3 gap-3">
                  {/* Mobile Platform */}
                  <div
                    className={cn(
                      'p-3 rounded-xl border-2 transition-all duration-200 group h-32',
                      'hover:shadow-md hover:scale-[1.02]',
                      isMobilePlatformSelected()
                        ? 'border-blue-500 bg-blue-50 shadow-lg shadow-blue-500/20'
                        : isPlatformShowingOptions('mobile')
                          ? 'border-orange-300 bg-orange-50 shadow-lg shadow-orange-300/20'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                    )}
                  >
                    {!isPlatformShowingOptions('mobile') ? (
                      /* Platform Header - Unselected State */
                      <button
                        type="button"
                        onClick={() => handlePlatformToggle('mobile')}
                        className="w-full h-full flex flex-col items-center justify-center space-y-2"
                        disabled={isFormDisabled}
                      >
                        <div className="w-8 h-8 rounded-lg flex items-center justify-center text-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200 transition-all duration-200">
                          📱
                        </div>
                        <div className="text-center">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Platform
                          </div>
                          <div className="text-xs font-semibold text-gray-900">Mobile</div>
                        </div>
                        <div className="w-4 h-4 rounded-full border-2 border-gray-300 group-hover:border-gray-400 transition-all duration-200"></div>
                      </button>
                    ) : (
                      /* Platform Header + Options - Showing Options State */
                      <div className="h-full flex flex-col">
                        {/* Compact Header */}
                        <button
                          type="button"
                          onClick={() => handlePlatformToggle('mobile')}
                          className="flex items-center justify-between mb-2"
                          disabled={isFormDisabled}
                        >
                          <div className="flex items-center space-x-1.5">
                            <div
                              className={cn(
                                'w-6 h-6 rounded-lg flex items-center justify-center text-sm text-white shadow-lg',
                                isMobilePlatformSelected() ? 'bg-blue-500' : 'bg-orange-400'
                              )}
                            >
                              📱
                            </div>
                            <div className="text-left">
                              <div
                                className={cn(
                                  'text-xs font-medium uppercase tracking-wide',
                                  isMobilePlatformSelected() ? 'text-blue-600' : 'text-orange-600'
                                )}
                              >
                                Platform
                              </div>
                              <div
                                className={cn(
                                  'text-xs font-semibold',
                                  isMobilePlatformSelected() ? 'text-blue-700' : 'text-orange-700'
                                )}
                              >
                                Mobile
                              </div>
                            </div>
                          </div>
                          {isMobilePlatformSelected() ? (
                            <div className="w-4 h-4 rounded-full border-2 border-blue-500 bg-blue-500 flex items-center justify-center">
                              <svg
                                className="w-3 h-3 text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </div>
                          ) : (
                            <div className="w-4 h-4 rounded-full border-2 border-orange-400 group-hover:border-orange-500 transition-all duration-200"></div>
                          )}
                        </button>

                        {/* Sub-options */}
                        <div className="flex-1 space-y-1.5">
                          <div
                            className={cn(
                              'text-xs font-medium uppercase tracking-wide text-left mb-1',
                              isMobilePlatformSelected() ? 'text-blue-600' : 'text-orange-600'
                            )}
                          >
                            Select One Option
                          </div>
                          <div className="space-y-1">
                            <label
                              className={cn(
                                'flex items-center space-x-2 cursor-pointer rounded-lg transition-colors',
                                formData.supportedPlatforms.mobile.ios
                                  ? 'text-blue-600'
                                  : 'text-gray-700 hover:text-gray-900'
                              )}
                            >
                              <input
                                type="radio"
                                name="mobilePlatform"
                                checked={formData.supportedPlatforms.mobile.ios}
                                onChange={() => handleSubPlatformToggle('mobile', 'ios')}
                                className="w-3 h-3 text-blue-600 bg-white border-gray-300 focus:ring-blue-500 focus:ring-offset-0"
                                disabled={isFormDisabled}
                              />
                              <span className="text-xs font-medium">
                                📱 iOS
                              </span>
                            </label>
                            <label
                              className={cn(
                                'flex items-center space-x-2 cursor-pointer rounded-lg transition-colors',
                                formData.supportedPlatforms.mobile.android
                                  ? 'text-blue-600'
                                  : 'text-gray-700 hover:text-gray-900'
                              )}
                            >
                              <input
                                type="radio"
                                name="mobilePlatform"
                                checked={formData.supportedPlatforms.mobile.android}
                                onChange={() => handleSubPlatformToggle('mobile', 'android')}
                                className="w-3 h-3 text-blue-600 bg-white border-gray-300 focus:ring-blue-500 focus:ring-offset-0"
                                disabled={isFormDisabled}
                              />
                              <span className="text-xs font-medium">
                                🤖 Android
                              </span>
                            </label>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Web Platform */}
                  <div
                    className={cn(
                      'p-3 rounded-xl border-2 transition-all duration-200 group h-32',
                      'hover:shadow-md hover:scale-[1.02]',
                      isWebPlatformSelected()
                        ? 'border-blue-500 bg-blue-50 shadow-lg shadow-blue-500/20'
                        : isPlatformShowingOptions('web')
                          ? 'border-orange-300 bg-orange-50 shadow-lg shadow-orange-300/20'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                    )}
                  >
                    {!isPlatformShowingOptions('web') ? (
                      /* Platform Header - Unselected State */
                      <button
                        type="button"
                        onClick={() => handlePlatformToggle('web')}
                        className="w-full h-full flex flex-col items-center justify-center space-y-2"
                        disabled={isFormDisabled}
                      >
                        <div className="w-8 h-8 rounded-lg flex items-center justify-center text-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200 transition-all duration-200">
                          🌍
                        </div>
                        <div className="text-center">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Platform
                          </div>
                          <div className="text-xs font-semibold text-gray-900">Web</div>
                        </div>
                        <div className="w-4 h-4 rounded-full border-2 border-gray-300 group-hover:border-gray-400 transition-all duration-200"></div>
                      </button>
                    ) : (
                      /* Platform Header + Options - Showing Options State */
                      <div className="h-full flex flex-col">
                        {/* Compact Header */}
                        <button
                          type="button"
                          onClick={() => handlePlatformToggle('web')}
                          className="flex items-center justify-between mb-2"
                          disabled={isFormDisabled}
                        >
                          <div className="flex items-center space-x-1.5">
                            <div
                              className={cn(
                                'w-6 h-6 rounded-lg flex items-center justify-center text-sm text-white shadow-lg',
                                isWebPlatformSelected() ? 'bg-blue-500' : 'bg-orange-400'
                              )}
                            >
                              🌍
                            </div>
                            <div className="text-left">
                              <div
                                className={cn(
                                  'text-xs font-medium uppercase tracking-wide',
                                  isWebPlatformSelected() ? 'text-blue-600' : 'text-orange-600'
                                )}
                              >
                                Platform
                              </div>
                              <div
                                className={cn(
                                  'text-xs font-semibold',
                                  isWebPlatformSelected() ? 'text-blue-700' : 'text-orange-700'
                                )}
                              >
                                Web
                              </div>
                            </div>
                          </div>
                          {isWebPlatformSelected() ? (
                            <div className="w-4 h-4 rounded-full border-2 border-blue-500 bg-blue-500 flex items-center justify-center">
                              <svg
                                className="w-3 h-3 text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </div>
                          ) : (
                            <div className="w-4 h-4 rounded-full border-2 border-orange-400 group-hover:border-orange-500 transition-all duration-200"></div>
                          )}
                        </button>

                        {/* Sub-options */}
                        <div className="flex-1 space-y-1.5">
                          <div
                            className={cn(
                              'text-xs font-medium uppercase tracking-wide text-left mb-1',
                              isWebPlatformSelected() ? 'text-blue-600' : 'text-orange-600'
                            )}
                          >
                            Select One Option
                          </div>
                          <div className="space-y-1">
                            <label
                              className={cn(
                                'flex items-center space-x-2 cursor-pointer rounded-lg transition-colors',
                                formData.supportedPlatforms.web.desktop
                                  ? 'text-blue-600'
                                  : 'text-gray-700 hover:text-gray-900'
                              )}
                            >
                              <input
                                type="radio"
                                name="webPlatform"
                                checked={formData.supportedPlatforms.web.desktop}
                                onChange={() => handleSubPlatformToggle('web', 'desktop')}
                                className="w-3 h-3 text-blue-600 bg-white border-gray-300 focus:ring-blue-500 focus:ring-offset-0"
                                disabled={isFormDisabled}
                              />
                              <span className="text-xs font-medium">
                                🖥️ Desktop
                              </span>
                            </label>
                            <label
                              className={cn(
                                'flex items-center space-x-2 cursor-pointer rounded-lg transition-colors',
                                formData.supportedPlatforms.web.mobileWeb
                                  ? 'text-blue-600'
                                  : 'text-gray-700 hover:text-gray-900'
                              )}
                            >
                              <input
                                type="radio"
                                name="webPlatform"
                                checked={formData.supportedPlatforms.web.mobileWeb}
                                onChange={() => handleSubPlatformToggle('web', 'mobileWeb')}
                                className="w-3 h-3 text-blue-600 bg-white border-gray-300 focus:ring-blue-500 focus:ring-offset-0"
                                disabled={isFormDisabled}
                              />
                              <span className="text-xs font-medium">
                                🌐 Mobile Web
                              </span>
                            </label>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Backend Platform */}
                  <div
                    className={cn(
                      'p-3 rounded-xl border-2 transition-all duration-200 group h-32',
                      'hover:shadow-md hover:scale-[1.02]',
                      formData.supportedPlatforms.backend.enabled
                        ? 'border-blue-500 bg-blue-50 shadow-lg shadow-blue-500/20'
                        : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                    )}
                  >
                    {!formData.supportedPlatforms.backend.enabled ? (
                      /* Platform Header - Unselected State */
                      <button
                        type="button"
                        onClick={() => handlePlatformToggle('backend')}
                        className="w-full h-full flex flex-col items-center justify-center space-y-2"
                        disabled={isFormDisabled}
                      >
                        <div className="w-8 h-8 rounded-lg flex items-center justify-center text-lg bg-gray-100 text-gray-600 group-hover:bg-gray-200 transition-all duration-200">
                          🛠️
                        </div>
                        <div className="text-center">
                          <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                            Platform
                          </div>
                          <div className="text-xs font-semibold text-gray-900">Backend</div>
                        </div>
                        <div className="w-4 h-4 rounded-full border-2 border-gray-300 group-hover:border-gray-400 transition-all duration-200"></div>
                      </button>
                    ) : (
                      /* Platform Header + Confirmation - Selected State */
                      <div className="h-full flex flex-col items-center justify-center space-y-2">
                        {/* Compact Header */}
                        <button
                          type="button"
                          onClick={() => handlePlatformToggle('backend')}
                          className="flex items-center space-x-1.5"
                          disabled={isFormDisabled}
                        >
                          <div className="w-6 h-6 rounded-lg flex items-center justify-center text-sm bg-blue-500 text-white shadow-lg">
                            🛠️
                          </div>
                          <div className="text-left">
                            <div className="text-xs font-medium text-blue-600 uppercase tracking-wide">
                              Platform
                            </div>
                            <div className="text-xs font-semibold text-blue-700">Backend</div>
                          </div>
                          <div className="w-4 h-4 rounded-full border-2 border-blue-500 bg-blue-500 ml-1">
                            <svg
                              className="w-2.5 h-2.5 text-white m-0.5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={3}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          </div>
                        </button>

                        {/* Confirmation Message */}
                        <div className="text-center">
                          <div className="text-sm mb-1">✅</div>
                          <div className="text-xs text-blue-600 font-medium">
                            Backend services included
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {errors.supportedPlatforms && (
                  <p className="text-red-500 text-xs flex items-center text-left">
                    <svg
                      className="w-3 h-3 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    {errors.supportedPlatforms}
                  </p>
                )}
              </div>

              {/* Description Section */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div className="text-left">
                    <h4 className="text-base font-semibold text-gray-900">Description</h4>
                    <p className="text-xs text-gray-500">Optional details about this release</p>
                  </div>
                </div>

                <div className="group">
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border-2 border-gray-200 rounded-xl focus:outline-none transition-all duration-200 bg-gray-50/50 hover:bg-white focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white resize-none text-left"
                    placeholder="Brief description of the release features, bug fixes, or improvements..."
                    disabled={isFormDisabled}
                  />
                </div>
              </div>

              {/* Enhanced Actions */}
              <div className="flex justify-end space-x-3 pt-3 border-t border-gray-100 pb-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-xs font-semibold text-gray-700 bg-white border-2 border-gray-200 rounded-xl hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-4 focus:ring-gray-500/20 transition-all duration-200"
                  disabled={isFormDisabled}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 text-xs font-semibold text-white bg-gradient-to-r from-blue-600 to-purple-600 border border-transparent rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-500/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                  disabled={isFormDisabled}
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-3 w-3 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                      Creating Release...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <svg
                        className="w-3 h-3 mr-1.5 transform rotate-45"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                      Create Release
                    </div>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateReleaseModal;
