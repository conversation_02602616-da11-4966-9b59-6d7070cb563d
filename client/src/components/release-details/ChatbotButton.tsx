import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { ChatService } from '../../services/chat.service';
import type { ChatMessage } from '../../services/chat.service';
import { useToast } from '../../hooks/useToast';

interface ChatbotButtonProps {
  releaseId: string;
}

interface ExtendedChatMessage extends ChatMessage {
  isError?: boolean;
  originalMessage?: string;
}

const ChatbotButton: React.FC<ChatbotButtonProps> = ({ releaseId }) => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [messages, setMessages] = useState<ExtendedChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatService = ChatService.getInstance();
  const { showToast } = useToast();

  // Load existing conversation history when component mounts
  useEffect(() => {
    setMessages(chatService.getConversationHistory());
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const retryMessage = async (originalMessage: string) => {
    // Remove the error message
    setMessages(prev => prev.slice(0, -1));

    // Retry sending the message
    await sendMessage(originalMessage);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const trimmedMessage = inputMessage.trim();
    setInputMessage('');
    await sendMessage(trimmedMessage);
  };

  const sendMessage = async (message: string) => {
    try {
      setIsLoading(true);

      // Add user message immediately
      setMessages(prev => [...prev, { role: 'user', content: message }]);

      // Get response from server
      const response = await chatService.sendMessage(releaseId, message);

      // Add assistant response
      setMessages(prev => [...prev, response]);

      // Scroll to bottom after new message
      setTimeout(scrollToBottom, 100);
    } catch (error: any) {
      console.error('Error sending message:', error);

      // Determine error message based on error type
      let errorMessage = 'Failed to send message. Please try again.';
      let chatErrorMessage = 'Sorry, I encountered an error processing your message.';

      if (error?.response?.status === 408 || error?.message?.includes('timeout')) {
        errorMessage = 'Request timed out. Please try a shorter message or try again later.';
        chatErrorMessage = '⚠️ **Request Timed Out**\n\nYour message took too long to process. Please try:\n- Using a shorter message\n- Asking a more specific question\n- Trying again in a few moments';
      } else if (error?.response?.status === 429) {
        errorMessage = 'Service is busy. Please try again in a few moments.';
        chatErrorMessage = '⚠️ **Service Busy**\n\nThe chat service is currently experiencing high traffic. Please wait a moment and try again.';
      } else if (error?.response?.status === 404) {
        errorMessage = 'Release information not found.';
        chatErrorMessage = '⚠️ **Release Not Found**\n\nI couldn\'t find information for this release. Please check that the release exists.';
      } else if (error?.response?.status === 401) {
        errorMessage = 'Authentication error. Please contact support.';
        chatErrorMessage = '⚠️ **Authentication Error**\n\nThere was an authentication issue. Please contact support for assistance.';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
        chatErrorMessage = '⚠️ **Server Error**\n\nThere was a server error. Please try again in a few moments.';
      } else if (error?.code === 'NETWORK_ERROR' || !navigator.onLine) {
        errorMessage = 'Network error. Please check your connection.';
        chatErrorMessage = '⚠️ **Network Error**\n\nPlease check your internet connection and try again.';
      }

      // Show toast notification
      showToast(errorMessage, 'error');

      // Add error message to chat with retry option
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: chatErrorMessage,
        isError: true,
        originalMessage: message
      }]);

      // Scroll to bottom to show error message
      setTimeout(scrollToBottom, 100);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Button */}
      <button
        onClick={() => setIsChatOpen(true)}
        className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors shadow-sm hover:shadow-md"
        title="Ask questions about this release"
      >
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        <span className="text-sm font-medium">Chat</span>
      </button>

      {/* Chat Modal */}
      {isChatOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl h-[600px] mx-4 flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Release Assistant</h3>
              <button
                onClick={() => {
                  setIsChatOpen(false);
                  setMessages([]);
                  chatService.clearConversationHistory();
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            {/* Chat Messages Container */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {/* Welcome Message */}
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-3 bg-gray-100 rounded-lg p-3 max-w-[80%]">
                  <p className="text-sm text-gray-900">
                    Hello! I'm your release assistant. How can I help you with this release?
                  </p>
                </div>
              </div>

              {/* Chat Messages */}
              {messages.map((msg, index) => (
                <div key={index} className={`flex items-start ${msg.role === 'user' ? 'justify-end' : ''}`}>
                  {msg.role === 'assistant' && (
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                        <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                      </div>
                    </div>
                  )}
                  <div className={`${
                    msg.role === 'user'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  } rounded-lg p-3 max-w-[80%]`}>
                    {msg.role === 'user' ? (
                      <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                    ) : (
                      <div className="text-sm prose prose-sm max-w-none">
                        <ReactMarkdown
                          components={{
                            // Customize heading styles
                            h1: ({children}) => <h1 className="text-lg font-bold mb-2 text-gray-900">{children}</h1>,
                            h2: ({children}) => <h2 className="text-base font-semibold mb-2 text-gray-900">{children}</h2>,
                            h3: ({children}) => <h3 className="text-sm font-medium mb-1 text-gray-900">{children}</h3>,
                            // Customize list styles
                            ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                            ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                            li: ({children}) => <li className="text-gray-900">{children}</li>,
                            // Customize paragraph styles
                            p: ({children}) => <p className="mb-2 text-gray-900 last:mb-0">{children}</p>,
                            // Customize strong/bold text
                            strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                            // Customize code styles
                            code: ({children}) => <code className="bg-gray-200 px-1 py-0.5 rounded text-xs font-mono text-gray-800">{children}</code>,
                            // Customize blockquote
                            blockquote: ({children}) => <blockquote className="border-l-4 border-purple-300 pl-3 italic text-gray-700">{children}</blockquote>,
                          }}
                        >
                          {msg.content}
                        </ReactMarkdown>
                        {msg.isError && msg.originalMessage && (
                          <button
                            onClick={() => retryMessage(msg.originalMessage!)}
                            className="mt-2 px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors flex items-center space-x-1"
                            disabled={isLoading}
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span>Retry</span>
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                  {msg.role === 'user' && <div className="flex-shrink-0 ml-3 w-8" />}
                </div>
              ))}

              {/* Loading indicator */}
              {isLoading && (
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                      </svg>
                    </div>
                  </div>
                  <div className="bg-gray-100 text-gray-900 rounded-lg p-3 max-w-[80%]">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                      <span className="text-sm text-gray-500">Assistant is typing...</span>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="border-t p-4">
              <div className="flex items-center space-x-3">
                <textarea
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your message..."
                  className="flex-1 border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  rows={1}
                  disabled={isLoading}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={isLoading || !inputMessage.trim()}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:bg-purple-400 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Sending</span>
                    </>
                  ) : (
                    <span>Send</span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatbotButton; 