import React from 'react';
import { Doughn<PERSON> } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Too<PERSON><PERSON>, Legend } from 'chart.js';
import {
  BugAntIcon,
  ShieldCheckIcon,
  BeakerIcon,
  ChartBarIcon,
  ArrowPathIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
} from '@heroicons/react/24/outline';
import type { ConfidenceMetric } from '../../../types/metrics';

ChartJS.register(ArcElement, Tooltip, Legend);

interface ConfidenceScoreOverviewProps {
  confidenceMetrics: ConfidenceMetric[];
  overallConfidence: number;
  hoveredToolIndex: number | null;
  setHoveredToolIndex: React.Dispatch<React.SetStateAction<number | null>>;
  onViewDetails?: () => void;
}

const ConfidenceScoreOverview: React.FC<ConfidenceScoreOverviewProps> = ({
  confidenceMetrics,
  overallConfidence,
  hoveredToolIndex,
  setHoveredToolIndex,
}) => {
  // State to track scroll position for showing scroll up/down indicators
  const [showScrollUp, setShowScrollUp] = React.useState(false);
  const [showScrollDown, setShowScrollDown] = React.useState(confidenceMetrics.length > 4);

  // Determine if we're in processing state
  const isProcessing = overallConfidence === 0 && confidenceMetrics.length < 4;
  const hasPartialMetrics = confidenceMetrics.length > 0 && confidenceMetrics.length < 4;

  // Handle scroll events to show/hide scroll indicators
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    setShowScrollUp(scrollTop > 20);
    setShowScrollDown(confidenceMetrics.length > 4 && scrollTop < scrollHeight - clientHeight - 20);
  };

  // Scroll down function
  const scrollDown = () => {
    const container = document.getElementById('tool-scores-container');
    if (container) {
      // Scroll to the bottom of the container
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Scroll up function
  const scrollUp = () => {
    const container = document.getElementById('tool-scores-container');
    if (container) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };

  // Helper function to get the appropriate icon for each metric
  const getMetricIcon = (metricName: string, isHovered: boolean = false) => {
    const iconClass = `w-5 h-5 transition-all duration-300 ${isHovered ? 'scale-125' : ''}`;

    switch (metricName) {
      case 'Code Quality':
        return <BugAntIcon className={iconClass} />;
      case 'Code Coverage':
        return <ShieldCheckIcon className={iconClass} />;
      case 'Test Results':
        return <BeakerIcon className={iconClass} />;
      case 'Build Stability':
        return <WrenchScrewdriverIcon className={iconClass} />;
      case 'Change Requests':
        return <DocumentTextIcon className={iconClass} />;
      case 'Change Log':
        return <ClipboardDocumentListIcon className={iconClass} />;
      default:
        return <ChartBarIcon className={iconClass} />;
    }
  };

  // Show loading state when no metrics are available
  if (confidenceMetrics.length === 0) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-4">
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 mb-1">Release Quality Score</h2>
          <p className="text-gray-500 text-sm">Processing metrics data...</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 items-center w-full">
          {/* Loading Chart */}
          <div className="lg:col-span-3 flex justify-center">
            <div className="relative w-full h-[350px] flex justify-center items-center">
              <div className="relative w-full max-w-[320px] h-[320px] flex items-center justify-center">
                {/* Loading spinner */}
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="animate-spin rounded-full h-32 w-32 border-4 border-gray-200 border-t-blue-500 mb-4"></div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600 mb-1">Processing...</div>
                    <div className="text-gray-500 text-sm flex items-center justify-center space-x-2">
                      <ArrowPathIcon className="w-4 h-4 animate-spin" />
                      <span>Analyzing metrics data</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Loading Tool Cards */}
          <div className="lg:col-span-2 flex flex-col justify-center">
            <div className="space-y-3 h-full flex flex-col">
              <h3 className="text-lg font-semibold text-center text-gray-900 mb-6">
                Tool Scores & Weights
              </h3>
              {/* Full height container with scroll */}
              <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 pr-2 min-h-0">
                <div className="space-y-3">
                  {[
                    'Code Quality',
                    'Code Coverage',
                    'Test Results',
                    'Build Stability',
                    'Change Requests',
                    'Change Log',
                  ].map((name, _index) => (
                    <div
                      key={name}
                      className="relative p-3 border border-gray-200 rounded-lg bg-gray-50 animate-pulse"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-6 h-6 bg-gray-300 rounded"></div>
                          <div>
                            <div className="h-4 bg-gray-300 rounded w-20 mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="h-6 bg-gray-300 rounded w-12 mb-1"></div>
                          <div className="h-3 bg-gray-200 rounded w-8"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-4">
      <div className="text-center mb-4">
        <h2 className="text-xl font-bold text-gray-900 mb-1">Release Quality Score</h2>
        <p className="text-gray-500 text-sm">Overall confidence based on integrated tools</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 items-center w-full">
        {/* Doughnut Chart */}
        <div className="lg:col-span-3 flex justify-center">
          <div
            className="relative w-full h-[462px] flex justify-center items-center overflow-visible"
            style={{ zIndex: 1 }}
          >
            <div
              className="relative w-full max-w-[422px] h-[422px] overflow-visible"
              style={{
                filter: 'drop-shadow(0 6px 20px rgba(0,0,0,0.12))',
                zIndex: 1,
              }}
            >
              <Doughnut
                data={{
                  labels: isProcessing
                    ? ['Processing metrics...']
                    : confidenceMetrics.map((m) => m.name),
                  datasets: [
                    {
                      data: isProcessing ? [100] : confidenceMetrics.map((m) => m.score),
                      backgroundColor: isProcessing
                        ? ['#e5e7eb']
                        : confidenceMetrics.map((_, index) => {
                            const colors = [
                              '#f59e0b', // Code Quality - Orange
                              '#10b981', // Code Coverage - Green
                              '#3b82f6', // Test Results - Blue
                              '#06b6d4', // Build Stability - Cyan
                              '#ef4444', // Change Requests - Red
                              '#8b5cf6', // Change Log - Purple
                            ];
                            return hoveredToolIndex === index
                              ? colors[index] + 'FF'
                              : colors[index] + 'E6';
                          }),
                      borderWidth: isProcessing
                        ? 0
                        : confidenceMetrics.map((_, index) => (hoveredToolIndex === index ? 0 : 2)),
                      borderColor: isProcessing
                        ? 'transparent'
                        : confidenceMetrics.map((_, index) =>
                            hoveredToolIndex === index ? 'transparent' : '#ffffff'
                          ),
                      hoverBorderWidth: 0,
                      hoverOffset: isProcessing
                        ? 0
                        : confidenceMetrics.map((_, index) =>
                            hoveredToolIndex === index ? 15 : 0
                          ),
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  layout: {
                    padding: {
                      top: 20,
                      bottom: 20,
                      left: 20,
                      right: 20,
                    },
                  },
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      enabled: false,
                      external: function (context: any) {
                        if (isProcessing) return;

                        // Tooltip Element
                        let tooltipEl = document.getElementById('chartjs-tooltip');

                        // Create element on first render
                        if (!tooltipEl) {
                          tooltipEl = document.createElement('div');
                          tooltipEl.id = 'chartjs-tooltip';
                          tooltipEl.innerHTML = '<table></table>';
                          document.body.appendChild(tooltipEl);
                        }

                        // Hide if no tooltip
                        const tooltipModel = context.tooltip;
                        if (tooltipModel.opacity === 0) {
                          tooltipEl.style.opacity = '0';
                          return;
                        }

                        // Set caret Position
                        tooltipEl.classList.remove('above', 'below', 'no-transform');
                        if (tooltipModel.yAlign) {
                          tooltipEl.classList.add(tooltipModel.yAlign);
                        } else {
                          tooltipEl.classList.add('no-transform');
                        }

                        function getBody(bodyItem: any) {
                          return bodyItem.lines;
                        }

                        // Set Text
                        if (tooltipModel.body) {
                          const titleLines = tooltipModel.title || [];
                          const bodyLines = tooltipModel.body.map(getBody);

                          let innerHtml = '<thead>';

                          titleLines.forEach(function (title: string) {
                            innerHtml += '<tr><th>' + title + '</th></tr>';
                          });
                          innerHtml += '</thead><tbody>';

                          bodyLines.forEach(function (body: any, i: number) {
                            const colors = tooltipModel.labelColors[i];
                            let style = 'background:' + colors.backgroundColor;
                            style += '; border-color:' + colors.borderColor;
                            style += '; border-width: 2px';
                            const span = '<span style="' + style + '"></span>';
                            innerHtml += '<tr><td>' + span + body + '</td></tr>';
                          });
                          innerHtml += '</tbody>';

                          const tableRoot = tooltipEl.querySelector('table');
                          if (tableRoot) {
                            tableRoot.innerHTML = innerHtml;
                          }
                        }

                        const position = context.chart.canvas.getBoundingClientRect();
                        const bodyFont = context.tooltip.options.bodyFont;

                        // Display, position, and set styles for font
                        tooltipEl.style.opacity = '1';
                        tooltipEl.style.position = 'absolute';
                        tooltipEl.style.left =
                          position.left + window.pageXOffset + tooltipModel.caretX + 'px';
                        tooltipEl.style.top =
                          position.top + window.pageYOffset + tooltipModel.caretY - 80 + 'px';
                        tooltipEl.style.fontFamily = bodyFont.family;
                        tooltipEl.style.fontSize = bodyFont.size + 'px';
                        tooltipEl.style.fontStyle = bodyFont.style;
                        tooltipEl.style.padding =
                          tooltipModel.padding + 'px ' + tooltipModel.padding + 'px';
                        tooltipEl.style.pointerEvents = 'none';
                        tooltipEl.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                        tooltipEl.style.color = '#ffffff';
                        tooltipEl.style.borderRadius = '8px';
                        tooltipEl.style.border = '1px solid #ffffff';
                        tooltipEl.style.zIndex = '999999';
                      },
                      callbacks: {
                        label: (context: any) => {
                          const metric = confidenceMetrics[context.dataIndex];

                          // New 6-metric system weights
                          let weight = '10%'; // default for new metrics
                          if (metric.name === 'Code Quality') weight = '20%';
                          else if (metric.name === 'Code Coverage') weight = '20%';
                          else if (metric.name === 'Test Results') weight = '20%';
                          else if (metric.name === 'Build Stability') weight = '20%';
                          else if (metric.name === 'Change Requests') weight = '10%';
                          else if (metric.name === 'Change Log') weight = '10%';

                          return [
                            `${metric.name}: ${metric.score}%`,
                            `Weight: ${weight}`,
                            `Contribution: ${Math.round((metric.score * parseFloat(weight)) / 100)}%`,
                          ];
                        },
                      },
                    },
                  },
                  interaction: {
                    intersect: false,
                    mode: 'point',
                  },
                  elements: {
                    arc: {
                      borderWidth: 2,
                      borderColor: '#ffffff',
                      hoverBorderWidth: 0,
                      hoverBorderColor: 'transparent',
                    },
                  },
                  animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 300,
                  },
                  onHover: (_: any, elements: any) => {
                    if (isProcessing) return; // Disable hover during processing

                    if (elements.length > 0) {
                      const index = elements[0].index;
                      setHoveredToolIndex(index);
                    } else {
                      setHoveredToolIndex(null);
                    }
                  },
                }}
              />

              {/* 3D Center Content */}
              <div
                className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none"
                style={{ zIndex: -1 }}
              >
                <div
                  className="rounded-full flex flex-col items-center justify-center border border-gray-300 relative overflow-hidden cursor-pointer pointer-events-auto group transition-all duration-300 ease-out hover:scale-102"
                  style={{
                    width: '253px',
                    height: '253px',
                    background:
                      'radial-gradient(circle at 30% 30%, rgba(255,255,255,0.95), rgba(248,250,252,0.9), rgba(241,245,249,0.85))',
                    boxShadow:
                      '0 6px 20px rgba(0,0,0,0.12), inset 0 2px 0 rgba(255,255,255,0.9), inset 0 -2px 4px rgba(0,0,0,0.05)',
                    backdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255,255,255,0.4)',
                    zIndex: -1,
                  }}
                >
                  {/* Enhanced inner glow effect */}
                  <div
                    className="absolute inset-4 rounded-full opacity-30"
                    style={{
                      background:
                        'linear-gradient(145deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.1))',
                      boxShadow: 'inset 0 3px 6px rgba(59, 130, 246, 0.1)',
                    }}
                  ></div>

                  {/* Content with enhanced 3D text effect */}
                  <div className="relative z-10 text-center transform transition-transform duration-300 group-hover:scale-102">
                    {isProcessing ? (
                      // Processing state
                      <>
                        <div className="text-4xl font-black mb-2 text-blue-600 animate-pulse">
                          <div className="flex items-center justify-center space-x-1">
                            <div
                              className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                              style={{ animationDelay: '0ms' }}
                            ></div>
                            <div
                              className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                              style={{ animationDelay: '150ms' }}
                            ></div>
                            <div
                              className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                              style={{ animationDelay: '300ms' }}
                            ></div>
                          </div>
                        </div>
                        <div
                          className="text-lg uppercase tracking-widest font-bold"
                          style={{
                            background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                            letterSpacing: '0.4px',
                          }}
                        >
                          Processing
                        </div>
                        {hasPartialMetrics && (
                          <div className="text-sm text-gray-500 mt-1">
                            {confidenceMetrics.length}/4 metrics ready
                          </div>
                        )}
                      </>
                    ) : (
                      // Normal confidence score display
                      <>
                        <div
                          className="text-7xl font-black mb-1"
                          style={{
                            background:
                              'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            textShadow:
                              '0 4px 8px rgba(59, 130, 246, 0.25), 0 2px 4px rgba(0,0,0,0.1)',
                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.08))',
                          }}
                        >
                          {overallConfidence}%
                        </div>
                        <div
                          className="text-xl uppercase tracking-widest font-bold"
                          style={{
                            background: 'linear-gradient(135deg, #6b7280, #4b5563)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                            letterSpacing: '0.4px',
                          }}
                        >
                          Confidence
                        </div>
                      </>
                    )}
                  </div>

                  {/* Animated border ring */}
                  <div
                    className="absolute inset-0 rounded-full opacity-15 group-hover:opacity-25 transition-opacity duration-300"
                    style={{
                      background:
                        'conic-gradient(from 0deg, #3b82f6, #8b5cf6, #f59e0b, #10b981, #3b82f6)',
                      mask: 'radial-gradient(circle, transparent 75%, black 77%, black 100%)',
                      WebkitMask: 'radial-gradient(circle, transparent 75%, black 77%, black 100%)',
                    }}
                  ></div>

                  {/* Enhanced depth shadow */}
                  <div
                    className="absolute inset-0 rounded-full"
                    style={{
                      boxShadow:
                        'inset 0 6px 12px rgba(0,0,0,0.08), inset 0 -6px 12px rgba(255,255,255,0.15)',
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tool Weightages Cards */}
        <div className="lg:col-span-2 flex flex-col justify-center">
          <div className="space-y-3 h-full flex flex-col">
            <h3 className="text-lg font-semibold text-center text-gray-900 mb-3">
              Tool Scores & Weights
            </h3>
            {/* Increased height container to show 4 metrics clearly with actionable scroll */}
            <div className="relative h-96 overflow-hidden">
              {/* Top fade indicator */}
              <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white to-transparent z-10 pointer-events-none"></div>

              {/* Scroll up button - show when scrolled down */}
              {showScrollUp && (
                <button
                  onClick={scrollUp}
                  className="absolute top-2 right-4 z-20 flex items-center space-x-2 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full px-3 py-2 shadow-sm border border-gray-200 hover:shadow-md cursor-pointer"
                >
                  <div className="flex items-center space-x-1">
                    <svg
                      className="w-3 h-3 animate-bounce"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 10l7-7m0 0l7 7m-7-7v18"
                      />
                    </svg>
                    <span className="font-medium">scroll up</span>
                  </div>
                </button>
              )}

              {/* Scrollable content */}
              <div
                id="tool-scores-container"
                className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 pr-2 scroll-smooth"
                onScroll={handleScroll}
              >
                <div className="space-y-3 pt-2 pb-2">
                  {confidenceMetrics.map((metric, index) => {
                    const colors = [
                      '#f59e0b', // Code Quality - Orange
                      '#10b981', // Code Coverage - Green
                      '#3b82f6', // Test Results - Blue
                      '#06b6d4', // Build Stability - Cyan
                      '#ef4444', // Change Requests - Red
                      '#8b5cf6', // Change Log - Purple
                    ];
                    const color = colors[index];
                    const isHovered = hoveredToolIndex === index;

                    return (
                      <div
                        key={metric.id}
                        className={`relative p-3 border border-gray-200 rounded-lg bg-white transition-all duration-200 cursor-pointer transform overflow-hidden ${
                          isHovered ? 'scale-102 shadow-md' : 'hover:shadow-sm'
                        }`}
                        style={{
                          borderColor: isHovered ? color : color + '30',
                          backgroundColor: isHovered ? color + '05' : 'white',
                        }}
                        onMouseEnter={() => setHoveredToolIndex(index)}
                        onMouseLeave={() => setHoveredToolIndex(null)}
                      >
                        {/* Minimal glow effect - much more subtle */}
                        {isHovered && (
                          <div
                            className="absolute inset-0 rounded-lg opacity-10 pointer-events-none"
                            style={{
                              background: `linear-gradient(135deg, ${color}20, transparent)`,
                            }}
                          ></div>
                        )}

                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <div
                                className={`w-5 h-5 rounded-full transition-all duration-200 ${
                                  isHovered ? 'scale-110' : ''
                                }`}
                                style={{
                                  backgroundColor: color,
                                }}
                              />
                              <div className="flex-1">
                                <div
                                  className={`font-semibold text-gray-900 text-sm transition-all duration-200 flex items-center space-x-2 ${
                                    isHovered ? 'font-bold' : ''
                                  }`}
                                >
                                  {getMetricIcon(metric.name, isHovered)}
                                  <span>{metric.name}</span>
                                </div>
                                <div className="text-xs text-gray-500 mt-0.5">{metric.label}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div
                                className={`text-xl font-bold transition-all duration-200 ${
                                  isHovered ? 'scale-105' : ''
                                }`}
                                style={{
                                  color,
                                }}
                              >
                                {metric.score}%
                              </div>
                              <div className="text-xs text-gray-400 mt-0.5">
                                {metric.name === 'Code Quality'
                                  ? '20% weight'
                                  : metric.name === 'Code Coverage'
                                    ? '20% weight'
                                    : metric.name === 'Test Results'
                                      ? '20% weight'
                                      : metric.name === 'Build Stability'
                                        ? '20% weight'
                                        : metric.name === 'Change Requests'
                                          ? '10% weight'
                                          : metric.name === 'Change Log'
                                            ? '10% weight'
                                            : '10% weight'}
                              </div>
                            </div>
                          </div>

                          {/* Simplified progress bar */}
                          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                            <div
                              className="h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${metric.score}%`,
                                backgroundColor: color,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Bottom fade indicator */}
              <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white to-transparent z-10 pointer-events-none"></div>

              {/* Scroll down button - show when there are more metrics below */}
              {showScrollDown && (
                <button
                  onClick={scrollDown}
                  className="absolute bottom-2 right-4 z-20 flex items-center space-x-2 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full px-3 py-2 shadow-sm border border-gray-200 hover:shadow-md cursor-pointer"
                >
                  <div className="flex items-center space-x-1">
                    <svg
                      className="w-3 h-3 animate-bounce"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 14l-7 7m0 0l-7-7m7 7V3"
                      />
                    </svg>
                    <span className="font-medium">scroll down</span>
                  </div>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfidenceScoreOverview;

// Add CSS to ensure tooltips are always visible
const tooltipStyle = document.createElement('style');
tooltipStyle.textContent = `
  #chartjs-tooltip {
    z-index: 999999 !important;
    position: absolute !important;
    pointer-events: none !important;
  }
  
  .chartjs-tooltip {
    z-index: 999999 !important;
    position: fixed !important;
  }
  
  .chartjs-render-monitor {
    position: relative !important;
    z-index: 1 !important;
  }
  
  canvas {
    position: relative !important;
    z-index: 1 !important;
  }
  
  div[role="tooltip"] {
    z-index: 999999 !important;
    position: fixed !important;
  }
`;
if (!document.head.querySelector('[data-tooltip-style]')) {
  tooltipStyle.setAttribute('data-tooltip-style', 'true');
  document.head.appendChild(tooltipStyle);
}
