import type { CosmosTeam } from '../../../services/api';
import type { ErrorInfo } from './ErrorDisplay';

export const classifyError = (error: any, team?: CosmosTeam): ErrorInfo => {
  // No team provided
  if (!team) {
    return {
      type: 'no_team',
      title: 'No Team Selected',
      message: 'Please select a team to view metrics.',
      icon: '🔍',
      color: 'blue',
      suggestions: [
        'Navigate back and select a team from the releases page',
        'Ensure the release is associated with a valid team'
      ],
      showRetry: false
    };
  }

  // Missing metadata
  if (!team.metadata) {
    return {
      type: 'missing_metadata',
      title: 'Team Setup Required',
      message: 'This team has not been configured with metadata.',
      icon: '⚙️',
      color: 'yellow',
      suggestions: [
        'Contact your team administrator to configure team metadata',
        'Ensure the team YAML file includes metadata section',
        'Check if the team was properly loaded from the configuration files'
      ],
      showRetry: false
    };
  }

  // Missing SonarQube configuration
  if (!team.metadata.sonarqube_url || !team.metadata.sonarqube_project_id) {
    return {
      type: 'missing_metadata',
      title: 'SonarQube Setup Required',
      message: 'This team is missing SonarQube configuration in metadata.',
      icon: '📊',
      color: 'yellow',
      suggestions: [
        'Add "sonarqube_url" to team metadata',
        'Add "sonarqube_project_id" to team metadata',
        'Update the team YAML file with SonarQube configuration',
        'Contact your DevOps team for SonarQube project setup'
      ],
      showRetry: false
    };
  }

  // Network and connectivity errors
  if (error?.code === 'ENOTFOUND' || error?.message?.includes('getaddrinfo ENOTFOUND')) {
    const hostname = error?.message?.match(/getaddrinfo ENOTFOUND (.+)/)?.[1] || team.metadata.sonarqube_url;
    return {
      type: 'network_error',
      title: 'Connection Failed',
      message: `Unable to reach SonarQube server: ${hostname}`,
      icon: '🌐',
      color: 'red',
      suggestions: [
        'Connect to your corporate VPN if accessing internal servers',
        'Check your internet connection',
        'Verify the SonarQube server URL is correct',
        'Ensure you have network access to the corporate domain',
        'Contact your network administrator if the issue persists'
      ],
      showRetry: true
    };
  }

  // VPN/Internal network issues
  if (error?.code === 'ECONNREFUSED' || error?.code === 'ETIMEDOUT') {
    return {
      type: 'vpn_required',
      title: 'VPN Required',
      message: 'Unable to connect to internal SonarQube server.',
      icon: '🔒',
      color: 'orange',
      suggestions: [
        'Connect to your corporate VPN to access internal servers',
        'Verify VPN connection is active and stable',
        'Check if you can access other internal company resources',
        'Try disconnecting and reconnecting to VPN',
        'Contact IT support if VPN issues persist'
      ],
      showRetry: true
    };
  }

  // HTTP status errors
  if (error?.response?.status) {
    const status = error.response.status;
    
    if (status === 404) {
      return {
        type: 'not_found',
        title: 'Project Not Found',
        message: `Project "${team.metadata.sonarqube_project_id}" not found on SonarQube server.`,
        icon: '🔍',
        color: 'yellow',
        suggestions: [
          'Verify the SonarQube project ID is correct',
          'Check if the project exists on the SonarQube server',
          'Ensure the project has been analyzed recently',
          'Contact your DevOps team to verify project configuration'
        ],
        showRetry: true
      };
    }
    
    if (status === 401 || status === 403) {
      return {
        type: 'unauthorized',
        title: 'Access Denied',
        message: 'Unable to authenticate with SonarQube server.',
        icon: '🔐',
        color: 'red',
        suggestions: [
          'Check SonarQube authentication token',
          'Verify token has not expired',
          'Ensure proper permissions for the project',
          'Contact your SonarQube administrator'
        ],
        showRetry: true
      };
    }
    
    if (status >= 500) {
      return {
        type: 'server_error',
        title: 'Server Unavailable',
        message: 'SonarQube server is currently experiencing issues.',
        icon: '🚨',
        color: 'red',
        suggestions: [
          'Verify you are connected to the corporate network/VPN',
          'SonarQube server may be temporarily unavailable',
          'Try again in a few minutes',
          'Contact your SonarQube administrator if it is an access issue'
        ],
        showRetry: true
      };
    }
  }

  // Generic error fallback
  return {
    type: 'unknown',
    title: 'Unexpected Error',
    message: error?.message || 'An unexpected error occurred while fetching metrics.',
    icon: '❌',
    color: 'red',
    suggestions: [
      'Try refreshing the page',
      'Check your network connection',
      'Contact support if the issue persists'
    ],
    showRetry: true
  };
}; 