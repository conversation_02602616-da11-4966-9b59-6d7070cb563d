import React from 'react';
import { Button } from '../../ui/Button';
import type { CosmosTeam } from '../../../services/api';

export type ErrorType = 'no_team' | 'missing_metadata' | 'network_error' | 'vpn_required' | 'server_error' | 'not_found' | 'unauthorized' | 'unknown';

export interface ErrorInfo {
  type: ErrorType;
  title: string;
  message: string;
  icon: string;
  color: string;
  suggestions: string[];
  showRetry: boolean;
}

interface ErrorDisplayProps {
  errorInfo: ErrorInfo;
  team?: CosmosTeam;
  onRetry: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ errorInfo, team, onRetry }) => {
  const colorClasses: Record<string, string> = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
    red: 'bg-red-50 border-red-200 text-red-800'
  };

  const iconColorClasses: Record<string, string> = {
    blue: 'text-blue-500',
    yellow: 'text-yellow-500',
    orange: 'text-orange-500',
    red: 'text-red-500'
  };

  return (
    <div className="p-6">
      <div className={`${colorClasses[errorInfo.color]} border-2 rounded-xl p-6 shadow-sm`}>
        <div className="flex items-start space-x-4">
          <div className={`text-3xl ${iconColorClasses[errorInfo.color]} flex-shrink-0`}>
            {errorInfo.icon}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-xl font-bold mb-2">{errorInfo.title}</h3>
            <p className="text-base mb-4 leading-relaxed">{errorInfo.message}</p>
            
            {/* Team information if available */}
            {team && (
              <div className="mb-4 p-3 bg-white bg-opacity-50 rounded-lg">
                <h4 className="font-semibold text-sm mb-2">Team Information:</h4>
                <div className="text-sm space-y-1">
                  <p><span className="font-medium">Name:</span> {team.teamName}</p>
                  <p><span className="font-medium">Platform:</span> {team.metadata?.platform || 'Not configured'}</p>
                  <p><span className="font-medium">SonarQube URL:</span> {team.metadata?.sonarqube_url || 'Not configured'}</p>
                  <p><span className="font-medium">Project ID:</span> {team.metadata?.sonarqube_project_id || 'Not configured'}</p>
                </div>
              </div>
            )}

            {/* Suggestions */}
            <div className="mb-6">
              <h4 className="font-semibold text-sm mb-3">💡 Suggested Solutions:</h4>
              <ul className="space-y-2">
                {errorInfo.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-xs mt-1">•</span>
                    <span className="text-sm leading-relaxed">{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Action buttons */}
            <div className="flex flex-wrap gap-3">
              {errorInfo.showRetry && (
                <Button 
                  onClick={onRetry}
                  className="bg-white hover:bg-gray-50 text-gray-800 border border-gray-300 shadow-sm"
                >
                  🔄 Retry
                </Button>
              )}
              {errorInfo.type === 'vpn_required' && (
                <Button 
                  onClick={() => window.open('https://your-vpn-portal.com', '_blank')}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  🔒 VPN Portal
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay; 