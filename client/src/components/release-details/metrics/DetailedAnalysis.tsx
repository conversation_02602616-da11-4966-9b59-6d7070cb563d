import React from 'react';
import { 
  BugAntIcon,
  ShieldCheckIcon,
  BeakerIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ShieldExclamationIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { CodeQualityTab, CodeCoverageTab, TestResultsTab, ChangelogTab } from './';
import ChangeRequestsTab from './ChangeRequestsTab';
import PostCodeFreezeTab from './PostCodeFreezeTab';
import BuildStabilityTab from './BuildStabilityTab';
import type { CosmosTeam } from '../../../services/api';
import type { Release } from '../../../types/api';

interface DetailedAnalysisProps {
  activeMetricsTab: string;
  setActiveMetricsTab: React.Dispatch<React.SetStateAction<string>>;
  team?: CosmosTeam;
  releaseId?: string;
  release?: Release;
}

const DetailedAnalysis: React.FC<DetailedAnalysisProps> = ({
  activeMetricsTab,
  setActiveMetricsTab,
  team,
  releaseId,
  release
}) => {
  const metricsTabs = [
    { 
      id: 'code-quality', 
      label: 'Code Quality',
      icon: BugAntIcon,
      formula: 'Based on SonarQube metrics: (Reliability + Security + Maintainability) / 3 - Penalty Points'
    },
    { 
      id: 'code-coverage', 
      label: 'Code Coverage',
      icon: ShieldCheckIcon,
      formula: 'Coverage analysis: (Line Coverage × 0.5) + (Branch Coverage × 0.3) + (Condition Coverage × 0.2)'
    },
    { 
      id: 'test-results', 
      label: 'Test Results',
      icon: BeakerIcon,
      formula: 'Test quality: (Success Rate × 0.6) + (Stability × 0.25) + (Performance × 0.15)'
    },
    { 
      id: 'changelog', 
      label: 'Change Log',
      icon: ClipboardDocumentListIcon,
      formula: 'PR quality: 50 + (Merged Ratio × 40) + (Open Ratio × 10)'
    },
    { 
      id: 'change-requests', 
      label: 'Change Requests',
      icon: DocumentTextIcon,
      formula: 'Request management: 100 - (Total × 2) - (Open × 5) - (High Priority × 8)'
    },
    { 
      id: 'build-stability', 
      label: 'Build Stability',
      icon: WrenchScrewdriverIcon,
      formula: 'Build health: Direct mapping of (Successful Builds / Total Builds) × 100'
    },
    { 
      id: 'post-code-freeze', 
      label: 'PCF Requests',
      icon: ShieldExclamationIcon,
      formula: 'Post Code Freeze (PCF) Requests are changes requested after Code Freeze that require Director level approval to merge into the current release. These are exceptional changes that need special review and approval process.'
    }
  ];

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
      {/* Clean Header */}
      <div className="bg-gray-50 border-b border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <span className="text-xl">📊</span>
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="text-xl font-semibold text-gray-900">Detailed Analysis</h3>
              <div className="relative group">
                {/* <InformationCircleIcon className="h-5 w-5 text-gray-400 hover:text-blue-500 cursor-help" /> */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-96 px-6 py-4 bg-white text-gray-900 text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 shadow-xl border border-gray-200">
                  {activeMetricsTab === 'post-code-freeze' ? (
                    <>
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-xl font-semibold text-blue-600">PCF Requests Overview</h3>
                        <XMarkIcon className="h-4 w-4 text-gray-400" />
                      </div>
                      <p className="text-gray-600 mb-4">Post Code Freeze requests are special changes that require elevated approval.</p>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-gray-800 mb-2">Key Points:</h4>
                          <ul className="text-sm text-gray-600 space-y-2">
                            <li>• Changes requested after Code Freeze period</li>
                            <li>• Requires Director level approval</li>
                            <li>• For merging into current release</li>
                          </ul>
                        </div>
                      </div>
                    </>
                  ) : (
                    'Comprehensive breakdown of quality metrics and scoring formulas for each category.'
                  )}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-8 border-transparent border-t-white"></div>
                </div>
              </div>
            </div>
            <p className="text-sm text-gray-500">Comprehensive breakdown of quality metrics with scoring formulas</p>
          </div>
        </div>
      </div>

      {/* Enhanced Tab Navigation with Formulas */}
      <div className="border-b border-gray-200 px-2 overflow-x-auto">
        <nav className="flex space-x-1 min-w-max">
          {metricsTabs.map((tab) => {
            const isActive = activeMetricsTab === tab.id;
            const IconComponent = tab.icon;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveMetricsTab(tab.id)}
                className={`
                  flex items-center space-x-1 px-4 py-3.5 rounded-lg font-medium whitespace-nowrap transition-all duration-200 flex-1 justify-center min-w-0
                  ${isActive
                    ? 'bg-blue-100 text-blue-700 border-2 border-blue-200 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 border-2 border-transparent'
                  }
                `}
                title={tab.id === 'post-code-freeze' ? 'Post Code Freeze Requests' : tab.label}
              >
                <IconComponent className="h-5 w-5 flex-shrink-0" />
                <span className="text-sm font-medium">{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Clean Tab Content */}
      <div className="p-6">
        {activeMetricsTab === 'code-quality' && (
          <div>
            {team ? (
              <CodeQualityTab
                team={team}
                releaseId={releaseId}
                onRefresh={(data) => console.log("Code quality data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">🔍</span>
                  <p className="text-lg font-medium">No Team Selected</p>
                  <p className="text-sm mt-2">Please select a team to view code quality metrics.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeMetricsTab === 'code-coverage' && (
          <div>
            {team ? (
              <CodeCoverageTab
                team={team}
                releaseId={releaseId}
                onRefresh={(data) => console.log("Code coverage data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">🔍</span>
                  <p className="text-lg font-medium">No Team Selected</p>
                  <p className="text-sm mt-2">Please select a team to view code coverage metrics.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeMetricsTab === 'test-results' && (
          <div>
            {team ? (
              <TestResultsTab
                team={team}
                releaseId={releaseId}
                onRefresh={(data: any) => console.log("Test results data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">🔍</span>
                  <p className="text-lg font-medium">No Team Selected</p>
                  <p className="text-sm mt-2">Please select a team to view test results metrics.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeMetricsTab === 'changelog' && (
          <div>
            {team && release ? (
              <ChangelogTab
                team={team}
                release={release}
                releaseId={releaseId}
                onRefresh={(data) => console.log("Changelog data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">📋</span>
                  <p className="text-lg font-medium">No Team or Release Selected</p>
                  <p className="text-sm mt-2">Please ensure a team and release are selected to view the changelog.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeMetricsTab === 'change-requests' && (
          <div>
            {team && release ? (
              <ChangeRequestsTab
                team={team}
                releaseId={releaseId}
                onRefresh={(data) => console.log("Change requests data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">📋</span>
                  <p className="text-lg font-medium">No Team or Release Selected</p>
                  <p className="text-sm mt-2">Please ensure a team and release are selected to view change requests.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeMetricsTab === 'build-stability' && (
          <div>
            {team && release ? (
              <BuildStabilityTab
                team={team}
                release={release}
                releaseId={releaseId}
                onRefresh={(data) => console.log("Build stability data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">🔧</span>
                  <p className="text-lg font-medium">No Team or Release Selected</p>
                  <p className="text-sm mt-2">Please ensure a team and release are selected to view build stability metrics.</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeMetricsTab === 'post-code-freeze' && (
          <div>
            {team && release ? (
              <PostCodeFreezeTab
                team={team}
                release={release}
                releaseId={releaseId}
                onRefresh={(data) => console.log("Post-code freeze data refreshed:", data)}
              />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <span className="text-4xl mb-4 block">📋</span>
                  <p className="text-lg font-medium">No Team or Release Selected</p>
                  <p className="text-sm mt-2">Please ensure a team and release are selected to view post-code freeze analysis.</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DetailedAnalysis; 