import React from 'react';
import { Button } from '../../ui/Button';
import { 
  DocumentTextIcon,
  ArrowPathIcon,
  ArrowTopRightOnSquareIcon,
  CodeBracketIcon,
  ClockIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import type { CosmosTeam } from '../../../services/api';
import type { Release } from '../../../types/api';
import { useMetricsCache, useMetricTimestamp, DEFAULT_CACHE_DURATION } from '../../../hooks/useMetricsCache';
import { fetchStoredChangelogMetrics } from '../../../services/metricsService';
import ErrorDisplay, { type ErrorInfo } from './ErrorDisplay';
import { classifyError } from './errorUtils';
import axios from 'axios';
import { formatTimestampWithTimezone } from '../../../utils/dateUtils';
import { getGitHubTimeout } from '../../../utils/apiHealthCheck';

interface GitHubPullRequest {
  number: number;
  title: string;
  author: string;
  avatar_url?: string;
  status: 'open' | 'closed' | 'merged';
  url: string;
  created_at: string;
  closed_at?: string;
  merged_at?: string;
  base_branch: string;
  head_branch: string;
}

interface ChangelogTabProps {
  team: CosmosTeam;
  release: Release;
  releaseId?: string;
  onRefresh: (data: any) => void;
}

const ChangelogTab: React.FC<ChangelogTabProps> = ({
  team,
  release,
  releaseId,
  onRefresh,
}) => {
  // Pagination state
  const [currentPage, setCurrentPage] = React.useState(1);
  const itemsPerPage = 20; // Show 20 PRs per page
  const [isFromStoredFallback, setIsFromStoredFallback] = React.useState(false);
  const [isManualRefresh, setIsManualRefresh] = React.useState(false);
  const [showFormulaTooltip, setShowFormulaTooltip] = React.useState(false);

  // Configure axios for API calls
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'changeLog');

  // Function to store changelog metrics in database
  const storeChangelogMetricsInDatabase = async (
    teamId: string,
    releaseId: string,
    pullRequests: GitHubPullRequest[],
    releaseData: Release,
    forceTimestamp: boolean = false
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/metrics/${releaseId}/changelog`, {
        teamId,
        pullRequests,
        releaseData: {
          releaseBranch: releaseData.releaseBranch,
          createdAt: releaseData.createdAt,
          releaseRolloutDate: releaseData.releaseRolloutDate
        },
        forceTimestamp
      });

      return response.data;
    } catch (error: any) {
      console.error('Failed to store Changelog metrics in database:', error.message);
      // Don't throw - this is a background operation
      return null;
    }
  };

  // Helper to extract repository path from GitHub URL
  const getRepoPath = (githubRepo: string): string => {
    // Handle various GitHub URL formats
    if (githubRepo.includes('://')) {
      // Full URL format: https://github.com/owner/repo or https://enterprise.github.com/owner/repo
      const urlParts = githubRepo.split('/');
      const pathParts = urlParts.slice(3); // e.g., ["owner", "repo"] or ["owner", "repo.git"]
      
      if (pathParts.length >= 2) {
        const owner = pathParts[0];
        const repo = pathParts[1].replace('.git', '');
        return `${owner}/${repo}`;
      }
    } else if (githubRepo.includes('/')) {
      // Already in owner/repo format
      return githubRepo.replace('.git', '');
    }
    
    // Fallback - return as is and let GitHub API handle any errors
    return githubRepo.replace('.git', '');
  };

  // Helper to display repository name (last 2 parts) as a clickable link
  const getRepoDisplayInfo = (githubRepo: string) => {
    const repoPath = getRepoPath(githubRepo);
    
    // Extract the last 2 parts for display
    const parts = repoPath.split('/');
    const displayName = parts.length >= 2 ? `${parts[parts.length - 2]}/${parts[parts.length - 1]}` : repoPath;
    
    // Construct the full GitHub URL for linking
    let fullUrl = githubRepo;
    if (!githubRepo.includes('://')) {
      // If it's just owner/repo format, we need to construct the full URL
      // We'll use the base URL from the repo path detection
      fullUrl = `https://github.com/${repoPath}`;
    }
    
    return { displayName, fullUrl };
  };

  // Fetch pull requests function
  const fetchPullRequests = async (): Promise<GitHubPullRequest[]> => {
    if (!team?.metadata?.github_repo || !release) {
      throw new Error('Team GitHub repository or release information not available');
    }

    try {
      setIsFromStoredFallback(false);

      const repoPath = getRepoPath(team.metadata.github_repo);
      const branch = release.releaseBranch || team.metadata.repo_default_branch || 'main';

      const params = {
        base: branch,
        state: 'all',
        per_page: '100', // Fetch up to 100 PRs per page
      };

      // Get adaptive timeout based on API health
      const timeoutConfig = await getGitHubTimeout();

      // Always try live API first with adaptive timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutConfig.timeout);

      const response = await fetch(
        `${API_BASE_URL}/api/github/repos/${repoPath}/pulls?${new URLSearchParams(params)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch pull requests: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Mark fetch as complete after setting all flags
      setIsFetchComplete(true);

      return data;

    } catch (liveError: any) {
      // Only use stored data as fallback when live API fails
      if (releaseId) {
        try {
          const storedData = await fetchStoredChangelogMetrics(releaseId);
          setIsFromStoredFallback(true);

          // Mark fetch as complete after setting all flags
          setIsFetchComplete(true);

          return storedData;
        } catch (storedError) {
          // Mark fetch as complete even on error
          setIsFetchComplete(true);

          // Re-throw the original live API error since it's more informative
          throw liveError;
        }
      } else {
        // Mark fetch as complete even on error
        setIsFetchComplete(true);

        // No releaseId to look up stored data, re-throw live error
        throw liveError;
      }
    }
  };

  // Use the centralized caching system
  const { data: pullRequests, isLoading, error, lastUpdated, isFromCache, refreshData } =
    useMetricsCache<GitHubPullRequest[]>(
      {
        key: `changelog-${releaseId}-${team?.teamName || 'unknown'}`,
        maxAge: DEFAULT_CACHE_DURATION,
        useLocalStorage: false, // Use session storage
      },
      fetchPullRequests,
      [team?.id, team?.teamName, releaseId, release?.releaseBranch]
    );

  // Track when the fetch is completely done (including setting isFromStoredFallback)
  const [isFetchComplete, setIsFetchComplete] = React.useState(false);

  // Reset fetch complete flag when starting a new fetch
  React.useEffect(() => {
    if (isLoading) {
      setIsFetchComplete(false);
      // Also reset the stored fallback flag when starting a new fetch
      setIsFromStoredFallback(false);
    }
  }, [isLoading]);

  // Mark fetch as complete when using cached data
  React.useEffect(() => {
    if (isFromCache && pullRequests) {
      setIsFetchComplete(true);
    }
  }, [isFromCache, pullRequests]);

  // Automatically store metrics in database when data is fetched (only after fetch is complete)
  React.useEffect(() => {
    // Only run storage logic when fetch is completely done
    if (!isFetchComplete) {
      return;
    }

    // Small delay to ensure state updates are complete, but much faster than before
    const timeoutId = setTimeout(async () => {
      // Only use the stored fallback flag - don't do age checks on live data
      // Live data can contain old PRs, that's normal and shouldn't prevent storage
      const isLikelyStoredFallback = isFromStoredFallback;

      console.log(`🔍 Changelog storage conditions:`, {
        hasPullRequests: !!(pullRequests && Array.isArray(pullRequests)),
        prCount: pullRequests?.length || 0,
        hasTeam: !!team,
        hasReleaseId: !!releaseId,
        hasRelease: !!release,
        isFromCache,
        isFromStoredFallback,
        isLikelyStoredFallback,
        isManualRefresh,
        shouldStore: pullRequests && Array.isArray(pullRequests) && team && releaseId && release && !isFromCache && !isLikelyStoredFallback
      });

      if (pullRequests && Array.isArray(pullRequests) && team && releaseId && release && !isFromCache && !isLikelyStoredFallback) {
        // Only store if we have fresh data (not from cache or stored fallback)
        try {
          console.log(`📦 Storing Changelog data with forceTimestamp=${isManualRefresh}`);
          await storeChangelogMetricsInDatabase(team.id, releaseId, pullRequests, release, isManualRefresh);

          // Refresh database timestamp after successful storage (await ensures DB write completes first)
          console.log(`🔄 Refreshing database timestamp after storage`);
          refetchDbTimestamp();

        } catch (error) {
          console.warn('Failed to store changelog metrics in background:', error);
          // Don't throw - this shouldn't break the UI
        } finally {
          // Reset manual refresh flag after storage
          setIsManualRefresh(false);
        }
      } else {
        console.log(`⏭️ Skipping Changelog storage - conditions not met`);
        // Reset manual refresh flag even when skipping storage
        if (isManualRefresh) {
          setIsManualRefresh(false);
        }
      }
    }, 50); // Much smaller delay - just enough to avoid state race conditions

    // Cleanup timeout on unmount or dependency change
    return () => clearTimeout(timeoutId);
  }, [pullRequests, team, releaseId, release, isFromCache, isFromStoredFallback, refetchDbTimestamp, isManualRefresh, isFetchComplete]);

  // Reset pagination when data changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [pullRequests]);

  // Handle refresh with callback - force fresh data
  const handleRefresh = async () => {
    setIsFromStoredFallback(false);
    setIsManualRefresh(true); // Set flag to force timestamp update in database

    // Clear cache to ensure we hit the live API (which will use server-side fallback if needed)
    const cacheKey = `changelog-${releaseId}-${team?.teamName || 'unknown'}`;

    // Clear from session storage
    try {
      sessionStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn('Failed to clear session storage:', error);
    }

    await refreshData(); // Force refresh bypassing cache
    if (onRefresh && pullRequests && Array.isArray(pullRequests)) {
      onRefresh({ data: pullRequests });
    }
  };

  // Get status configuration for PR status chips
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'merged':
        return {
          color: 'bg-green-100 text-green-800 border border-green-200',
          label: 'Merged',
        };
      case 'closed':
        return {
          color: 'bg-red-100 text-red-800 border border-red-200',
          label: 'Closed',
        };
      case 'open':
        return {
          color: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
          label: 'Open',
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border border-gray-200',
          label: status,
        };
    }
  };

  // Convert error to ErrorInfo for compatibility with existing error display
  const errorInfo: ErrorInfo | null = error ? classifyError(error, team) : null;

  // Determine which timestamp to show: database timestamp takes priority
  const displayTimestamp = dbTimestamp || lastUpdated;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-purple-50 via-purple-50 to-violet-50 rounded-2xl border border-purple-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-200 border-t-purple-600 shadow-lg"></div>
          <div className="absolute inset-0 rounded-full h-20 w-20 border-4 border-transparent border-r-purple-400 animate-pulse"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Loading Changelog</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching pull requests from GitHub...</p>
          {team && release && (
            <div className="bg-white bg-opacity-80 rounded-xl p-4 shadow-sm border border-white">
              <p className="text-sm text-gray-700 mb-1">
                <span className="font-semibold">Repository:</span> {team.metadata?.github_repo}
              </p>
              <p className="text-sm text-gray-700">
                <span className="font-semibold">Branch:</span> {release.releaseBranch || team.metadata?.repo_default_branch || 'main'}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (errorInfo) {
    return <ErrorDisplay errorInfo={errorInfo} team={team} onRetry={handleRefresh} />;
  }

  // No data state
  if (!pullRequests || !Array.isArray(pullRequests) || pullRequests.length === 0) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <CodeBracketIcon className="w-16 h-16 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Pull Requests Found</h3>
          <p className="text-gray-600 mb-4">
            No pull requests found for the specified date range and branch.
          </p>
          {team && release && (
            <div className="text-sm text-gray-500 mb-4">
              <p><strong>Repository:</strong> {team.metadata?.github_repo}</p>
              <p><strong>Branch:</strong> {release.releaseBranch || team.metadata?.repo_default_branch || 'main'}</p>
            </div>
          )}
          <Button onClick={handleRefresh} className="bg-purple-600 hover:bg-purple-700 text-white">
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Success state - show PRs
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-white to-purple-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-purple-700 rounded-xl flex items-center justify-center shadow-md">
              <CodeBracketIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <h2 className="text-xl font-bold text-gray-900">Release Changelog</h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFormulaTooltip(!showFormulaTooltip)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    title="View scoring formula"
                  >
                    <InformationCircleIcon className="w-5 h-5 text-gray-400 hover:text-purple-600" />
                  </button>
                  
                  {/* Formula Tooltip */}
                  {showFormulaTooltip && (
                    <div className="absolute top-full left-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-80">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-purple-600">
                          Changelog Score Formula
                        </h4>
                        <button
                          onClick={() => setShowFormulaTooltip(false)}
                          className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                          title="Close"
                        >
                          <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="text-sm">
                        <p className="text-gray-600 mb-3 text-xs">
                          Pull request quality and delivery metrics analysis
                        </p>
                        <div className="mb-3">
                          <p className="font-medium text-gray-800 text-xs mb-1">Score Components:</p>
                          <ul className="text-xs text-gray-600 space-y-1">
                            <li>• Base Score: 50 points</li>
                            <li>• Merged Ratio: (merged/total) × 40 points</li>
                            <li>• Open Ratio: (open/total) × 10 points</li>
                          </ul>
                        </div>
                        <div className="border-t border-gray-100 pt-2">
                          <p className="font-medium text-gray-800 text-xs mb-1">Calculation:</p>
                          <p className="text-xs text-gray-600 font-mono bg-gray-50 p-2 rounded">
                            Score = 50 + (merged_ratio × 40) + (open_ratio × 10)
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md flex items-center space-x-1">
                  <span className="font-medium">Repository:</span>
                  {team?.metadata?.github_repo ? (
                    <a
                      href={getRepoDisplayInfo(team.metadata.github_repo).fullUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-purple-600 hover:text-purple-800 font-medium flex items-center space-x-1"
                    >
                      <span>{getRepoDisplayInfo(team.metadata.github_repo).displayName}</span>
                      <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                    </a>
                  ) : (
                    <span>N/A</span>
                  )}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Branch:</span> {release?.releaseBranch || team?.metadata?.repo_default_branch || 'main'}
                </span>
                {isFromCache && (
                  <span className="text-purple-600 bg-purple-50 px-2 py-1 rounded-md border border-purple-200">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {displayTimestamp && (
                  <span className="text-purple-600 bg-purple-50 px-2 py-1 rounded-md border border-purple-200 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(displayTimestamp)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Pull Requests Table with Pagination */}
      {(() => {
        // Ensure pullRequests is an array before processing
        const pullRequestsArray = Array.isArray(pullRequests) ? pullRequests : [];
        
        // Calculate pagination
        const totalPages = Math.ceil(pullRequestsArray.length / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const currentPRs = pullRequestsArray.slice(startIndex, endIndex);
        
        return (
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      PR #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Author
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Title
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentPRs.map((pr) => {
                    const statusConfig = getStatusConfig(pr.status);
                    return (
                      <tr key={pr.number} className="hover:bg-gray-50 transition-colors duration-150">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <a
                            href={pr.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-purple-600 hover:text-purple-800 font-medium flex items-center space-x-1"
                          >
                            <span>#{pr.number}</span>
                            <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                          </a>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 h-8 w-8">
                              <div className="h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center text-white text-sm font-medium">
                                {pr.author.charAt(0).toUpperCase()}
                              </div>
                            </div>
                            <div className="text-sm font-medium text-gray-900">{pr.author}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-md truncate" title={pr.title}>
                            {pr.title}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium ${statusConfig.color}`}
                          >
                            {statusConfig.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(pr.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination Controls */}
            <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(endIndex, pullRequestsArray.length)}</span> of{' '}
                  <span className="font-medium">{pullRequestsArray.length}</span> pull requests
                </div>
                
                {totalPages > 1 && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    
                    <span className="text-sm text-gray-700">
                      Page <span className="font-medium">{currentPage}</span> of{' '}
                      <span className="font-medium">{totalPages}</span>
                    </span>
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })()}
    </div>
  );
};

export default ChangelogTab; 