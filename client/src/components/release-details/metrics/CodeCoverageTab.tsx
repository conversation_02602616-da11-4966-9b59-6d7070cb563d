import React from "react";
import { But<PERSON> } from "../../ui/Button";
import { 
  ShieldCheckIcon,
  DocumentTextIcon,
  ChartBarIcon,
  XMarkIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import type { CosmosTeam } from '../../../services/api';
import ErrorDisplay, { type ErrorInfo } from './ErrorDisplay';
import { classifyError } from './errorUtils';
import { useMetricsCache, useMetricTimestamp, DEFAULT_CACHE_DURATION } from '../../../hooks/useMetricsCache';
import { fetchCodeCoverageMetricsWithMetadata, type ProcessedMetrics } from '../../../services/metricsService';
import { formatTimestampWithTimezone } from '../../../utils/dateUtils';
import axios from 'axios';

interface MetricBoxProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  value: string | number;
  subLabel?: string;
  infoText: string;
}

const MetricBox: React.FC<MetricBoxProps> = ({ 
  icon: Icon, 
  label, 
  value, 
  subLabel,
  infoText 
}) => (
  <div className="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg hover:border-green-200 transition-all duration-300 group">
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-3">
        <span className="text-xl">
          <Icon className="h-6 w-6 text-gray-800" />
        </span>
        <span className="text-sm font-semibold text-gray-800">{label}</span>
        <div className="group/tooltip relative">
          <span className="text-gray-400 hover:text-green-500 cursor-help transition-colors duration-200">
            <InformationCircleIcon className="h-4 w-4" />
          </span>
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200 pointer-events-none z-50 shadow-lg w-64 text-center">
            <div className="leading-relaxed">{infoText}</div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      </div>
    </div>
    <div className="flex items-end justify-between">
      <div>
        <div className="text-3xl font-bold text-green-600 group-hover:text-green-700 transition-colors duration-200">{value}</div>
        {subLabel && (
          <div className="text-xs text-gray-500 mt-1 font-medium">{subLabel}</div>
        )}
      </div>
    </div>
  </div>
);

interface CodeCoverageTabProps {
  team?: CosmosTeam;
  releaseId?: string;
  initialData?: any;
  onRefresh?: (data: any) => void;
}

const CodeCoverageTab: React.FC<CodeCoverageTabProps> = ({
  team,
  releaseId,
  initialData: _initialData,
  onRefresh
}) => {
  const [showFormulaTooltip, setShowFormulaTooltip] = React.useState(false);
  const [isFromStoredFallback, setIsFromStoredFallback] = React.useState(false);
  const [isManualRefresh, setIsManualRefresh] = React.useState(false);

  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'codeCoverage');

  // Configure axios for SonarQube API calls
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

  // Function to store metrics in Azure database
  const storeMetricsInDatabase = async (teamId: string, releaseId: string, metrics: any, forceTimestamp: boolean = false) => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/metrics/${releaseId}/test-coverage`,
        {
          teamId,
          metrics,
          forceTimestamp
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Failed to store Code Coverage metrics in database:', error.message);
      // Don't throw - this is a background operation
      return null;
    }
  };

  // Custom fetch function that tracks if data comes from stored metrics (using smart fallback)
  const fetchCodeCoverageWithTracking = async (): Promise<ProcessedMetrics> => {
    if (!team) {
      throw new Error('No team selected');
    }

    try {
      // Reset stored fallback flag when attempting fresh data
      setIsFromStoredFallback(false);

      // Try live data first - this now includes smart fallback logic
      const result = await fetchCodeCoverageMetricsWithMetadata(team, releaseId);

      // Set the stored fallback flag based on the metadata
      if (result.isFromStored) {
        setIsFromStoredFallback(true);
      }

      // Mark fetch as complete after setting all flags
      setIsFetchComplete(true);

      return result.data;

    } catch (error: any) {
      // Mark fetch as complete even on error
      setIsFetchComplete(true);

      // If the smart fallback in the service also failed, this is a genuine error
      throw error;
    }
  };

  // Use the centralized caching system
  const { data, isLoading, error, lastUpdated, isFromCache, refreshData } = useMetricsCache<ProcessedMetrics>(
    {
      key: `code-coverage-${releaseId}-${team?.teamName || 'unknown'}`,
      maxAge: DEFAULT_CACHE_DURATION,
      useLocalStorage: false, // Use session storage
    },
    fetchCodeCoverageWithTracking,
    [team?.id, team?.teamName, releaseId] // Dependencies that trigger refetch
  );

  // Track when the fetch is completely done (including setting isFromStoredFallback)
  const [isFetchComplete, setIsFetchComplete] = React.useState(false);

  // Reset fetch complete flag when starting a new fetch
  React.useEffect(() => {
    if (isLoading) {
      setIsFetchComplete(false);
      // Also reset the stored fallback flag when starting a new fetch
      setIsFromStoredFallback(false);
    }
  }, [isLoading]);

  // Mark fetch as complete when using cached data
  React.useEffect(() => {
    if (isFromCache && data) {
      setIsFetchComplete(true);
    }
  }, [isFromCache, data]);

  // Automatically store metrics in database when data is fetched (only after fetch is complete)
  React.useEffect(() => {
    // Only run storage logic when fetch is completely done
    if (!isFetchComplete) {
      return;
    }

    // Small delay to ensure state updates are complete, but much faster than before
    const timeoutId = setTimeout(async () => {
      // Only use the stored fallback flag - don't do age checks on live data
      const isLikelyStoredFallback = isFromStoredFallback;

      if (data && team && releaseId && !isFromCache && !isLikelyStoredFallback) {
        // Only store if we have fresh data (not from cache or stored fallback)
        try {
          await storeMetricsInDatabase(team.id, releaseId, data, isManualRefresh);

          // Refresh database timestamp after successful storage (await ensures DB write completes first)
          refetchDbTimestamp();

        } catch (error) {
          console.warn('Failed to store coverage metrics in background:', error);
          // Don't throw - this shouldn't break the UI
        } finally {
          // Reset manual refresh flag after storage
          setIsManualRefresh(false);
        }
      } else {
        // Reset manual refresh flag even when skipping storage
        if (isManualRefresh) {
          setIsManualRefresh(false);
        }
      }
    }, 50); // Much smaller delay - just enough to avoid state race conditions

    // Cleanup timeout on unmount or dependency change
    return () => clearTimeout(timeoutId);
  }, [data, team, releaseId, isFromCache, isFromStoredFallback, refetchDbTimestamp, isManualRefresh, isFetchComplete]);

  // Format large numbers
  const formatNumber = (num: string | number): string => {
    const n = typeof num === 'string' ? parseInt(num) : num;
    if (n >= 1000000) return `${(n / 1000000).toFixed(1)}M`;
    if (n >= 1000) return `${(n / 1000).toFixed(1)}k`;
    return n.toLocaleString();
  };

  // Define metric information
  const metricInfo = {
    coverage: "Percentage of code covered by unit tests. Higher coverage generally indicates better tested code and reduced risk of bugs.",
    linesToCover: "Total number of lines that can be covered by unit tests. This excludes comments, blank lines, and non-executable code.",
    uncoveredLines: "Number of lines not covered by any unit tests. These lines represent potential risk areas that lack test validation.",
    lineCoverage: "Alternative calculation of line coverage percentage. May differ slightly from overall coverage due to different calculation methods."
  };

  // Handle refresh with callback - force fresh data
  const handleRefresh = async () => {
    setIsFromStoredFallback(false);
    setIsManualRefresh(true); // Set flag to force timestamp update in database

    // Clear cache to ensure we hit the live API (which will use server-side fallback if needed)
    const cacheKey = `code-coverage-${releaseId}-${team?.teamName || 'unknown'}`;

    // Clear from session storage
    try {
      sessionStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn('Failed to clear session storage:', error);
    }

    await refreshData();
    if (onRefresh && data) {
      onRefresh({ data });
    }
  };

  // Convert error to ErrorInfo for compatibility with existing error display
  const errorInfo: ErrorInfo | null = error ? classifyError(error, team) : null;

  // Determine which timestamp to show: database timestamp takes priority
  const displayTimestamp = dbTimestamp || lastUpdated;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-2xl border border-green-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-green-200 border-t-green-600 shadow-lg"></div>
          <div className="absolute inset-0 rounded-full h-20 w-20 border-4 border-transparent border-r-green-400 animate-pulse"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Analyzing Code Coverage</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching coverage metrics from SonarQube...</p>
          {team && (
            <div className="bg-white bg-opacity-80 rounded-xl p-4 shadow-sm border border-white">
              <p className="text-sm text-gray-700 mb-1">
                <span className="font-semibold">Team:</span> {team.teamName}
              </p>
              <p className="text-sm text-gray-700">
                <span className="font-semibold">Project:</span> {team.metadata?.sonarqube_project_id}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (errorInfo) {
    return <ErrorDisplay errorInfo={errorInfo} team={team} onRetry={handleRefresh} />;
  }

  // No data state
  if (!data) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
          <div className="text-4xl mb-4">📊</div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Coverage Data Available</h3>
          <p className="text-gray-600 mb-4">No code coverage data found for this team.</p>
          <Button 
            onClick={handleRefresh}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            🔄 Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Success state - show metrics
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-white to-emerald-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 via-emerald-600 to-emerald-700 rounded-xl flex items-center justify-center shadow-md">
              <ShieldCheckIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-bold text-gray-900 mb-1">Code Coverage Analysis</h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFormulaTooltip(!showFormulaTooltip)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    title="View scoring formula"
                  >
                    <InformationCircleIcon className="w-5 h-5 text-gray-500 hover:text-gray-700" />
                  </button>
                  {showFormulaTooltip && (
                    <div className="absolute top-8 left-0 z-10 w-80 p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-green-600">Coverage Score Formula</h3>
                        <button
                          onClick={() => setShowFormulaTooltip(false)}
                          className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                          title="Close"
                        >
                          <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p><strong>Weighted Average:</strong></p>
                        <p>• New Code Coverage: 70% weight</p>
                        <p>• Overall Coverage: 30% weight</p>
                        <p className="mt-2"><strong>Formula:</strong> (New × 0.7) + (Overall × 0.3)</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Team:</span> {team?.teamName || 'Unknown'}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Platform:</span> {team?.metadata?.platform || 'N/A'}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Project:</span> {team?.metadata?.sonarqube_project_id || 'N/A'}
                </span>
                {isFromCache && (
                  <span className="text-emerald-700 bg-emerald-100 px-2 py-1 rounded-md border border-emerald-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {displayTimestamp && (
                  <span className="text-emerald-700 bg-emerald-100 px-2 py-1 rounded-md border border-emerald-300 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(displayTimestamp)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* New Code Coverage */}
        <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4">
            <h3 className="text-lg font-bold text-white">New Code Coverage</h3>
            <p className="text-green-100 text-sm">Recent changes coverage</p>
          </div>
          <div className="p-6 space-y-4">
            {data.new_coverage && (
            <MetricBox
                icon={ShieldCheckIcon}
              label="Coverage"
                value={`${parseFloat(data.new_coverage).toFixed(1)}%`}
              infoText={metricInfo.coverage}
            />
            )}
            {data.new_lines_to_cover && (
            <MetricBox
                icon={DocumentTextIcon}
              label="Lines to Cover"
                value={formatNumber(data.new_lines_to_cover)}
              infoText={metricInfo.linesToCover}
            />
            )}
            {data.new_uncovered_lines && (
            <MetricBox
                icon={XMarkIcon}
              label="Uncovered Lines"
                value={formatNumber(data.new_uncovered_lines)}
              infoText={metricInfo.uncoveredLines}
            />
            )}
            {data.new_line_coverage && (
              <MetricBox
                icon={ChartBarIcon}
                label="Line Coverage"
                value={`${parseFloat(data.new_line_coverage).toFixed(1)}%`}
                infoText={metricInfo.lineCoverage}
              />
            )}
          </div>
        </div>

        {/* Overall Coverage */}
        <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4">
            <h3 className="text-lg font-bold text-white">Overall Coverage</h3>
            <p className="text-emerald-100 text-sm">Complete project coverage</p>
          </div>
          <div className="p-6 space-y-4">
            {data.coverage && (
            <MetricBox
                icon={ShieldCheckIcon}
              label="Coverage"
                value={`${parseFloat(data.coverage).toFixed(1)}%`}
              infoText={metricInfo.coverage}
            />
            )}
            {data.lines_to_cover && (
            <MetricBox
                icon={DocumentTextIcon}
              label="Lines to Cover"
                value={formatNumber(data.lines_to_cover)}
              infoText={metricInfo.linesToCover}
            />
            )}
            {data.uncovered_lines && (
            <MetricBox
                icon={XMarkIcon}
              label="Uncovered Lines"
                value={formatNumber(data.uncovered_lines)}
              infoText={metricInfo.uncoveredLines}
            />
            )}
            {data.line_coverage && (
              <MetricBox
                icon={ChartBarIcon}
                label="Line Coverage"
                value={`${parseFloat(data.line_coverage).toFixed(1)}%`}
                infoText={metricInfo.lineCoverage}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeCoverageTab; 