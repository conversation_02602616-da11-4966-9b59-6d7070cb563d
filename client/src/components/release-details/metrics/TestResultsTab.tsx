import React from 'react';
import { 
  BeakerIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ForwardIcon,
  ClockIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  SparklesIcon,
  BoltIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';
import type { CosmosTeam } from '../../../services/api';
import ErrorDisplay, { type ErrorInfo } from './ErrorDisplay';
import { classifyError } from './errorUtils';
import { useMetricsCache, useMetricTimestamp, DEFAULT_CACHE_DURATION } from '../../../hooks/useMetricsCache';
import { fetchTestResultsMetricsWithMetadata, type ProcessedMetrics } from '../../../services/metricsService';
import { formatDuration, formatTimestampWithTimezone } from '../../../utils/dateUtils';
import axios from 'axios';

interface MetricBoxProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  value: string | number;
  subLabel?: string;
  infoText: string;
}

const MetricBox: React.FC<MetricBoxProps> = ({ icon: Icon, label, value, subLabel, infoText }) => (
  <div className="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg hover:border-blue-200 transition-all duration-300 group">
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-3">
        <span className="text-xl">
          <Icon className="h-6 w-6 text-gray-800" />
        </span>
        <span className="text-sm font-semibold text-gray-800">{label}</span>
        <div className="group/tooltip relative">
          <span className="text-gray-400 hover:text-blue-500 cursor-help transition-colors duration-200">
            <InformationCircleIcon className="h-4 w-4" />
          </span>
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200 pointer-events-none z-50 shadow-lg w-64 text-center">
            <div className="leading-relaxed">{infoText}</div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      </div>
    </div>
    <div className="flex items-end justify-between">
      <div>
        <div className="text-3xl font-bold text-blue-600 group-hover:text-blue-700 transition-colors duration-200">
          {value}
        </div>
        {subLabel && <div className="text-xs text-gray-500 mt-1 font-medium">{subLabel}</div>}
      </div>
    </div>
  </div>
);

interface TestResultsTabProps {
  team: CosmosTeam;
  releaseId?: string;
  onRefresh: (data: any) => void;
}

interface TestSection {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const TestResultsTab: React.FC<TestResultsTabProps> = ({
  team,
  releaseId,
  onRefresh,
}) => {
  const [showFormulaTooltip, setShowFormulaTooltip] = React.useState(false);
  const [isFromStoredFallback, setIsFromStoredFallback] = React.useState(false);
  const [isManualRefresh, setIsManualRefresh] = React.useState(false);
  const [activeTestSection, setActiveTestSection] = React.useState('unit');

  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'testResults');

  // Define test sections
  const testSections: TestSection[] = [
    {
      id: 'unit',
      label: 'Unit Tests',
      icon: BeakerIcon,
      description: 'Individual component and function testing'
    },
    {
      id: 'e2e',
      label: 'E2E Tests',
      icon: RocketLaunchIcon,
      description: 'End-to-end user journey testing'
    },
    {
      id: 'performance',
      label: 'Performance Tests',
      icon: BoltIcon,
      description: 'Load, stress, and performance testing'
    }
  ];

  // Configure axios for SonarQube API calls
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

  // Function to store test results metrics in Azure database
  const storeTestResultsMetricsInDatabase = async (
    teamId: string,
    releaseId: string,
    metrics: any,
    forceTimestamp: boolean = false
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/metrics/${releaseId}/test-results`, {
        teamId,
        metrics,
        forceTimestamp,
      });

      return response.data;
    } catch (error: any) {
      console.error('Failed to store Test Results metrics in database:', error.message);
      // Don't throw - this is a background operation
      return null;
    }
  };

  // Custom fetch function that tracks if data comes from stored metrics (using smart fallback)
  const fetchTestResultsWithTracking = async (): Promise<ProcessedMetrics> => {
    if (!team) {
      throw new Error('No team selected');
    }

    try {
      // Reset stored fallback flag when attempting fresh data
      setIsFromStoredFallback(false);

      // Try live data first - this now includes smart fallback logic
      const result = await fetchTestResultsMetricsWithMetadata(team, releaseId);

      // Set the stored fallback flag based on the metadata
      if (result.isFromStored) {
        setIsFromStoredFallback(true);
      }

      // Mark fetch as complete after setting all flags
      setIsFetchComplete(true);

      return result.data;

    } catch (error: any) {
      // Mark fetch as complete even on error
      setIsFetchComplete(true);

      // If the smart fallback in the service also failed, this is a genuine error
      throw error;
    }
  };

  // Use the centralized caching system
  const { data, isLoading, error, lastUpdated, isFromCache, refreshData } =
    useMetricsCache<ProcessedMetrics>(
      {
        key: `test-results-${releaseId}-${team?.teamName || 'unknown'}`,
        maxAge: DEFAULT_CACHE_DURATION,
        useLocalStorage: false, // Use session storage
      },
      fetchTestResultsWithTracking,
      [team?.id, team?.teamName, releaseId] // Dependencies that trigger refetch
    );

  // Track when the fetch is completely done (including setting isFromStoredFallback)
  const [isFetchComplete, setIsFetchComplete] = React.useState(false);

  // Reset fetch complete flag when starting a new fetch
  React.useEffect(() => {
    if (isLoading) {
      setIsFetchComplete(false);
      // Also reset the stored fallback flag when starting a new fetch
      setIsFromStoredFallback(false);
    }
  }, [isLoading]);

  // Mark fetch as complete when using cached data
  React.useEffect(() => {
    if (isFromCache && data) {
      setIsFetchComplete(true);
    }
  }, [isFromCache, data]);

  // Automatically store metrics in database when data is fetched (only after fetch is complete)
  React.useEffect(() => {
    // Only run storage logic when fetch is completely done
    if (!isFetchComplete) {
      return;
    }

    // Small delay to ensure state updates are complete, but much faster than before
    const timeoutId = setTimeout(async () => {
      // Only use the stored fallback flag - don't do age checks on live data
      const isLikelyStoredFallback = isFromStoredFallback;

      if (data && team && releaseId && !isFromCache && !isLikelyStoredFallback) {
        // Only store if we have fresh data (not from cache or stored fallback)
        try {
          await storeTestResultsMetricsInDatabase(team.id, releaseId, data, isManualRefresh);

          // Refresh database timestamp after successful storage (await ensures DB write completes first)
          refetchDbTimestamp();

        } catch (error) {
          console.warn('Failed to store test results metrics in background:', error);
          // Don't throw - this shouldn't break the UI
        } finally {
          // Reset manual refresh flag after storage
          setIsManualRefresh(false);
        }
      } else {
        // Reset manual refresh flag even when skipping storage
        if (isManualRefresh) {
          setIsManualRefresh(false);
        }
      }
    }, 50); // Much smaller delay - just enough to avoid state race conditions

    // Cleanup timeout on unmount or dependency change
    return () => clearTimeout(timeoutId);
  }, [data, team, releaseId, isFromCache, isFromStoredFallback, refetchDbTimestamp, isManualRefresh, isFetchComplete]);

  // Format large numbers
  const formatNumber = (num: string | number): string => {
    const n = typeof num === 'string' ? parseInt(num) : num;
    if (n >= 1000000) return `${(n / 1000000).toFixed(1)}M`;
    if (n >= 1000) return `${(n / 1000).toFixed(1)}k`;
    return n.toLocaleString();
  };

  // Convert milliseconds to minutes and seconds format for the dateUtils formatter
  const formatTestDuration = (ms: string | number): string => {
    const milliseconds = typeof ms === 'string' ? parseInt(ms) : ms;
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return formatDuration(`${minutes}m ${seconds}s`);
  };

  // Calculate success rate
  const calculateSuccessRate = (): string => {
    if (!data || !data.tests) return '0.0%';

    const total = parseInt(data.tests);
    const failures = parseInt(data.test_failures || '0');
    const errors = parseInt(data.test_errors || '0');
    const successful = total - failures - errors;

    if (total === 0) return '0.0%';
    return `${((successful / total) * 100).toFixed(1)}%`;
  };

  // Define metric information
  const metricInfo = {
    unitTests:
      'Total number of unit tests in the project. Higher test counts generally indicate better test coverage.',
    testSuccessRate:
      'Percentage of tests that pass successfully. Higher success rates indicate more stable code.',
    testFailures:
      'Number of test failures. These are tests that ran but produced unexpected results.',
    testErrors:
      "Number of test errors. These are tests that couldn't run due to errors in the test setup or code.",
    skippedTests:
      'Number of tests that were skipped during execution. These might be disabled or conditional tests.',
    testDuration:
      'Total time taken to execute all tests. Shorter durations generally indicate more efficient tests.',
  };

  // Handle refresh with callback - force fresh data
  const handleRefresh = async () => {
    setIsFromStoredFallback(false);
    setIsManualRefresh(true); // Set flag to force timestamp update in database

    // Clear cache to ensure we hit the live API (which will use server-side fallback if needed)
    const cacheKey = `test-results-${releaseId}-${team?.teamName || 'unknown'}`;

    // Clear from session storage
    try {
      sessionStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn('Failed to clear session storage:', error);
    }

    await refreshData();
    if (onRefresh && data) {
      onRefresh({ data });
    }
  };

  // Convert error to ErrorInfo for compatibility with existing error display
  const errorInfo: ErrorInfo | null = error ? classifyError(error, team) : null;

  // Determine which timestamp to show: database timestamp takes priority
  const displayTimestamp = dbTimestamp || lastUpdated;

  // Mock data generators for E2E and Performance tests
  const generateE2ETestData = () => ({
    totalTests: 145,
    successRate: '92.4%',
    failures: 8,
    errors: 3,
    skipped: 2,
    executionTime: '12m 34s',
    avgResponseTime: '1.2s',
    browserCoverage: '95%'
  });

  const generatePerformanceTestData = () => ({
    totalTests: 28,
    successRate: '89.3%',
    failures: 2,
    errors: 1,
    avgLoadTime: '850ms',
    throughput: '1,250 req/s',
    p95ResponseTime: '2.1s',
    cpuUtilization: '68%'
  });

  // Mock data
  const e2eData = generateE2ETestData();
  const performanceData = generatePerformanceTestData();

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50 rounded-2xl border border-purple-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-200 border-t-purple-600 shadow-lg"></div>
          <div className="absolute inset-0 rounded-full h-20 w-20 border-4 border-transparent border-r-purple-400 animate-pulse"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Analyzing Test Results</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching test metrics from SonarQube...</p>
          {team && (
            <div className="bg-white bg-opacity-80 rounded-xl p-4 shadow-sm border border-white">
              <p className="text-sm text-gray-700 mb-1">
                <span className="font-semibold">Team:</span> {team.teamName}
              </p>
              <p className="text-sm text-gray-700">
                <span className="font-semibold">Project:</span>{' '}
                {team.metadata?.sonarqube_project_id}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (errorInfo) {
    return <ErrorDisplay errorInfo={errorInfo} team={team} onRetry={handleRefresh} />;
  }

  // No data state
  if (!data) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <BeakerIcon className="w-16 h-16 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Test Data Available</h3>
          <p className="text-gray-600 mb-4">
            No test execution data found for this project. This could be due to:
          </p>
          <div className="text-sm text-gray-500 space-y-2 max-w-md mx-auto">
            <p>• No tests configured in the project</p>
            <p>• Test results not being published to SonarQube</p>
            <p>• Missing test execution history</p>
          </div>
        </div>
      </div>
    );
  }

  // Success state - show metrics with sections
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-white to-purple-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-md">
              <span className="text-white text-lg">
                <BeakerIcon className="w-6 h-6" />
              </span>
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-bold text-gray-900 mb-1">Test Results Analysis</h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFormulaTooltip(!showFormulaTooltip)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    title="View scoring formula"
                  >
                    <InformationCircleIcon className="w-5 h-5 text-gray-400 hover:text-blue-600" />
                  </button>
                  
                  {/* Formula Tooltip */}
                  {showFormulaTooltip && (
                    <div className="absolute top-full left-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-80">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-blue-600">
                          Test Results Score Formula
                        </h4>
                        <button
                          onClick={() => setShowFormulaTooltip(false)}
                          className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                          title="Close"
                        >
                          <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="text-sm">
                        <p className="text-gray-600 mb-3 text-xs">
                          Based on test execution success and stability metrics across all test types
                        </p>
                        <div className="mb-3">
                          <p className="font-medium text-gray-800 text-xs mb-1">Components:</p>
                          <ul className="text-xs text-gray-600 space-y-1">
                            <li>• Unit Test Success Rate: (Passed Tests / Total Tests) × 100</li>
                            <li>• E2E Test Stability: User journey completion rate</li>
                            <li>• Performance Test Results: Load handling capability</li>
                            <li>• Overall Test Coverage: Comprehensive testing score</li>
                          </ul>
                        </div>
                        <div className="border-t border-gray-100 pt-2">
                          <p className="font-medium text-gray-800 text-xs mb-1">Calculation:</p>
                          <p className="text-xs text-gray-600 font-mono bg-gray-50 p-2 rounded">
                            Final Score = (Unit × 0.5) + (E2E × 0.3) + (Performance × 0.2)
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Team:</span> {team?.teamName}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Platform:</span> {team?.metadata?.platform}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Project:</span>{' '}
                  {team?.metadata?.sonarqube_project_id}
                </span>
                {isFromCache && (
                  <span className="text-blue-600 bg-blue-50 px-2 py-1 rounded-md border border-blue-200">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {displayTimestamp && (
                  <span className="text-blue-600 bg-blue-50 px-2 py-1 rounded-md border border-blue-200 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(displayTimestamp)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Test Section Navigation */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-1 p-2">
            {testSections.map((section) => {
              const isActive = activeTestSection === section.id;
              const IconComponent = section.icon;
              
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveTestSection(section.id)}
                  className={`
                    flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center
                    ${isActive
                      ? 'bg-blue-100 text-blue-700 border-2 border-blue-200 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 border-2 border-transparent'
                    }
                  `}
                  title={section.description}
                >
                  <IconComponent className="h-5 w-5" />
                  <span className="text-sm font-medium">{section.label}</span>
                  {section.id === 'e2e' || section.id === 'performance' ? (
                    <span className="text-xs bg-orange-100 text-orange-700 px-2 py-0.5 rounded-full">
                      Mock
                    </span>
                  ) : null}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Test Section Content */}
        <div className="p-6">
          {/* Unit Tests Section */}
          {activeTestSection === 'unit' && (
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <BeakerIcon className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Unit Tests</h3>
                  <p className="text-sm text-gray-600">Individual component and function testing results</p>
                </div>
              </div>
              
              {data && data.tests ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <MetricBox
                    icon={BeakerIcon}
                    label="Total Tests"
                    value={formatNumber(data.tests)}
                    infoText={metricInfo.unitTests}
                  />

                  <MetricBox
                    icon={CheckCircleIcon}
                    label="Success Rate"
                    value={calculateSuccessRate()}
                    infoText={metricInfo.testSuccessRate}
                  />

                  <MetricBox
                    icon={XCircleIcon}
                    label="Failures"
                    value={formatNumber(data.test_failures || '0')}
                    infoText={metricInfo.testFailures}
                  />

                  <MetricBox
                    icon={ExclamationTriangleIcon}
                    label="Errors"
                    value={formatNumber(data.test_errors || '0')}
                    infoText={metricInfo.testErrors}
                  />

                  <MetricBox
                    icon={ForwardIcon}
                    label="Skipped"
                    value={formatNumber(data.skipped_tests || '0')}
                    infoText={metricInfo.skippedTests}
                  />

                  {data.test_execution_time && (
                    <MetricBox
                      icon={ClockIcon}
                      label="Execution Time"
                      value={formatTestDuration(data.test_execution_time)}
                      infoText={metricInfo.testDuration}
                    />
                  )}
                </div>
              ) : (
                <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
                  <div className="flex justify-center mb-4">
                    <BeakerIcon className="w-16 h-16 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">No Unit Test Data Available</h3>
                  <p className="text-gray-600 mb-4">
                    No unit test execution data found for this project.
                  </p>
                  <div className="text-sm text-gray-500 space-y-2 max-w-md mx-auto">
                    <p>• No unit tests configured in the project</p>
                    <p>• Test results not being published to SonarQube</p>
                    <p>• Missing test execution history</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* E2E Tests Section */}
          {activeTestSection === 'e2e' && (
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <RocketLaunchIcon className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">E2E Tests</h3>
                  <p className="text-sm text-gray-600">End-to-end user journey testing results</p>
                </div>
                <span className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-xs font-medium">
                  Mock Data - Preview
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <MetricBox
                  icon={RocketLaunchIcon}
                  label="Total E2E Tests"
                  value={e2eData.totalTests.toLocaleString()}
                  infoText="Total number of end-to-end test scenarios covering user journeys"
                />

                <MetricBox
                  icon={CheckCircleIcon}
                  label="Success Rate"
                  value={e2eData.successRate}
                  infoText="Percentage of E2E tests that completed successfully without failures"
                />

                <MetricBox
                  icon={XCircleIcon}
                  label="Failures"
                  value={e2eData.failures.toString()}
                  infoText="Number of E2E tests that failed during execution"
                />

                <MetricBox
                  icon={ExclamationTriangleIcon}
                  label="Errors"
                  value={e2eData.errors.toString()}
                  infoText="Number of E2E tests that encountered errors preventing execution"
                />

                <MetricBox
                  icon={ClockIcon}
                  label="Execution Time"
                  value={e2eData.executionTime}
                  infoText="Total time taken to execute all E2E test scenarios"
                />

                <MetricBox
                  icon={SparklesIcon}
                  label="Browser Coverage"
                  value={e2eData.browserCoverage}
                  infoText="Percentage of supported browsers covered by E2E tests"
                />
              </div>
            </div>
          )}

          {/* Performance Tests Section */}
          {activeTestSection === 'performance' && (
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <BoltIcon className="h-6 w-6 text-yellow-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Performance Tests</h3>
                  <p className="text-sm text-gray-600">Load, stress, and performance testing results</p>
                </div>
                <span className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-xs font-medium">
                  Mock Data - Preview
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <MetricBox
                  icon={BoltIcon}
                  label="Performance Tests"
                  value={performanceData.totalTests.toString()}
                  infoText="Total number of performance tests including load, stress, and spike tests"
                />

                <MetricBox
                  icon={CheckCircleIcon}
                  label="Success Rate"
                  value={performanceData.successRate}
                  infoText="Percentage of performance tests that met the defined performance criteria"
                />

                <MetricBox
                  icon={ClockIcon}
                  label="Avg Load Time"
                  value={performanceData.avgLoadTime}
                  infoText="Average page load time across all performance test scenarios"
                />

                <MetricBox
                  icon={SparklesIcon}
                  label="Throughput"
                  value={performanceData.throughput}
                  infoText="Maximum requests per second the system can handle under load"
                />

                <MetricBox
                  icon={ExclamationTriangleIcon}
                  label="P95 Response Time"
                  value={performanceData.p95ResponseTime}
                  infoText="95th percentile response time - 95% of requests complete within this time"
                />

                <MetricBox
                  icon={XCircleIcon}
                  label="CPU Utilization"
                  value={performanceData.cpuUtilization}
                  infoText="Average CPU utilization during peak load testing scenarios"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TestResultsTab; 