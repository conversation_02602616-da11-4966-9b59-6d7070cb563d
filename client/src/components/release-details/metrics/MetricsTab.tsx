import React from 'react';
import ConfidenceScoreOverview from './ConfidenceScoreOverview';
import DetailedAnalysis from './DetailedAnalysis';
import type { ConfidenceMetric } from '../../../types/metrics';
import type { CosmosTeam } from '../../../services/api';
import type { Release } from '../../../types/api';

interface MetricsTabProps {
  confidenceMetrics: ConfidenceMetric[];
  overallConfidence: number;
  activeMetricsTab: string;
  setActiveMetricsTab: React.Dispatch<React.SetStateAction<string>>;
  hoveredToolIndex: number | null;
  setHoveredToolIndex: React.Dispatch<React.SetStateAction<number | null>>;
  team?: CosmosTeam;
  releaseId?: string;
  release?: Release;
}

const MetricsTab: React.FC<MetricsTabProps> = ({
  confidenceMetrics,
  overallConfidence,
  activeMetricsTab,
  setActiveMetricsTab,
  hoveredToolIndex,
  setHoveredToolIndex,
  team,
  releaseId,
  release
}) => {
  return (
    <div className="space-y-8">
      <ConfidenceScoreOverview 
        confidenceMetrics={confidenceMetrics}
        overallConfidence={overallConfidence}
        hoveredToolIndex={hoveredToolIndex}
        setHoveredToolIndex={setHoveredToolIndex}
        onViewDetails={() => setActiveMetricsTab('detailed')}
      />
      
      <DetailedAnalysis 
        activeMetricsTab={activeMetricsTab}
        setActiveMetricsTab={setActiveMetricsTab}
        team={team}
        releaseId={releaseId}
        release={release}
      />
    </div>
  );
};

export default MetricsTab; 