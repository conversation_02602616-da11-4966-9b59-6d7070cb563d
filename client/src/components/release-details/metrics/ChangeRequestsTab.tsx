import React, { useState } from 'react';
import { 
  DocumentTextIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowTopRightOnSquareIcon,
  ArrowPathIcon,
  InformationCircleIcon
} from "@heroicons/react/24/outline";
import type { CosmosTeam } from '../../../services/api';
import { useMetricsCache, useMetricTimestamp, DEFAULT_CACHE_DURATION } from '../../../hooks/useMetricsCache';
import { fetchChangeRequestsMetricsWithMetadata } from '../../../services/metricsService';
import ErrorDisplay, { type ErrorInfo } from "./ErrorDisplay";
import { classifyError } from "./errorUtils";
import { formatTimestampWithTimezone } from '../../../utils/dateUtils';
import axios from 'axios';

interface ChangeRequest {
  number: string;
  shortDescription: string;
  openedAt: string;
  resolvedAt?: string;
  priority: string;
  assignmentGroup: string;
  state: string;
  sysId: string;
  assignedTo?: string;
  requestedBy?: string;
  callerId?: string;
}

interface ChangeRequestsTabProps {
  team?: CosmosTeam;
  releaseId?: string;
  onRefresh?: (data: any) => void;
}

const ChangeRequestsTab: React.FC<ChangeRequestsTabProps> = ({
  team,
  releaseId,
  onRefresh,
}) => {
  // Pagination state
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const [isFromStoredFallback, setIsFromStoredFallback] = useState(false);
  const [isManualRefresh, setIsManualRefresh] = useState(false);
  const [showFormulaTooltip, setShowFormulaTooltip] = useState(false);

  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'changeRequests');

  // Configure axios for API calls
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

  // Function to store change requests metrics in database
  const storeChangeRequestsMetricsInDatabase = async (
    teamId: string,
    releaseId: string,
    changeRequests: ChangeRequest[],
    forceTimestamp: boolean = false
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/metrics/${releaseId}/change-requests`, {
        teamId,
        changeRequests,
        forceTimestamp
      });

      return response.data;
    } catch (error: any) {
      console.error('Failed to store Change Requests metrics in database:', error.message);
      // Don't throw - this is a background operation
      return null;
    }
  };

  // Helper to construct ServiceNow change request URL
  const constructChangeRequestUrl = (sysId: string) => {
    // Use the working URL format with proper encoding
    const baseUrl = "https://walmartglobal.service-now.com/now/nav/ui/classic/params/target";
    const targetUrl = `change_request.do?sys_id=${sysId}&sysparm_stack=&sysparm_view=&sysparm_view_forced=true`;
    return `${baseUrl}/${encodeURIComponent(targetUrl)}`;
  };

  // Custom fetch function that tracks if data comes from stored metrics (using smart fallback)
  const fetchChangeRequests = async () => {
    if (!team) {
      throw new Error('No team selected');
    }

    try {
      // Reset stored fallback flag when attempting fresh data
      setIsFromStoredFallback(false);

      // Try live data first - this now includes smart fallback logic
      const result = await fetchChangeRequestsMetricsWithMetadata(team, releaseId);

      // Set the stored fallback flag based on the metadata
      if (result.isFromStored) {
        setIsFromStoredFallback(true);
      }

      // Mark fetch as complete after setting all flags
      setIsFetchComplete(true);

      // Return in the expected format for the UI
      return {
        items: result.data.items || [],
        total: result.data.total || 0,
        lastUpdated: result.data.lastUpdated
      };

    } catch (error: any) {
      // Mark fetch as complete even on error
      setIsFetchComplete(true);

      // If the smart fallback in the service also failed, this is a genuine error
      throw error;
    }
  };

  // Use the centralized caching system
  const {
    data: changeRequestsData,
    isLoading,
    error,
    lastUpdated,
    isFromCache,
    refreshData,
    clearCache
  } = useMetricsCache<{
    items: ChangeRequest[];
    total: number;
    lastUpdated?: string;
  }>(
    {
      key: `change-requests-${releaseId}-${team?.metadata?.serviceNowId || 'unknown'}`,
      maxAge: DEFAULT_CACHE_DURATION,
      useLocalStorage: false, // Use session storage
    },
    fetchChangeRequests,
    [releaseId, team?.metadata?.serviceNowId]
  );

  // Track when the fetch is completely done (including setting isFromStoredFallback)
  const [isFetchComplete, setIsFetchComplete] = React.useState(false);

  // Reset fetch complete flag when starting a new fetch
  React.useEffect(() => {
    if (isLoading) {
      setIsFetchComplete(false);
      // Also reset the stored fallback flag when starting a new fetch
      setIsFromStoredFallback(false);
    }
  }, [isLoading]);

  // Mark fetch as complete when using cached data
  React.useEffect(() => {
    if (isFromCache && changeRequestsData) {
      setIsFetchComplete(true);
    }
  }, [isFromCache, changeRequestsData]);

  // Automatically store metrics in database when data is fetched (only after fetch is complete)
  React.useEffect(() => {
    // Only run storage logic when fetch is completely done
    if (!isFetchComplete) {
      return;
    }

    // Small delay to ensure state updates are complete, but much faster than before
    const timeoutId = setTimeout(async () => {
      // Only use the stored fallback flag - don't do age checks on live data
      // Live data can contain old change requests, that's normal and shouldn't prevent storage
      const isLikelyStoredFallback = isFromStoredFallback;

      console.log(`🔍 Change Requests storage conditions:`, {
        hasItems: !!(changeRequestsData?.items && Array.isArray(changeRequestsData.items)),
        itemCount: changeRequestsData?.items?.length || 0,
        hasTeam: !!team,
        hasReleaseId: !!releaseId,
        isFromCache,
        isFromStoredFallback,
        isLikelyStoredFallback,
        isManualRefresh,
        shouldStore: changeRequestsData?.items && Array.isArray(changeRequestsData.items) && changeRequestsData.items.length > 0 && team && releaseId && !isFromCache && !isLikelyStoredFallback
      });

      if (changeRequestsData?.items && Array.isArray(changeRequestsData.items) && changeRequestsData.items.length > 0 && team && releaseId && !isFromCache && !isLikelyStoredFallback) {
        // Only store if we have fresh data with actual items (not from cache or stored fallback)
        try {
          console.log(`📦 Storing Change Requests data with forceTimestamp=${isManualRefresh}`);
          await storeChangeRequestsMetricsInDatabase(team.id, releaseId, changeRequestsData.items, isManualRefresh);

          // Refresh database timestamp after successful storage (await ensures DB write completes first)
          console.log(`🔄 Refreshing database timestamp after storage`);
          refetchDbTimestamp();

        } catch (error) {
          console.warn('Failed to store change requests metrics in background:', error);
          // Don't throw - this shouldn't break the UI
        } finally {
          // Reset manual refresh flag after storage
          setIsManualRefresh(false);
        }
      } else {
        console.log(`⏭️ Skipping Change Requests storage - conditions not met`);
        // Reset manual refresh flag even when skipping storage
        if (isManualRefresh) {
          setIsManualRefresh(false);
        }
      }
    }, 50); // Much smaller delay - just enough to avoid state race conditions

    // Cleanup timeout on unmount or dependency change
    return () => clearTimeout(timeoutId);
  }, [changeRequestsData, team, releaseId, isFromCache, isFromStoredFallback, refetchDbTimestamp, isManualRefresh, isFetchComplete]);

  // Handle refresh with callback - force fresh data
  const handleRefresh = async () => {
    setIsFromStoredFallback(false);
    setIsManualRefresh(true); // Set flag to force timestamp update in database

    // Clear cache to ensure we hit the live API (which will use server-side fallback if needed)
    const cacheKey = `change-requests-${releaseId}-${team?.metadata?.serviceNowId || 'unknown'}`;

    // Clear from session storage
    try {
      sessionStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn('Failed to clear session storage:', error);
    }

    clearCache(); // Clear the cache first
    await refreshData(); // Then force refresh bypassing cache
    if (onRefresh && changeRequestsData) {
      onRefresh(changeRequestsData);
    }
  };

  // Reset pagination when data changes
  React.useEffect(() => {
    setPage(0);
  }, [changeRequestsData]);

  const handleChangePage = (newPage: number) => {
    setPage(newPage);
  };

  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case "1":
        return { color: "bg-red-100 text-red-800 border-red-200", label: "Critical" };
      case "2":
        return { color: "bg-orange-100 text-orange-800 border-orange-200", label: "High" };
      case "3":
        return { color: "bg-yellow-100 text-yellow-800 border-yellow-200", label: "Medium" };
      case "4":
        return { color: "bg-blue-100 text-blue-800 border-blue-200", label: "Low" };
      default:
        return { color: "bg-gray-100 text-gray-800 border-gray-200", label: priority };
    }
  };

  const getStateConfig = (state: string) => {
    switch (state.toLowerCase()) {
      case "closed":
        return { color: "bg-green-100 text-green-800 border-green-200", label: "Closed" };
      case "resolved":
        return { color: "bg-green-100 text-green-800 border-green-200", label: "Resolved" };
      case "work in progress":
        return { color: "bg-blue-100 text-blue-800 border-blue-200", label: "In Progress" };
      case "assigned":
        return { color: "bg-yellow-100 text-yellow-800 border-yellow-200", label: "Assigned" };
      case "pending":
        return { color: "bg-orange-100 text-orange-800 border-orange-200", label: "Pending" };
      default:
        return { color: "bg-gray-100 text-gray-800 border-gray-200", label: state };
    }
  };

  // Convert error to ErrorInfo for compatibility with existing error display
  const errorInfo: ErrorInfo | null = error ? classifyError(error, team) : null;

  // Determine which timestamp to show: database timestamp takes priority
  const displayTimestamp = dbTimestamp || lastUpdated;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-red-50 via-red-50 to-pink-50 rounded-2xl border border-red-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-red-200 border-t-red-600 shadow-lg"></div>
          <div className="absolute inset-0 rounded-full h-20 w-20 border-4 border-transparent border-r-red-400 animate-pulse"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Loading Change Requests</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching change requests from ServiceNow...</p>
          {team?.metadata?.serviceNowId && (
            <div className="bg-white bg-opacity-80 rounded-xl p-4 shadow-sm border border-white">
              <p className="text-sm text-gray-700">
                <span className="font-semibold">Assignment Group:</span> {team.metadata.serviceNowId}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (errorInfo) {
    return <ErrorDisplay errorInfo={errorInfo} team={team} onRetry={handleRefresh} />;
  }

  // No ServiceNow ID configured
  if (!team?.metadata?.serviceNowId) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <ExclamationTriangleIcon className="w-16 h-16 text-yellow-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">ServiceNow Not Configured</h3>
          <p className="text-gray-600 mb-4">
            This team does not have a ServiceNow assignment group configured.
          </p>
          <p className="text-sm text-gray-500">
            Contact your team administrator to configure the ServiceNow integration.
          </p>
        </div>
      </div>
    );
  }

  // No data state
  if (!changeRequestsData || changeRequestsData.items.length === 0) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <DocumentTextIcon className="w-16 h-16 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Change Requests Found</h3>
          <p className="text-gray-600 mb-4">
            No change requests found for assignment group: {team.metadata.serviceNowId}
          </p>
          <button
            onClick={handleRefresh}
            className="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200"
          >
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    );
  }

  // Calculate pagination
  const totalPages = Math.ceil(changeRequestsData.items.length / rowsPerPage);
  const startIndex = page * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentRequests = changeRequestsData.items.slice(startIndex, endIndex);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-white to-red-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-xl flex items-center justify-center shadow-md">
              <DocumentTextIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <h2 className="text-xl font-bold text-gray-900">Change Requests</h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFormulaTooltip(!showFormulaTooltip)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    title="View scoring formula"
                  >
                    <InformationCircleIcon className="w-5 h-5 text-gray-400 hover:text-red-600" />
                  </button>
                  
                  {/* Formula Tooltip */}
                  {showFormulaTooltip && (
                    <div className="absolute top-full left-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-80">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-red-600">
                          Change Requests Score Formula
                        </h4>
                        <button
                          onClick={() => setShowFormulaTooltip(false)}
                          className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                          title="Close"
                        >
                          <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="text-sm">
                        <p className="text-gray-600 mb-3 text-xs">
                          Penalty-based scoring system starting from perfect score
                        </p>
                        <div className="mb-3">
                          <p className="font-medium text-gray-800 text-xs mb-1">Penalty Components:</p>
                          <ul className="text-xs text-gray-600 space-y-1">
                            <li>• Total Requests: min(count × 2, 30) penalty</li>
                            <li>• Open Requests: min(count × 5, 40) penalty</li>
                            <li>• High Priority: min(count × 8, 30) penalty</li>
                          </ul>
                        </div>
                        <div className="border-t border-gray-100 pt-2">
                          <p className="font-medium text-gray-800 text-xs mb-1">Calculation:</p>
                          <p className="text-xs text-gray-600 font-mono bg-gray-50 p-2 rounded">
                            Score = 100 - total_penalty - open_penalty - priority_penalty
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Assignment Group:</span> {team.metadata.serviceNowId}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Total:</span> {changeRequestsData.total}
                </span>
                {isFromCache && (
                  <span className="text-red-600 bg-red-50 px-2 py-1 rounded-md border border-red-200">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {displayTimestamp && (
                  <span className="text-red-600 bg-red-50 px-2 py-1 rounded-md border border-red-200 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(displayTimestamp)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>

          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Change Requests Table */}
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request #
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date Opened
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  State
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRequests.map((cr) => {
                const priorityConfig = getPriorityConfig(cr.priority);
                const stateConfig = getStateConfig(cr.state);
                return (
                  <tr key={cr.sysId} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <a
                        href={constructChangeRequestUrl(cr.sysId)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-red-600 hover:text-red-800 font-medium flex items-center space-x-1"
                      >
                        <span>{cr.number}</span>
                        <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                      </a>
                    </td>
                    <td 
                      className="px-6 py-4 max-w-md overflow-hidden text-ellipsis"
                      title={cr.shortDescription}
                    >
                      <span className="text-sm text-gray-900">{cr.shortDescription}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(cr.openedAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium border ${priorityConfig.color}`}
                      >
                        {priorityConfig.label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium border ${stateConfig.color}`}
                      >
                        {stateConfig.label}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                <span className="font-medium">{Math.min(endIndex, changeRequestsData.items.length)}</span> of{' '}
                <span className="font-medium">{changeRequestsData.items.length}</span> change requests
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleChangePage(page - 1)}
                  disabled={page === 0}
                  className="px-3 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                
                <span className="text-sm text-gray-700">
                  Page <span className="font-medium">{page + 1}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </span>
                
                <button
                  onClick={() => handleChangePage(page + 1)}
                  disabled={page === totalPages - 1}
                  className="px-3 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChangeRequestsTab; 