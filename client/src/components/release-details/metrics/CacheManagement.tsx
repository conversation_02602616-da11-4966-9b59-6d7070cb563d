import React, { useState, useCallback } from 'react';
import { clearAllMetricsCache } from '../../../hooks/useMetricsCache';
import type { CosmosTeam } from '../../../services/api';

interface CacheManagementProps {
  team?: CosmosTeam;
  onClearCache?: () => void;
}

const CacheManagement: React.FC<CacheManagementProps> = ({ team, onClearCache }) => {
  const [isClearing, setIsClearing] = useState(false);
  const [lastCleared, setLastCleared] = useState<Date | null>(null);

  const handleClearCache = useCallback(async () => {
    setIsClearing(true);
    try {
      clearAllMetricsCache();
      setLastCleared(new Date());
      if (onClearCache) {
        onClearCache();
      }
      
      // Show success feedback briefly
      setTimeout(() => {
        setIsClearing(false);
      }, 1000);
      
    } catch (error) {
      console.error('Failed to clear cache:', error);
      setIsClearing(false);
    }
  }, [onClearCache]);

  const getCacheInfo = () => {
    if (!team) return null;

    const cacheKeys = [];
    const storage = sessionStorage;
    
    // Check for common cache keys for this team
    const teamCacheKeys = [
      `metrics_cache_code-quality-${team.teamName}`,
      `metrics_cache_code-coverage-${team.teamName}`,
      `metrics_cache_test-results-${team.teamName}`,
      `metrics_cache_build-stability-${team.teamName}`,
      `metrics_cache_security-${team.teamName}`,
    ];

    for (const key of teamCacheKeys) {
      try {
        const cached = storage.getItem(key);
        if (cached) {
          const parsedData = JSON.parse(cached);
          if (Date.now() <= parsedData.expiresAt) {
            cacheKeys.push({
              key: key.replace(`metrics_cache_`, '').replace(`-${team.teamName}`, ''),
              timestamp: parsedData.timestamp,
              expiresAt: parsedData.expiresAt,
            });
          }
        }
      } catch (error) {
        // Ignore parsing errors
      }
    }

    return cacheKeys;
  };

  const cacheInfo = getCacheInfo();
  const hasCachedData = cacheInfo && cacheInfo.length > 0;

  return (
    <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-sm">📋</span>
          </div>
          <div>
            <h4 className="text-sm font-semibold text-gray-900">Cache Status</h4>
            <div className="text-xs text-gray-600">
              {hasCachedData ? (
                <span className="text-green-600">
                  {cacheInfo!.length} metric{cacheInfo!.length !== 1 ? 's' : ''} cached
                </span>
              ) : (
                <span className="text-gray-500">No cached data</span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {lastCleared && (
            <span className="text-xs text-gray-500 bg-green-50 px-2 py-1 rounded border border-green-200">
              Cleared {lastCleared.toLocaleTimeString()}
            </span>
          )}
          
          <button
            onClick={handleClearCache}
            disabled={isClearing || !hasCachedData}
            className="text-xs bg-red-50 hover:bg-red-100 text-red-700 px-3 py-1.5 rounded-md border border-red-200 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
            title={hasCachedData ? "Clear all cached metrics data" : "No cached data to clear"}
          >
            <span className={isClearing ? 'animate-spin' : ''}>
              {isClearing ? '⏳' : '🗑️'}
            </span>
            <span>{isClearing ? 'Clearing...' : 'Clear Cache'}</span>
          </button>
        </div>
      </div>

      {/* Detailed cache info */}
      {hasCachedData && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="text-xs text-gray-600">
            <div className="flex flex-wrap gap-2">
              {cacheInfo!.map((cache) => {
                const age = Date.now() - cache.timestamp;
                const ageMinutes = Math.floor(age / (1000 * 60));
                const expiresInMinutes = Math.floor((cache.expiresAt - Date.now()) / (1000 * 60));
                
                return (
                  <div
                    key={cache.key}
                    className="bg-blue-50 px-2 py-1 rounded border border-blue-200"
                    title={`Cached ${ageMinutes}m ago, expires in ${expiresInMinutes}m`}
                  >
                    <span className="font-medium">{cache.key}</span>
                    <span className="text-blue-600 ml-1">({ageMinutes}m)</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheManagement; 