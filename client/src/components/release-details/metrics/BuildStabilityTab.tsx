import React from 'react';
import { But<PERSON> } from '../../ui/Button';
import { 
  WrenchScrewdriverIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  ClockIcon,
  InformationCircleIcon,
  ChartPieIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import type { CosmosTeam } from '../../../services/api';
import type { Release } from '../../../types/api';
import { useMetricsCache, useMetricTimestamp, DEFAULT_CACHE_DURATION } from '../../../hooks/useMetricsCache';
import { fetchBuildStabilityMetricsWithMetadata } from '../../../services/metricsService';
import axios from 'axios';
import { formatTimestampWithTimezone } from '../../../utils/dateUtils';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from 'chart.js';
import { Pie, Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

interface BuildStabilityTabProps {
  team?: CosmosTeam;
  release?: Release;
  releaseId?: string;
  initialData?: any;
  onRefresh?: (data: any) => void;
}

interface BuildStatistics {
  total: number;
  success: number;
  failure: number;
  aborted: number;
  unstable: number;
  successRate: number;
  failureRate: number;
  avgBuildTime: number;
  lastBuildDate: string;
  firstBuildDate: string;
}

interface BuildStabilityData {
  statistics: BuildStatistics;
  builds: any[];
  teamId: string;
  branch: string;
  qualityScore: number;
}

const BuildStabilityTab: React.FC<BuildStabilityTabProps> = ({
  team,
  release,
  releaseId,
  initialData: _initialData,
  onRefresh,
}) => {
  const [isFromStoredFallback, setIsFromStoredFallback] = React.useState(false);
  const [isManualRefresh, setIsManualRefresh] = React.useState(false);
  const [showFormulaTooltip, setShowFormulaTooltip] = React.useState(false);

  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'buildStability');

  // Configure axios for API calls
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

  // Function to store build stability metrics in database
  const storeBuildStabilityMetricsInDatabase = async (
    teamId: string,
    releaseId: string,
    buildData: BuildStabilityData,
    releaseData: Release,
    forceTimestamp: boolean = false
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/metrics/${releaseId}/build-stability`, {
        teamId,
        buildData,
        releaseData: {
          releaseBranch: releaseData.releaseBranch,
          createdAt: releaseData.createdAt,
          releaseRolloutDate: releaseData.releaseRolloutDate
        },
        forceTimestamp
      });
      return response.data;
    } catch (error: any) {
      console.error('❌ Failed to store Build Stability metrics in database:', error.message);
      // Don't throw - this is a background operation
      return null;
    }
  };

  // Custom fetch function that tracks if data comes from stored metrics (using smart fallback)
  const fetchBuildStability = async (): Promise<BuildStabilityData> => {
    if (!team) {
      throw new Error('No team selected');
    }

    try {
      // Reset stored fallback flag when attempting fresh data
      setIsFromStoredFallback(false);

      // Get the branch to use for the API call
      const branch = release?.releaseBranch || team.metadata?.repo_default_branch || 'main';

      // Try live data first - this now includes smart fallback logic
      const result = await fetchBuildStabilityMetricsWithMetadata(team, releaseId, branch);

      // Set the stored fallback flag based on the metadata
      if (result.isFromStored) {
        setIsFromStoredFallback(true);
      }

      // Mark fetch as complete after setting all flags
      setIsFetchComplete(true);

      return result.data;

    } catch (error: any) {
      // Mark fetch as complete even on error
      setIsFetchComplete(true);

      // If the smart fallback in the service also failed, this is a genuine error
      throw error;
    }
  };

  // Use the centralized caching system
  const { data: buildData, isLoading, error, lastUpdated, isFromCache, refreshData } =
    useMetricsCache<BuildStabilityData>(
      {
        key: `build-stability-${releaseId}-${team?.id || 'unknown'}`,
        maxAge: DEFAULT_CACHE_DURATION,
        useLocalStorage: false, // Use session storage
      },
      fetchBuildStability,
      [team?.id, team?.metadata?.looper_team_id, releaseId, release?.releaseBranch]
    );

  // Reset fetch complete flag when starting a new fetch
  React.useEffect(() => {
    if (isLoading) {
      setIsFetchComplete(false);
    }
  }, [isLoading]);

  // Mark fetch as complete when using cached data
  React.useEffect(() => {
    if (isFromCache && buildData) {
      setIsFetchComplete(true);
    }
  }, [isFromCache, buildData]);

  // Track when the fetch is completely done (including setting isFromStoredFallback)
  const [isFetchComplete, setIsFetchComplete] = React.useState(false);

  // Automatically store metrics in database when data is fetched (only after fetch is complete)
  React.useEffect(() => {
    // Only run storage logic when fetch is completely done
    if (!isFetchComplete) {
      return;
    }

    const storeData = async () => {


      // Only use the stored fallback flag - don't do age checks on live data
      // Live data can contain old builds, that's normal and shouldn't prevent storage
      const isLikelyStoredFallback = isFromStoredFallback;

      console.log(`🔍 Build Stability storage conditions:`, {
        hasBuildData: !!buildData,
        hasStatistics: !!(buildData?.statistics),
        totalBuilds: buildData?.statistics?.total || 0,
        hasTeam: !!team,
        hasReleaseId: !!releaseId,
        hasRelease: !!release,
        isFromCache,
        isFromStoredFallback,
        isLikelyStoredFallback,
        isManualRefresh,
        shouldStore: buildData && buildData.statistics && buildData.statistics.total > 0 && team && releaseId && release && !isFromCache && !isLikelyStoredFallback
      });

      if (buildData && buildData.statistics && buildData.statistics.total > 0 && team && releaseId && release && !isFromCache && !isLikelyStoredFallback) {
        // Only store if we have fresh data with actual build statistics (not from cache or stored fallback)
        try {
          console.log(`📦 Storing Build Stability data with forceTimestamp=${isManualRefresh}`);
          await storeBuildStabilityMetricsInDatabase(team.id, releaseId, buildData, release, isManualRefresh);

          // Refresh database timestamp after successful storage
          console.log(`🔄 Refreshing database timestamp after storage`);
          setTimeout(() => {
            refetchDbTimestamp();
          }, 500); // Reduced delay for faster UI update

        } catch (error) {
          console.warn('Failed to store build stability metrics in background:', error);
          // Don't throw - this shouldn't break the UI
        } finally {
          // Reset manual refresh flag after storage
          setIsManualRefresh(false);
        }
      } else {
        console.log(`⏭️ Skipping Build Stability storage - conditions not met`);
        // Reset manual refresh flag even when skipping storage
        if (isManualRefresh) {
          setIsManualRefresh(false);
        }
      }
    };

    storeData();
  }, [buildData, team, releaseId, release, isFromCache, isFromStoredFallback, refetchDbTimestamp, isManualRefresh, isFetchComplete]);

  // Handle refresh with callback - force fresh data
  const handleRefresh = async () => {
    setIsFromStoredFallback(false);
    setIsManualRefresh(true); // Set flag to force timestamp update in database

    // Clear cache to ensure we hit the live API (which will use server-side fallback if needed)
    const cacheKey = `build-stability-${releaseId}-${team?.id || 'unknown'}`;

    // Clear from session storage
    try {
      sessionStorage.removeItem(cacheKey);
    } catch (e) {
      console.warn('Failed to clear session storage:', e);
    }

    await refreshData(); // Force refresh bypassing cache
    if (onRefresh && buildData) {
      onRefresh({ data: buildData });
    }
  };

  // Format build time
  const formatBuildTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  // Determine which timestamp to show: database timestamp takes priority
  const displayTimestamp = dbTimestamp || lastUpdated;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-cyan-50 via-cyan-50 to-teal-50 rounded-2xl border border-cyan-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-cyan-200 border-t-cyan-600 shadow-lg"></div>
          <div className="absolute inset-0 rounded-full h-20 w-20 border-4 border-transparent border-r-cyan-400 animate-pulse"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Analyzing Build Stability</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching build metrics from Looper...</p>
          {team && release && (
            <div className="bg-white bg-opacity-80 rounded-xl p-4 shadow-sm border border-white">
              <p className="text-sm text-gray-700 mb-1">
                <span className="font-semibold">Team:</span> {team.teamName}
              </p>
              <p className="text-sm text-gray-700">
                <span className="font-semibold">Branch:</span> {release.releaseBranch || team.metadata?.repo_default_branch || 'main'}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Error state with detailed error information
  if (error) {
    const errorMessage = error.message || String(error);
    const isNetworkError = errorMessage.includes('Network Error') || errorMessage.includes('Cannot connect') || errorMessage.includes('ECONNREFUSED');
    const isTimeoutError = errorMessage.includes('Timeout Error') || errorMessage.includes('timeout');
    const isAuthError = errorMessage.includes('Authentication Error') || errorMessage.includes('Access denied');
    const isCertificateError = errorMessage.includes('SSL Certificate Error') || errorMessage.includes('certificate');
    
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            {isNetworkError || isTimeoutError || isCertificateError ? (
              <ExclamationTriangleIcon className="w-16 h-16 text-orange-400" />
            ) : (
              <ExclamationTriangleIcon className="w-16 h-16 text-red-400" />
            )}
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            {isNetworkError ? 'Network Connection Issue' : 
             isTimeoutError ? 'Service Timeout' :
             isAuthError ? 'Authentication Required' :
             isCertificateError ? 'SSL Certificate Issue' :
             'Build Stability Error'}
          </h3>
          <p className="text-gray-600 mb-4">{errorMessage}</p>
          
          {(isNetworkError || isTimeoutError || isCertificateError) && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4 text-left">
              <h4 className="text-sm font-medium text-orange-800 mb-2">💡 Troubleshooting Tips:</h4>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• Ensure you're connected to the corporate network or VPN</li>
                <li>• Check if the Looper service is accessible from your network</li>
                {isCertificateError && <li>• SSL certificate issues are common in corporate environments</li>}
                <li>• Try accessing the service from a different network environment</li>
                <li>• Contact your network administrator if the issue persists</li>
              </ul>
            </div>
          )}
          
          {team && release && (
            <div className="text-sm text-gray-500 mb-4 bg-gray-50 rounded-lg p-3">
              <p><strong>Team:</strong> {team.teamName}</p>
              <p><strong>Looper Team ID:</strong> {team.metadata?.looper_team_id}</p>
              <p><strong>Branch:</strong> {release.releaseBranch || team.metadata?.repo_default_branch || 'main'}</p>
            </div>
          )}
          
          <div className="flex justify-center space-x-3">
            <Button onClick={handleRefresh} className="bg-cyan-600 hover:bg-cyan-700 text-white">
              <ArrowPathIcon className="w-4 h-4 mr-2" />
              Retry Connection
            </Button>
            {(isNetworkError || isTimeoutError || isCertificateError) && (
              <Button onClick={() => window.open('https://dx.walmart.com/proxy/nextgenci/v1/nextgen-api', '_blank')} variant="outline">
                Test Looper Access
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // No Looper team ID configured
  if (!team?.metadata?.looper_team_id) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <ExclamationTriangleIcon className="w-16 h-16 text-yellow-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Looper Not Configured</h3>
          <p className="text-gray-600 mb-4">
            This team does not have a Looper team ID configured for build stability metrics.
          </p>
          <p className="text-sm text-gray-500">
            Contact your team administrator to configure the Looper integration.
          </p>
        </div>
      </div>
    );
  }

  // No data state
  if (!buildData || !buildData.statistics) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <WrenchScrewdriverIcon className="w-16 h-16 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Build Data Found</h3>
          <p className="text-gray-600 mb-4">
            No build stability data found for this release branch.
          </p>
          {team && release && (
            <div className="text-sm text-gray-500 mb-4">
              <p><strong>Team ID:</strong> {team.metadata.looper_team_id}</p>
              <p><strong>Branch:</strong> {release.releaseBranch || team.metadata?.repo_default_branch || 'main'}</p>
            </div>
          )}
          <Button onClick={handleRefresh} className="bg-cyan-600 hover:bg-cyan-700 text-white">
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const { statistics } = buildData;

  return (
    <div className="space-y-8">
      {/* Header with consistent styling pattern */}
      <div className="bg-gradient-to-r from-white to-cyan-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 via-cyan-600 to-cyan-700 rounded-xl flex items-center justify-center shadow-md">
              <WrenchScrewdriverIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-bold text-gray-900 mb-1">Build Stability Analysis</h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFormulaTooltip(!showFormulaTooltip)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    title="View scoring formula"
                  >
                    <InformationCircleIcon className="w-5 h-5 text-gray-400 hover:text-cyan-600" />
                  </button>
                  
                  {/* Formula Tooltip */}
                  {showFormulaTooltip && (
                    <div className="absolute top-full left-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-50 w-80">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-cyan-600">
                          Build Stability Score Formula
                        </h4>
                        <button
                          onClick={() => setShowFormulaTooltip(false)}
                          className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                          title="Close"
                        >
                          <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="text-sm">
                        <p className="text-gray-600 mb-3 text-xs">
                          Based on Looper build metrics with direct success rate mapping
                        </p>
                        <div className="mb-3">
                          <p className="font-medium text-gray-800 text-xs mb-1">Components:</p>
                          <ul className="text-xs text-gray-600 space-y-1">
                            <li>• Success Rate: (Successful Builds / Total Builds) × 100</li>
                            <li>• Direct mapping: Success Rate = Metric Score</li>
                          </ul>
                        </div>
                        <div className="border-t border-gray-100 pt-2">
                          <p className="font-medium text-gray-800 text-xs mb-1">Calculation:</p>
                          <p className="text-xs text-gray-600 font-mono bg-gray-50 p-2 rounded">
                            Score = Success Rate (0-100%)
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Team:</span> {team?.teamName || 'Unknown'}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Branch:</span> {release?.releaseBranch || team?.metadata?.repo_default_branch || 'main'}
                </span>
                {isFromCache && (
                  <span className="text-cyan-600 bg-cyan-50 px-2 py-1 rounded-md border border-cyan-200">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-cyan-600 bg-cyan-50 px-2 py-1 rounded-md border border-cyan-200">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {displayTimestamp && (
                  <span className="text-cyan-600 bg-cyan-50 px-2 py-1 rounded-md border border-cyan-200 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(displayTimestamp)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-cyan-600 hover:bg-cyan-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>


      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            label: 'Total Builds',
            value: statistics.total.toLocaleString(),
            icon: DocumentTextIcon,
            color: 'cyan',
            bgClass: 'bg-cyan-50',
            iconClass: 'bg-cyan-100',
            textClass: 'text-cyan-600'
          },
          {
            label: 'Success Rate',
            value: `${Math.round(statistics.successRate)}%`,
            icon: CheckCircleIcon,
            color: 'green',
            bgClass: 'bg-green-50',
            iconClass: 'bg-green-100',
            textClass: 'text-green-600'
          },
          {
            label: 'Failures',
            value: statistics.failure.toLocaleString(),
            icon: XCircleIcon,
            color: 'red',
            bgClass: 'bg-red-50',
            iconClass: 'bg-red-100',
            textClass: 'text-red-600'
          },
          {
            label: 'Avg Build Time',
            value: formatBuildTime(statistics.avgBuildTime),
            icon: ClockIcon,
            color: 'cyan',
            bgClass: 'bg-cyan-50',
            iconClass: 'bg-cyan-100',
            textClass: 'text-cyan-600'
          }
        ].map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgClass} rounded-xl p-6 border border-gray-200 shadow-sm`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 ${stat.iconClass} rounded-lg flex items-center justify-center shadow-sm`}>
                <stat.icon className={`w-6 h-6 ${stat.textClass}`} />
              </div>
            </div>
            <div className="space-y-1">
              <div className={`text-2xl font-bold ${stat.textClass}`}>
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Build Status Distribution Pie Chart */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center mb-6">
            <div className="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
              <ChartPieIcon className="w-5 h-5 text-cyan-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Build Status Distribution</h3>
              <p className="text-sm text-gray-600">Breakdown of build outcomes</p>
            </div>
          </div>
          
          <div className="h-80 flex items-center justify-center">
            <Pie
              data={{
                labels: ['Success', 'Failed', 'Aborted', 'Unstable'],
                datasets: [
                  {
                    data: [
                      statistics.success,
                      statistics.failure,
                      statistics.aborted,
                      statistics.unstable
                    ],
                    backgroundColor: [
                      '#10b981', // green for success
                      '#ef4444', // red for failed
                      '#f59e0b', // amber for aborted
                      '#f97316'  // orange for unstable
                    ],
                    borderColor: [
                      '#059669',
                      '#dc2626',
                      '#d97706',
                      '#ea580c'
                    ],
                    borderWidth: 2,
                    hoverBackgroundColor: [
                      '#059669',
                      '#dc2626',
                      '#d97706',
                      '#ea580c'
                    ]
                  }
                ]
              }}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom' as const,
                    labels: {
                      padding: 20,
                      font: {
                        size: 12
                      },
                      usePointStyle: true,
                      pointStyle: 'circle'
                    }
                  },
                  tooltip: {
                    callbacks: {
                      label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                        return `${label}: ${value} (${percentage}%)`;
                      }
                    },
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1
                  }
                }
              }}
            />
          </div>
        </div>

        {/* Build Duration Trend Line Chart */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
          <div className="flex items-center mb-6">
            <div className="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
              <ChartBarIcon className="w-5 h-5 text-cyan-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Build Duration Trends</h3>
              <p className="text-sm text-gray-600">Recent build performance over time</p>
            </div>
          </div>
          
          <div className="h-80">
            {buildData.builds && buildData.builds.length > 0 ? (
              <Line
                data={{
                  labels: buildData.builds
                    .sort((a: any, b: any) => (a.createdAt || 0) - (b.createdAt || 0)) // Sort by creation time
                    .map((build: any) => 
                      build.buildNumber ? `#${build.buildNumber}` : `Build ${build.id?.slice(-4) || 'N/A'}`
                    ),
                  datasets: [
                    {
                      label: 'Build Duration (minutes)',
                      data: buildData.builds
                        .sort((a: any, b: any) => (a.createdAt || 0) - (b.createdAt || 0))
                        .map((build: any) => {
                          const duration = build.buildTimeInSecs || build.duration || build.buildTime || 0;
                          return Math.round(duration / 60); // Convert to minutes
                        }),
                      borderColor: '#06b6d4',
                      backgroundColor: 'rgba(6, 182, 212, 0.1)',
                      borderWidth: 3,
                      fill: true,
                      tension: 0.4,
                      pointBackgroundColor: buildData.builds
                        .sort((a: any, b: any) => (a.createdAt || 0) - (b.createdAt || 0))
                        .map((build: any) => {
                          const status = build.buildStatus || build.result || build.status || 'UNKNOWN';
                          switch (status.toUpperCase()) {
                            case 'SUCCESS': case 'SUCCESSFUL': return '#10b981';
                            case 'FAILURE': case 'FAILED': case 'FAIL': return '#ef4444';
                            case 'ABORTED': case 'ABORT': return '#f59e0b';
                            case 'UNSTABLE': return '#f97316';
                            default: return '#6b7280';
                          }
                        }),
                      pointBorderColor: '#ffffff',
                      pointBorderWidth: 2,
                      pointRadius: 6,
                      pointHoverRadius: 8,
                      pointHoverBackgroundColor: '#06b6d4',
                      pointHoverBorderWidth: 3
                    }
                  ]
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  interaction: {
                    intersect: false,
                    mode: 'index'
                  },
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: 'rgba(0, 0, 0, 0.8)',
                      titleColor: 'white',
                      bodyColor: 'white',
                      borderColor: 'rgba(255, 255, 255, 0.1)',
                      borderWidth: 1,
                      callbacks: {
                        title: function(context) {
                          const build = buildData.builds
                            .sort((a: any, b: any) => (a.createdAt || 0) - (b.createdAt || 0))[context[0].dataIndex];
                          return `Build #${build.buildNumber || 'N/A'}`;
                        },
                        label: function(context) {
                          const build = buildData.builds
                            .sort((a: any, b: any) => (a.createdAt || 0) - (b.createdAt || 0))[context.dataIndex];
                          const duration = Math.round((build.buildTimeInSecs || build.duration || build.buildTime || 0) / 60);
                          const status = build.buildStatus || build.result || build.status || 'Unknown';
                          const date = build.createdAt ? new Date(build.createdAt).toLocaleDateString() : 'Unknown';
                          const flowName = build.flowName || 'Unknown Flow';
                          
                          return [
                            `Duration: ${duration} minutes`,
                            `Status: ${status}`,
                            `Flow: ${flowName}`,
                            `Date: ${date}`
                          ];
                        }
                      }
                    }
                  },
                  scales: {
                    x: {
                      title: {
                        display: true,
                        text: 'Build Number (Chronological Order)',
                        font: {
                          size: 12,
                          weight: 'bold'
                        },
                        color: '#6b7280'
                      },
                      grid: {
                        display: false
                      },
                      ticks: {
                        color: '#6b7280',
                        font: {
                          size: 11
                        }
                      }
                    },
                    y: {
                      title: {
                        display: true,
                        text: 'Duration (minutes)',
                        font: {
                          size: 12,
                          weight: 'bold'
                        },
                        color: '#6b7280'
                      },
                      beginAtZero: true,
                      grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                      },
                      ticks: {
                        color: '#6b7280',
                        font: {
                          size: 11
                        },
                        callback: function(value) {
                          return value + 'm';
                        }
                      }
                    }
                  }
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500 bg-gray-50 rounded-lg">
                <div className="text-center p-8">
                  <ChartBarIcon className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <h4 className="text-lg font-medium text-gray-600 mb-2">No Build History Available</h4>
                  <p className="text-sm text-gray-500 mb-4">Duration trends will appear when build data is available</p>
                  <div className="bg-white rounded-lg p-4 border border-gray-200 text-xs text-gray-600">
                    <p><strong>Expected data:</strong> Build time, status, and timestamps</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuildStabilityTab; 