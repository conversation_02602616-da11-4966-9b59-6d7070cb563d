import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '../../ui/Button';
import { 
  DocumentTextIcon,
  PlusIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  UserIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';
import type { CosmosTeam } from '../../../services/api';
import type { Release } from '../../../types/api';
import { useAuth } from '../../../hooks/useAuth';
import { useMetricTimestamp } from '../../../hooks/useMetricsCache';
import axios from 'axios';
import { formatTimestampWithTimezone } from '../../../utils/dateUtils';

// PCF Request interfaces
export interface PCFRequest {
  id: string;
  releaseId: string;
  teamId: string;
  jiraTicket: string;
  summary: string;
  description: string;
  requestedBy: string;
  requestedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  approver?: string;
  approvedAt?: string;
  approverRole?: string;
  rejectionReason?: string;
}

export interface CreatePCFRequest {
  jiraTicket: string;
  summary: string;
  description: string;
  approver?: string;
}

interface PostCodeFreezeTabProps {
  team: CosmosTeam;
  release: Release;
  releaseId?: string;
  onRefresh: (data: any) => void;
}

const PostCodeFreezeTab: React.FC<PostCodeFreezeTabProps> = ({
  team,
  release,
  releaseId,
  onRefresh
}) => {
  const { user } = useAuth();
  const [pcfRequests, setPcfRequests] = useState<PCFRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectingRequestId, setRejectingRequestId] = useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [expandedRequests, setExpandedRequests] = useState<Set<string>>(new Set());

  // Cache and timestamp state for metadata display
  const [isFromCache, setIsFromCache] = useState(false);
  const [isFromStoredFallback, setIsFromStoredFallback] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);


  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'pcfRequests');

  // Form state
  const [formData, setFormData] = useState<CreatePCFRequest>({
    jiraTicket: '',
    summary: '',
    description: '',
    approver: ''
  });

  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

  // Check if user is logged in and can create requests
  const canCreateRequest = (): boolean => {
    return !!(user?.email && releaseId && team);
  };

  // Get user status message
  const getUserStatusMessage = (): string => {
    if (!user?.email) return 'Please log in to create PCF requests';
    if (!team) return 'Please select a team to create PCF requests';
    if (!releaseId) return 'Please select a release to create PCF requests';
    return '';
  };

  // Toggle request expansion
  const toggleRequestExpansion = (requestId: string) => {
    setExpandedRequests(prev => {
      const newSet = new Set(prev);
      if (newSet.has(requestId)) {
        newSet.delete(requestId);
      } else {
        newSet.add(requestId);
      }
      return newSet;
    });
  };

  // Get PCF approvers from team roles
  const getPCFApprovers = (): string[] => {
    if (!team?.roles) return [];
    
    // Look for PCF approvers in team roles
    // First check if there's a specific 'pcf_approvers' role
    if ((team.roles as any).pcf_approvers) {
      // Handle both array and object formats
      const pcfApprovers = (team.roles as any).pcf_approvers;
      if (Array.isArray(pcfApprovers)) {
        return pcfApprovers as string[];
      } else if (typeof pcfApprovers === 'object') {
        return Object.values(pcfApprovers).flat() as string[];
      }
    }
    
    // Fallback to release_devops role
    if ((team.roles as any).release_devops) {
      const releaseDevops = (team.roles as any).release_devops;
      if (Array.isArray(releaseDevops)) {
        return releaseDevops as string[];
      } else if (typeof releaseDevops === 'object') {
        return Object.values(releaseDevops).flat() as string[];
      }
    }
    
    // Further fallback to engineering_leadership if available
    if (team.roles.engineering_leadership) {
      return team.roles.engineering_leadership;
    }
    
    return [];
  };

  // Fetch PCF requests
  const fetchPCFRequests = async (forceRefresh: boolean = false) => {
    if (!releaseId) return;

    try {
      setIsLoading(true);
      setError(null);

      // Reset cache flags for fresh data
      setIsFromCache(false);
      setIsFromStoredFallback(false);

      // Add cache-busting parameter to ensure fresh data
      const timestamp = Date.now();
      const response = await axios.get(`${API_BASE_URL}/api/pcf-requests/${releaseId}?t=${timestamp}&refresh=${forceRefresh}`);
      setPcfRequests(response.data);

      // Set timestamp for when data was fetched
      setLastUpdated(new Date().toISOString());

      // Refresh database timestamp after successful fetch (increased delay)
      setTimeout(() => {
        refetchDbTimestamp();
      }, 2000);

    } catch (err: any) {
      console.error('Failed to fetch PCF requests:', err);
      setError(err.response?.data?.error || 'Failed to fetch PCF requests');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle manual refresh
  const handleRefresh = async () => {
    await fetchPCFRequests(true);
  };

  // Create PCF request
  const handleCreateRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Debug logging to identify missing values
    console.log('PCF Create Request Debug:', {
      releaseId,
      team: team?.teamName,
      userEmail: user?.email,
      hasReleaseId: !!releaseId,
      hasTeam: !!team,
      hasUser: !!user?.email
    });
    
    if (!releaseId || !team || !user?.email) {
      console.error('PCF Create Request failed - missing required data:', {
        releaseId: !!releaseId,
        team: !!team,
        userEmail: !!user?.email
      });
      setError('Missing required data: Please ensure you are logged in and have selected a team and release.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null); // Clear any previous errors
      
      const requestData = {
        ...formData,
        requestedBy: user.email
      };

      console.log('Sending PCF request:', requestData);
      const response = await axios.post(`${API_BASE_URL}/api/pcf-requests/${releaseId}`, requestData);
      console.log('PCF request created successfully:', response.data);
      
      // Reset form and close modal
      setFormData({
        jiraTicket: '',
        summary: '',
        description: '',
        approver: ''
      });
      setShowCreateModal(false);
      
      // Refresh data
      await fetchPCFRequests();
      
      if (onRefresh) {
        onRefresh({ action: 'created', data: pcfRequests });
      }
    } catch (err: any) {
      console.error('Failed to create PCF request:', err);
      setError(err.response?.data?.error || 'Failed to create PCF request');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Approve/Reject PCF request
  const handleApprovalAction = async (requestId: string, action: 'approve' | 'reject', reason?: string) => {
    if (!user?.email) return;

    // For reject action, show custom modal if no reason provided
    if (action === 'reject' && !reason) {
      setRejectingRequestId(requestId);
      setShowRejectModal(true);
      return;
    }

    try {
      await axios.post(`${API_BASE_URL}/api/pcf-requests/${requestId}/${action}`, {
        approverEmail: user.email,
        rejectionReason: reason
      });
      
      // Refresh data
      await fetchPCFRequests();
      
      if (onRefresh) {
        onRefresh({ action, requestId, data: pcfRequests });
      }
    } catch (err: any) {
      console.error(`Failed to ${action} PCF request:`, err);
      setError(err.response?.data?.error || `Failed to ${action} PCF request`);
    }
  };

  // Handle rejection submission
  const handleRejectSubmit = async () => {
    if (!rejectingRequestId || !rejectionReason.trim()) return;
    
    setIsSubmitting(true);
    await handleApprovalAction(rejectingRequestId, 'reject', rejectionReason.trim());
    
    // Close modal and reset state
    setShowRejectModal(false);
    setRejectingRequestId(null);
    setRejectionReason('');
    setIsSubmitting(false);
  };

  // Load data on mount
  useEffect(() => {
    fetchPCFRequests();
  }, [releaseId]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-indigo-50 via-indigo-50 to-purple-50 rounded-2xl border border-indigo-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-indigo-200 border-t-indigo-600 shadow-lg"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Loading PCF Requests</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching post code freeze requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-white to-indigo-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 via-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
              <DocumentTextIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <h2 className="text-xl font-bold text-gray-900">Post Code Freeze Requests</h2>
                <div className="relative group">
                  <InformationCircleIcon className="w-5 h-5 text-gray-400 hover:text-indigo-500 cursor-help transition-colors duration-200" />
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-96 px-6 py-4 bg-white text-gray-900 text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 shadow-xl border border-gray-200">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-xl font-semibold text-indigo-600">PCF Requests Overview</h3>
                      {/* <XMarkIcon className="h-4 w-4 text-gray-400" /> */}
                    </div>
                    <p className="text-gray-600 mb-4">Post Code Freeze requests are special changes that require elevated approval.</p>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium text-gray-800 mb-2">Key Points:</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Changes requested after Code Freeze period</li>
                          <li>• Requires Director level approval</li>
                          <li>• For merging into current release</li>
                        </ul>
                      </div>
                    </div>
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 border-8 border-transparent border-b-white"></div>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Team:</span> {team?.teamName || 'Unknown'}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Release:</span> {release?.releaseName || 'Unknown'}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Total Requests:</span> {pcfRequests.length}
                </span>
                {isFromCache && (
                  <span className="text-indigo-700 bg-indigo-100 px-2 py-1 rounded-md border border-indigo-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-indigo-700 bg-indigo-100 px-2 py-1 rounded-md border border-indigo-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {(dbTimestamp || lastUpdated) && (
                  <span className="text-indigo-700 bg-indigo-100 px-2 py-1 rounded-md border border-indigo-300 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(dbTimestamp || lastUpdated!)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
            <p className="text-red-800 font-medium">Error</p>
          </div>
          <p className="text-red-700 text-sm mt-1">{error}</p>
        </div>
      )}

      {/* PCF Requests List */}
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">PCF Requests</h3>
            <div className="flex items-center space-x-3">
              {!user?.email && (
                <span className="bg-yellow-100 text-yellow-800 text-sm font-medium px-2.5 py-0.5 rounded border border-yellow-300">
                  Login required
                </span>
              )}
              <Button
                onClick={() => {
                  console.log('PCF New Request button clicked');
                  if (!user?.email) {
                    alert('Please log in first to create PCF requests. Use the login button in the top navigation.');
                    return;
                  }
                  if (canCreateRequest()) {
                    setShowCreateModal(true);
                  } else {
                    alert('Please select a team and release to create PCF requests.');
                  }
                }}
                className={`flex items-center space-x-2 ${
                  canCreateRequest() 
                    ? 'bg-indigo-600 hover:bg-indigo-700 text-white' 
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
                disabled={!canCreateRequest()}
              >
                <PlusIcon className="w-4 h-4" />
                <span>New Request</span>
              </Button>
            </div>
          </div>
        </div>

        {pcfRequests.length === 0 ? (
          <div className="p-8 text-center">
            <DocumentTextIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">No PCF Requests</h3>
            <p className="text-gray-600 mb-4">
              {!user?.email 
                ? 'Please log in to create post code freeze requests.' 
                : 'No post code freeze requests have been created yet.'
              }
            </p>
            <Button
              onClick={() => {
                console.log('PCF Create First Request button clicked');
                if (!user?.email) {
                  alert('Please log in first to create PCF requests. Use the login button in the top navigation.');
                  return;
                }
                if (canCreateRequest()) {
                  setShowCreateModal(true);
                } else {
                  alert('Please select a team and release to create PCF requests.');
                }
              }}
              className={`${
                canCreateRequest() 
                  ? 'bg-indigo-600 hover:bg-indigo-700 text-white' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              disabled={!canCreateRequest()}
            >
              Create PCF Request
            </Button>
          </div>
        ) : (
          <div className="p-6">
            <div className="space-y-4">
              {pcfRequests.map((request) => {
                const isExpanded = expandedRequests.has(request.id);
                
                return (
                  <div 
                    key={request.id} 
                    className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    {/* Request Header - Always Visible */}
                    <div 
                      className="p-6 cursor-pointer"
                      onClick={() => toggleRequestExpansion(request.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-3">
                            {/* Status Badge */}
                            <span className={`
                              inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                              ${request.status === 'approved' ? 'bg-green-100 text-green-800 border border-green-200' : 
                                request.status === 'rejected' ? 'bg-red-100 text-red-800 border border-red-200' : 
                                'bg-yellow-100 text-yellow-800 border border-yellow-200'}
                            `}>
                              {request.status === 'approved' ? '✅ Approved' : 
                               request.status === 'rejected' ? '❌ Rejected' : 
                               '⏳ Pending'}
                            </span>
                            
                            {/* Jira Ticket */}
                            <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded border border-blue-200">
                              <a 
                                href={`https://jira.walmart.com/browse/${request.jiraTicket}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-blue-800 transition-colors duration-200"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {request.jiraTicket}
                              </a>
                            </span>
                          </div>
                          
                          {/* Summary - Full text always visible */}
                          <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-relaxed">
                            {request.summary}
                          </h3>
                          
                          {/* Key Info Row */}
                          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-1">
                              <UserIcon className="h-4 w-4 text-gray-400" />
                              <span className="font-medium">Requested by:</span>
                              <span className="text-gray-900 font-medium">{request.requestedBy}</span>
                            </div>
                            
                            <div className="flex items-center space-x-1">
                              <CalendarDaysIcon className="h-4 w-4 text-gray-400" />
                              <span>{formatTimestampWithTimezone(request.requestedAt)}</span>
                            </div>
                            
                            {request.status === 'approved' && request.approver && (
                              <div className="flex items-center space-x-1">
                                <CheckCircleIcon className="h-4 w-4 text-green-500" />
                                <span className="font-medium text-green-700">Approved by:</span>
                                <span className="text-green-800 font-medium">{request.approver}</span>
                              </div>
                            )}
                            
                            {request.status === 'rejected' && request.approver && (
                              <div className="flex items-center space-x-1">
                                <XCircleIcon className="h-4 w-4 text-red-500" />
                                <span className="font-medium text-red-700">Rejected by:</span>
                                <span className="text-red-800 font-medium">{request.approver}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Expand/Collapse Icon */}
                        <div className="flex-shrink-0 ml-4">
                          <ChevronDownIcon 
                            className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                              isExpanded ? 'transform rotate-180' : ''
                            }`} 
                          />
                        </div>
                      </div>
                    </div>

                    {/* Expanded Content */}
                    {isExpanded && (
                      <div className="border-t border-gray-100 px-6 pb-6">
                        {/* Description Section */}
                        <div className="py-4">
                          <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                            <DocumentTextIcon className="h-4 w-4 mr-2 text-gray-500" />
                            Description
                          </h4>
                          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                              {request.description || 'No description provided'}
                            </p>
                          </div>
                        </div>

                        {/* Detailed Information Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
                          {/* Request Details */}
                          <div className="space-y-4">
                            <h4 className="text-sm font-semibold text-gray-900 flex items-center border-b border-gray-200 pb-2">
                              <InformationCircleIcon className="h-4 w-4 mr-2 text-blue-500" />
                              Request Details
                            </h4>
                            
                            <div className="space-y-3">
                              <div className="flex flex-col space-y-1">
                                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Jira Ticket</span>
                                <a 
                                  href={`https://jira.walmart.com/browse/${request.jiraTicket}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  {request.jiraTicket}
                                </a>
                              </div>
                              
                              <div className="flex flex-col space-y-1">
                                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Requested By</span>
                                <span className="text-sm text-gray-900 break-all">{request.requestedBy}</span>
                              </div>
                              
                              <div className="flex flex-col space-y-1">
                                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Request Date</span>
                                <span className="text-sm text-gray-700">{formatTimestampWithTimezone(request.requestedAt)}</span>
                              </div>
                              
                              <div className="flex flex-col space-y-1">
                                <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</span>
                                <span className={`text-sm font-medium ${
                                  request.status === 'approved' ? 'text-green-600' : 
                                  request.status === 'rejected' ? 'text-red-600' : 
                                  'text-yellow-600'
                                }`}>
                                  {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Approval/Rejection Details */}
                          <div className="space-y-4">
                            <h4 className="text-sm font-semibold text-gray-900 flex items-center border-b border-gray-200 pb-2">
                              {request.status === 'approved' ? (
                                <><CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />Approval Details</>
                              ) : request.status === 'rejected' ? (
                                <><XCircleIcon className="h-4 w-4 mr-2 text-red-500" />Rejection Details</>
                              ) : (
                                <><ClockIcon className="h-4 w-4 mr-2 text-yellow-500" />Pending Approval</>
                              )}
                            </h4>
                            
                            {request.status === 'approved' && request.approver && request.approvedAt && (
                              <div className="space-y-3">
                                <div className="flex flex-col space-y-1">
                                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Approved By</span>
                                  <span className="text-sm text-green-700 font-medium break-all">{request.approver}</span>
                                </div>
                                
                                <div className="flex flex-col space-y-1">
                                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Approval Date</span>
                                  <span className="text-sm text-gray-700">{formatTimestampWithTimezone(request.approvedAt)}</span>
                                </div>
                                
                                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                                  <div className="flex items-center">
                                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                                    <span className="text-sm font-medium text-green-800">Request Approved</span>
                                  </div>
                                  <p className="text-sm text-green-700 mt-1">
                                    This PCF request has been approved and can proceed with implementation.
                                  </p>
                                </div>
                              </div>
                            )}
                            
                            {request.status === 'rejected' && request.approver && (
                              <div className="space-y-3">
                                <div className="flex flex-col space-y-1">
                                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Rejected By</span>
                                  <span className="text-sm text-red-700 font-medium break-all">{request.approver}</span>
                                </div>
                                
                                {request.rejectionReason && (
                                  <div className="flex flex-col space-y-1">
                                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Rejection Reason</span>
                                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                                      <p className="text-sm text-red-700 whitespace-pre-wrap">{request.rejectionReason}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                            
                            {request.status === 'pending' && (
                              <div className="space-y-3">
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                  <div className="flex items-center">
                                    <ClockIcon className="h-5 w-5 text-yellow-500 mr-2" />
                                    <span className="text-sm font-medium text-yellow-800">Awaiting Approval</span>
                                  </div>
                                  <p className="text-sm text-yellow-700 mt-1">
                                    This request is pending approval from authorized team members.
                                  </p>
                                </div>
                                
                                {/* Show available approvers */}
                                {getPCFApprovers() && getPCFApprovers().length > 0 && (
                                  <div className="flex flex-col space-y-1">
                                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Available Approvers</span>
                                    <div className="text-sm text-gray-700">
                                      {getPCFApprovers().map((approver: string, index: number) => (
                                        <div key={index} className="break-all">• {approver}</div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        {canCreateRequest() && request.status === 'pending' && (
                          <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-100">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApprovalAction(request.id, 'approve');
                              }}
                              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2"
                            >
                              <CheckCircleIcon className="h-4 w-4" />
                              <span>Approve Request</span>
                            </button>
                            
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApprovalAction(request.id, 'reject');
                              }}
                              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center space-x-2"
                            >
                              <XCircleIcon className="h-4 w-4" />
                              <span>Reject Request</span>
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Create PCF Request Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Create Post Code Freeze Request</h3>
            </div>
            
            <form onSubmit={handleCreateRequest} className="p-6 space-y-4">
              <div>
                <label htmlFor="jiraTicket" className="block text-sm font-medium text-gray-700 mb-1">
                  Jira Ticket # <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="jiraTicket"
                  required
                  value={formData.jiraTicket}
                  onChange={(e) => setFormData(prev => ({ ...prev, jiraTicket: e.target.value }))}
                  placeholder="e.g., PROJ-1234"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="summary" className="block text-sm font-medium text-gray-700 mb-1">
                  Summary <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="summary"
                  required
                  value={formData.summary}
                  onChange={(e) => setFormData(prev => ({ ...prev, summary: e.target.value }))}
                  placeholder="Brief summary of the change"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="description"
                  required
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Detailed description of the change along with PR links if available"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="approver" className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Approver (Optional)
                </label>
                <select
                  id="approver"
                  value={formData.approver}
                  onChange={(e) => setFormData(prev => ({ ...prev, approver: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  <option value="">Any available approver</option>
                  {getPCFApprovers().map(approver => (
                    <option key={approver} value={approver}>{approver}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white disabled:opacity-50"
                >
                  {isSubmitting ? 'Creating...' : 'Create Request'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Reject PCF Request Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <XCircleIcon className="h-6 w-6 text-red-600" />
                <h3 className="text-lg font-semibold text-gray-900">Reject PCF Request</h3>
              </div>
            </div>
            
            <div className="p-6 space-y-4">
              <p className="text-sm text-gray-600">
                Please provide a reason for rejecting this PCF request. This will help the requester understand the decision.
              </p>
              
              <div>
                <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-2">
                  Rejection Reason <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="rejectionReason"
                  rows={4}
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Explain why this request is being rejected..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                />
              </div>
              
              <div className="flex items-center justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectingRequestId(null);
                    setRejectionReason('');
                  }}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700"
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleRejectSubmit}
                  disabled={isSubmitting || !rejectionReason.trim()}
                  className="bg-red-600 hover:bg-red-700 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <XCircleIcon className="h-4 w-4" />
                  <span>{isSubmitting ? 'Rejecting...' : 'Reject Request'}</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Floating Action Button for Mobile */}
      <button
        onClick={() => {
          if (canCreateRequest()) {
            setShowCreateModal(true);
          }
        }}
        className={`fixed bottom-6 right-6 p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-40 md:hidden ${
          canCreateRequest()
            ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        }`}
        title={canCreateRequest() ? 'Create PCF Request' : getUserStatusMessage()}
        disabled={!canCreateRequest()}
      >
        <PlusIcon className="w-6 h-6" />
      </button>
    </div>
  );
};

export default PostCodeFreezeTab; 