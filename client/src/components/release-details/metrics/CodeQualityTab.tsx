import React from 'react';
import { Button } from '../../ui/Button';
import { 
  BugAntIcon,
  ShieldExclamationIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import type { CosmosTeam } from '../../../services/api';
import ErrorDisplay, { type ErrorInfo } from './ErrorDisplay';
import { classifyError } from './errorUtils';
import { useMetricsCache, useMetricTimestamp, DEFAULT_CACHE_DURATION } from '../../../hooks/useMetricsCache';
import { fetchCodeQualityMetricsWithMetadata, type ProcessedMetrics } from '../../../services/metricsService';
import { formatTimestampWithTimezone } from '../../../utils/dateUtils';
import axios from 'axios';

interface MetricBoxProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  value: string | number;
  subLabel?: string;
  infoText: string;
  rating?: string;
}

const MetricBox: React.FC<MetricBoxProps> = ({
  icon: Icon,
  label,
  value,
  subLabel,
  infoText,
  rating,
}) => {
  const getRatingInfo = (rating: string): { label: string; color: string } => {
    const ratingMap: { [key: string]: { label: string; color: string } } = {
      '1.0': { label: 'A', color: 'bg-green-100 text-green-800 border border-green-200' },
      '2.0': { label: 'B', color: 'bg-yellow-100 text-yellow-800 border border-yellow-200' },
      '3.0': { label: 'C', color: 'bg-orange-100 text-orange-800 border border-orange-200' },
      '4.0': { label: 'D', color: 'bg-red-100 text-red-800 border border-red-200' },
      '5.0': { label: 'E', color: 'bg-red-200 text-red-900 border border-red-300' },
    };
    return (
      ratingMap[rating] || {
        label: rating,
        color: 'bg-gray-100 text-gray-700 border border-gray-200',
      }
    );
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-lg hover:border-orange-200 transition-all duration-300 group">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-xl">
            <Icon className="h-6 w-6 text-gray-800" />
          </span>
          <span className="text-sm font-semibold text-gray-800">{label}</span>
          <div className="group/tooltip relative">
            <span className="text-gray-400 hover:text-orange-500 cursor-help transition-colors duration-200">
              <InformationCircleIcon className="h-4 w-4" />
            </span>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover/tooltip:opacity-100 transition-opacity duration-200 pointer-events-none z-50 shadow-lg w-64 text-center">
              <div className="leading-relaxed">{infoText}</div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-end justify-between">
        <div>
          <div className="text-3xl font-bold text-orange-600 group-hover:text-orange-700 transition-colors duration-200">
            {value}
          </div>
          {subLabel && <div className="text-xs text-gray-500 mt-1 font-medium">{subLabel}</div>}
        </div>
        {rating && (
          <div className={`px-2 py-1 rounded-md text-xs font-bold ${getRatingInfo(rating).color}`}>
            {getRatingInfo(rating).label}
          </div>
        )}
      </div>
    </div>
  );
};

interface CodeQualityTabProps {
  team?: CosmosTeam;
  releaseId?: string;
  initialData?: any;
  onRefresh?: (data: any) => void;
}

// Configure axios for SonarQube API calls
const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

// Function to store metrics in Azure database
const storeMetricsInDatabase = async (teamId: string, releaseId: string, metrics: any, forceTimestamp: boolean = false) => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/metrics/${releaseId}/code-quality`,
      {
        teamId,
        metrics,
        forceTimestamp
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Failed to store Code Quality metrics in database:', error.message);
    // Don't throw - this is a background operation
    return null;
  }
};

const CodeQualityTab: React.FC<CodeQualityTabProps> = ({ team, releaseId, initialData: _initialData, onRefresh }) => {
  const [showFormulaTooltip, setShowFormulaTooltip] = React.useState(false);
  const [isFromStoredFallback, setIsFromStoredFallback] = React.useState(false);
  const [isManualRefresh, setIsManualRefresh] = React.useState(false);

  // Get the database timestamp for this specific metric
  const { dbTimestamp, refetch: refetchDbTimestamp } = useMetricTimestamp(releaseId, 'codeQuality');

  // Custom fetch function that tracks if data comes from stored metrics (using smart fallback)
  const fetchCodeQualityWithTracking = async (): Promise<ProcessedMetrics> => {
    if (!team) {
      throw new Error('No team selected');
    }

    try {
      // Reset stored fallback flag when attempting fresh data
      setIsFromStoredFallback(false);

      // Try live data first - this now includes smart fallback logic
      const result = await fetchCodeQualityMetricsWithMetadata(team, releaseId);

      // Set the stored fallback flag based on the metadata
      if (result.isFromStored) {
        setIsFromStoredFallback(true);
      }

      // Mark fetch as complete after setting all flags
      setIsFetchComplete(true);

      return result.data;

    } catch (error: any) {
      // Mark fetch as complete even on error
      setIsFetchComplete(true);

      // If the smart fallback in the service also failed, this is a genuine error
      throw error;
    }
  };

  // Use the centralized caching system
  const { data, isLoading, error, lastUpdated, isFromCache, refreshData } =
    useMetricsCache<ProcessedMetrics>(
      {
        key: `code-quality-${releaseId}-${team?.teamName || 'unknown'}`,
        maxAge: DEFAULT_CACHE_DURATION,
        useLocalStorage: false, // Use session storage
      },
      fetchCodeQualityWithTracking,
      [team?.id, team?.teamName, releaseId] // Dependencies that trigger refetch
    );

  // Track when the fetch is completely done (including setting isFromStoredFallback)
  const [isFetchComplete, setIsFetchComplete] = React.useState(false);

  // Reset fetch complete flag when starting a new fetch
  React.useEffect(() => {
    if (isLoading) {
      setIsFetchComplete(false);
      // Also reset the stored fallback flag when starting a new fetch
      setIsFromStoredFallback(false);
    }
  }, [isLoading]);

  // Mark fetch as complete when using cached data
  React.useEffect(() => {
    if (isFromCache && data) {
      setIsFetchComplete(true);
    }
  }, [isFromCache, data]);

  // Automatically store metrics in database when data is fetched (only after fetch is complete)
  React.useEffect(() => {
    // Only run storage logic when fetch is completely done
    if (!isFetchComplete) {
      return;
    }

    // Small delay to ensure state updates are complete, but much faster than before
    const timeoutId = setTimeout(async () => {
      // Only use the stored fallback flag - don't do age checks on live data
      const isLikelyStoredFallback = isFromStoredFallback;

      if (data && team && releaseId && !isFromCache && !isLikelyStoredFallback) {
        // Only store if we have fresh data (not from cache or stored fallback)
        try {
          await storeMetricsInDatabase(team.id, releaseId, data, isManualRefresh);

          // Refresh database timestamp after successful storage (await ensures DB write completes first)
          refetchDbTimestamp();

        } catch (error) {
          console.warn('Failed to store metrics in background:', error);
          // Don't throw - this shouldn't break the UI
        } finally {
          // Reset manual refresh flag after storage
          setIsManualRefresh(false);
        }
      } else {
        // Reset manual refresh flag even when skipping storage
        if (isManualRefresh) {
          setIsManualRefresh(false);
        }
      }
    }, 50); // Much smaller delay - just enough to avoid state race conditions

    // Cleanup timeout on unmount or dependency change
    return () => clearTimeout(timeoutId);
  }, [data, team, releaseId, isFromCache, isFromStoredFallback, refetchDbTimestamp, isManualRefresh, isFetchComplete]);

  // Format large numbers
  const formatNumber = (num: string | number): string => {
    const n = typeof num === 'string' ? parseInt(num) : num;
    if (n >= 1000000) return `${(n / 1000000).toFixed(1)}M`;
    if (n >= 1000) return `${(n / 1000).toFixed(1)}k`;
    return n.toLocaleString();
  };

  // Define metric information
  const metricInfo = {
    bugs: 'Number of bug issues found by SonarQube analysis. Bugs represent coding errors that could lead to unexpected behavior.',
    vulnerabilities:
      'Number of security vulnerabilities found. These are potential security weaknesses that could be exploited.',
    codeSmells:
      'Number of maintainability issues. Code smells indicate areas where code could be improved for better readability and maintainability.',
    coverage:
      'Percentage of code covered by unit tests. Higher coverage generally indicates better tested code.',
    duplicatedLines:
      'Percentage of duplicated lines of code. Lower duplication indicates better code organization and maintainability.',
    reliabilityRating:
      'Overall reliability rating from A (best) to E (worst), based on bug density and severity.',
    securityRating:
      'Overall security rating from A (best) to E (worst), based on vulnerability density and severity.',
    maintainabilityRating:
      'Overall maintainability rating from A (best) to E (worst), based on technical debt ratio.',
    linesOfCode: 'Total number of lines of code (excluding comments and blank lines).',
    complexity:
      'Cyclomatic complexity measures the number of linearly independent paths through the code.',
    unitTests: 'Total number of unit tests in the project.',
  };

  // Handle refresh with callback - force fresh data
  const handleRefresh = async () => {
    setIsFromStoredFallback(false);
    setIsManualRefresh(true); // Set flag to force timestamp update in database

    // Clear cache to ensure we hit the live API (which will use server-side fallback if needed)
    const cacheKey = `code-quality-${releaseId}-${team?.teamName || 'unknown'}`;

    // Clear from session storage
    try {
      sessionStorage.removeItem(cacheKey);
    } catch (error) {
      console.warn('Failed to clear session storage:', error);
    }

    await refreshData();
    if (onRefresh && data) {
      onRefresh({ data });
    }
  };

  // Convert error to ErrorInfo for compatibility with existing error display
  const errorInfo: ErrorInfo | null = error ? classifyError(error, team) : null;

  // Determine which timestamp to show: database timestamp takes priority (will be updated after refresh)
  const displayTimestamp = dbTimestamp || lastUpdated;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-2xl border border-blue-100">
        <div className="relative mb-8">
          <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-blue-600 shadow-lg"></div>
          <div className="absolute inset-0 rounded-full h-20 w-20 border-4 border-transparent border-r-blue-400 animate-pulse"></div>
        </div>
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-3">Analyzing Code Quality</h3>
          <p className="text-gray-600 mb-6 text-lg">Fetching metrics from SonarQube...</p>
          {team && (
            <div className="bg-white bg-opacity-80 rounded-xl p-4 shadow-sm border border-white">
              <p className="text-sm text-gray-700 mb-1">
                <span className="font-semibold">Team:</span> {team.teamName}
              </p>
              <p className="text-sm text-gray-700">
                <span className="font-semibold">Project:</span>{' '}
                {team.metadata?.sonarqube_project_id}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Error state with shared error component
  if (errorInfo) {
    return <ErrorDisplay errorInfo={errorInfo} team={team} onRetry={handleRefresh} />;
  }

  // No data state
  if (!data) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-xl p-8 text-center">
          <div className="flex justify-center mb-4">
            <ChartBarIcon className="w-16 h-16 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Data Available</h3>
          <p className="text-gray-600 mb-4">No code quality data found for this team.</p>
          <Button onClick={handleRefresh} className="bg-blue-600 hover:bg-blue-700 text-white">
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Success state - show metrics
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-white to-orange-50 rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-md">
              <ChartBarIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-bold text-gray-900 mb-1">Code Quality Analysis</h2>
                <div className="relative">
                  <button
                    onClick={() => setShowFormulaTooltip(!showFormulaTooltip)}
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    title="View scoring formula"
                  >
                    <InformationCircleIcon className="w-5 h-5 text-gray-500 hover:text-gray-700" />
                  </button>
                  {showFormulaTooltip && (
                    <div className="absolute top-8 left-0 z-10 w-80 p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-orange-600">Quality Score Formula</h3>
                        <button
                          onClick={() => setShowFormulaTooltip(false)}
                          className="p-1 hover:bg-gray-100 rounded-full transition-colors duration-200"
                          title="Close"
                        >
                          <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p><strong>Rating Weights:</strong></p>
                        <p>• A (1.0): 100 points</p>
                        <p>• B (2.0): 80 points</p>
                        <p>• C (3.0): 60 points</p>
                        <p>• D (4.0): 40 points</p>
                        <p>• E (5.0): 20 points</p>
                        <p className="mt-2"><strong>Formula:</strong> (Total Points / Max Possible) × 100</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Team:</span> {team?.teamName || 'Unknown'}
                </span>
                <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded-md">
                  <span className="font-medium">Project:</span> {team?.metadata?.sonarqube_project_id || 'N/A'}
                </span>
                {isFromCache && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Cached</span>
                    </span>
                  </span>
                )}
                {isFromStoredFallback && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300">
                    <span className="font-medium flex items-center space-x-1">
                      <DocumentTextIcon className="w-4 h-4" />
                      <span>Stored Data</span>
                    </span>
                  </span>
                )}
                {displayTimestamp && (
                  <span className="text-orange-700 bg-orange-100 px-2 py-1 rounded-md border border-orange-300 flex items-center space-x-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>Last updated: {formatTimestampWithTimezone(displayTimestamp)}</span>
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium flex items-center space-x-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* New Code Metrics */}
        <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4">
            <h3 className="text-lg font-bold text-white">New Code</h3>
            <p className="text-orange-100 text-sm">Recent changes and additions</p>
          </div>
          <div className="p-6 space-y-4">
            <MetricBox
              icon={BugAntIcon}
              label="Bugs"
              value={formatNumber(data.new_bugs || '0')}
              infoText={metricInfo.bugs}
              rating={data.new_reliability_rating}
            />
            <MetricBox
              icon={ShieldExclamationIcon}
              label="Vulnerabilities"
              value={formatNumber(data.new_vulnerabilities || '0')}
              infoText={metricInfo.vulnerabilities}
              rating={data.new_security_rating}
            />
            <MetricBox
              icon={CodeBracketIcon}
              label="Code Smells"
              value={formatNumber(data.new_code_smells || '0')}
              infoText={metricInfo.codeSmells}
              rating={data.new_maintainability_rating}
            />
            <MetricBox
              icon={DocumentTextIcon}
              label="Lines Added"
              value={formatNumber(data.new_lines || '0')}
              infoText="Number of new lines of code added in recent changes"
            />
            {data.new_coverage && (
              <MetricBox
                icon={ChartBarIcon}
                label="Coverage"
                value={`${parseFloat(data.new_coverage).toFixed(1)}%`}
                infoText={metricInfo.coverage}
              />
            )}
            {data.new_duplicated_lines_density && (
              <MetricBox
                icon={DocumentDuplicateIcon}
                label="Duplication"
                value={`${parseFloat(data.new_duplicated_lines_density).toFixed(1)}%`}
                infoText={metricInfo.duplicatedLines}
              />
            )}
          </div>
        </div>

        {/* Overall Code Metrics */}
        <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4">
            <h3 className="text-lg font-bold text-white">Overall Code</h3>
            <p className="text-orange-100 text-sm">Complete project metrics</p>
          </div>
          <div className="p-6 space-y-4">
            <MetricBox
              icon={BugAntIcon}
              label="Bugs"
              value={formatNumber(data.bugs || '0')}
              infoText={metricInfo.bugs}
              rating={data.reliability_rating}
            />
            <MetricBox
              icon={ShieldExclamationIcon}
              label="Vulnerabilities"
              value={formatNumber(data.vulnerabilities || '0')}
              infoText={metricInfo.vulnerabilities}
              rating={data.security_rating}
            />
            <MetricBox
              icon={CodeBracketIcon}
              label="Code Smells"
              value={formatNumber(data.code_smells || '0')}
              infoText={metricInfo.codeSmells}
              rating={data.sqale_rating}
            />
            <MetricBox
              icon={DocumentTextIcon}
              label="Lines of Code"
              value={formatNumber(data.ncloc || '0')}
              infoText={metricInfo.linesOfCode}
            />
            {data.coverage && (
              <MetricBox
                icon={ChartBarIcon}
                label="Coverage"
                value={`${parseFloat(data.coverage).toFixed(1)}%`}
                infoText={metricInfo.coverage}
              />
            )}
            {data.duplicated_lines_density && (
              <MetricBox
                icon={DocumentDuplicateIcon}
                label="Duplication"
                value={`${parseFloat(data.duplicated_lines_density).toFixed(1)}%`}
                infoText={metricInfo.duplicatedLines}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeQualityTab;
