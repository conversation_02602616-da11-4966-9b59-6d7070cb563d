import React from 'react';
import type { Release } from '../../services/release';
import type { SignoffStatus } from '../../services/signoffs';
import { ChatbotButton } from './';

interface ReleaseHeaderProps {
  release: Release;
  signoffStatus: SignoffStatus | null;
  overallConfidence: number;
  isPdfGenerating: boolean;
  isEmailProcessing: boolean;
  onPdfDownload: () => Promise<void>;
  onEmailSend: () => void;
  onGoBack: () => void;
  getTeamName: (teamId: string) => string;
  formatReleaseType: (type: string) => string;
  getReleaseTypeColor: (type: string) => string;
}

const ReleaseHeader: React.FC<ReleaseHeaderProps> = ({
  release,
  signoffStatus,
  overallConfidence,
  isPdfGenerating,
  isEmailProcessing,
  onPdfDownload,
  onEmailSend,
  onGoBack,
  getTeamName,
  formatReleaseType,
  getReleaseTypeColor
}) => {
  return (
    <div className="bg-white border-b border-gray-200 sticky top-0 z-20">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onGoBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{release.releaseName}</h1>
              <div className="flex items-center space-x-3 text-sm text-gray-600">
                <span className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span>{getTeamName(release.teamId)}</span>
                </span>
                <span>•</span>
                <span className={`px-2 py-1 ${getReleaseTypeColor(release.releaseType)} rounded text-xs font-medium`}>
                  {formatReleaseType(release.releaseType)}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-8">
            {/* Signoff Progress */}
            <div className="text-right">
              <div className="text-sm text-gray-500">Signoff Progress</div>
              <div className="text-2xl font-bold text-green-600">{signoffStatus?.overallProgress || 0}%</div>
              <div className="text-xs text-gray-500">Complete</div>
            </div>

            {/* Confidence Score */}
            <div className="text-right">
              <div className="text-sm text-gray-500">Confidence Score</div>
              <div className="text-2xl font-bold text-yellow-600">{overallConfidence}%</div>
              <div className="text-xs text-gray-500">Quality</div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <ChatbotButton releaseId={release.id} />
              
              {/* PDF Download Button */}
              <button
                onClick={onPdfDownload}
                disabled={isPdfGenerating}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors shadow-sm hover:shadow-md disabled:cursor-not-allowed"
                title="Download PDF Report"
              >
                {isPdfGenerating ? (
                  <>
                    <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="text-sm font-medium">Generating...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span className="text-sm font-medium">PDF</span>
                  </>
                )}
              </button>

              {/* Email Button */}
              <button
                onClick={onEmailSend}
                disabled={isEmailProcessing}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg transition-colors shadow-sm hover:shadow-md disabled:cursor-not-allowed"
                title="Send Email Summary"
              >
                {isEmailProcessing ? (
                  <>
                    <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="text-sm font-medium">Sending...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm font-medium">Email</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReleaseHeader; 