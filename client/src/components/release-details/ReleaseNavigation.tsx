import React from 'react';
import trackingIcon from '../../assets/images/tracking.png';
import metricsIcon from '../../assets/images/metrics.png';
import overviewIcon from '../../assets/images/overview.png';

interface ReleaseNavigationProps {
  activeTab: 'overview' | 'signoffs' | 'metrics';
  onTabChange: (tab: 'overview' | 'signoffs' | 'metrics') => void;
}

const ReleaseNavigation: React.FC<ReleaseNavigationProps> = ({
  activeTab,
  onTabChange
}) => {
  const tabs = [
    { 
      id: 'overview' as const, 
      label: 'Overview', 
      icon: <img src={overviewIcon} alt="Overview" className="w-5 h-5 inline-block align-middle" />
    },
    { 
      id: 'metrics' as const, 
      label: 'Release Metrics', 
      icon: <img src={metricsIcon} alt="Release Metrics" className="w-5 h-5 inline-block align-middle" />
    },
    { 
      id: 'signoffs' as const, 
      label: 'Signoff Tracker', 
      icon: <img src={trackingIcon} alt="Signoff Tracker" className="w-5 h-5 inline-block align-middle" />
    }
  ];

  return (
    <div className="bg-white border-b border-gray-200 px-6">
      <nav className="flex space-x-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`group flex items-center space-x-2 py-3 px-4 text-sm font-medium rounded-t-lg border-b-2 transition-all duration-200 ${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <span className="text-base">{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
};

export default ReleaseNavigation; 