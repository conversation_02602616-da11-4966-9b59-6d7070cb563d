import React from 'react';
import type { SignoffStatus, SignoffStage } from '../../../services/signoffs';

interface ApprovalWorkflowCardProps {
  signoffStatus: SignoffStatus | null;
  onViewSignoffs: () => void;
}

const ApprovalWorkflowCard: React.FC<ApprovalWorkflowCardProps> = ({
  signoffStatus,
  onViewSignoffs
}) => {
  return (
    <div className="group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300">
      {/* Sophisticated Header */}
      <div className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-200 opacity-20 rounded-full -translate-y-16 translate-x-16"></div>
        
        <div className="relative">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-1">Approval Workflow</h3>
                <p className="text-blue-600 font-medium">
                  {signoffStatus ? 
                    `${signoffStatus.stages.filter(stage => stage.status === 'completed').length} of ${signoffStatus.stages.length} stages completed` :
                    'Loading...'
                  }
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-blue-600">{signoffStatus?.overallProgress || 0}%</div>
              <div className="text-blue-500 text-sm font-medium">Progress</div>
            </div>
          </div>
          
          <button 
            onClick={onViewSignoffs}
            className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-6 rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md hover:shadow-lg font-medium group-hover:scale-[1.01]"
          >
            <span className="flex items-center justify-center space-x-2">
              <span>View Detailed Signoff Tracker</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </span>
          </button>
        </div>
      </div>
      
      {/* Enhanced Content */}
      <div className="p-6 pb-4">
        <div className="space-y-6">
          {/* Stage Progress Cards */}
          {signoffStatus ? (
            <div className="grid grid-cols-1 gap-4">
              {signoffStatus.stages.map((stage: SignoffStage) => {
                const approvedCount = stage.currentApprovals.filter(a => a.status === 'approved').length;
                
                return (
                  <div
                    key={stage.id}
                    className={`bg-white rounded-lg p-4 border transition-all shadow-sm hover:shadow-md ${
                      stage.status === 'completed' ? 'border-emerald-300 bg-emerald-50' :
                      stage.status === 'in_progress' ? 'border-amber-300 bg-amber-50' :
                      stage.isAvailable ? 'border-blue-200 hover:border-blue-300' : 'border-gray-200 opacity-60'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          stage.status === 'completed' ? 'bg-emerald-100 text-emerald-600' :
                          stage.status === 'in_progress' ? 'bg-amber-100 text-amber-600' :
                          'bg-gray-100 text-gray-500'
                        }`}>
                          {stage.status === 'completed' ? '✅' :
                           stage.status === 'in_progress' ? '⏳' : '⏸️'}
                        </div>
                        <h4 className="font-semibold text-gray-900">{stage.name}</h4>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-bold ${
                          stage.status === 'completed' ? 'text-emerald-600' :
                          stage.status === 'in_progress' ? 'text-amber-600' : 'text-gray-500'
                        }`}>
                          {stage.progress}%
                        </div>
                        <div className="text-xs text-gray-500">
                          {stage.id === 'functional_qa' ? (
                            `${approvedCount}/${stage.requiredApprovers.length} teams`
                          ) : (
                            `${approvedCount}/${stage.minRequiredApprovals} approvals`
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Detailed Stage Info */}
                    <div className="space-y-3 mb-4">
                      {/* Sub-team Details for Functional QA */}
                      {stage.id === 'functional_qa' && (
                        <div className="space-y-2">
                          <div className="flex flex-wrap gap-1">
                            {stage.requiredApprovers.map((approver) => {
                              const isApproved = stage.currentApprovals.find(a => a.subTeam === approver.subTeam && a.status === 'approved');
                              return (
                                <span
                                  key={approver.subTeam || `approver-${approver.email}`}
                                  className={`px-2 py-1 rounded text-xs font-medium ${
                                    isApproved 
                                      ? 'bg-emerald-100 text-emerald-700 border border-emerald-200' 
                                      : 'bg-gray-100 text-gray-600 border border-gray-200'
                                  }`}
                                >
                                  {approver.subTeam ? 
                                    `${approver.subTeam.charAt(0).toUpperCase()}${approver.subTeam.slice(1)}` :
                                    'Team'
                                  }
                                  {isApproved ? ' ✓' : ''}
                                </span>
                              );
                            })}
                          </div>
                        </div>
                      )}
                      
                      {/* Approver Details for other stages */}
                      {stage.id !== 'functional_qa' && (
                        <div className="space-y-2">
                          <div className="text-xs text-gray-600">Required approvers:</div>
                          <div className="space-y-1">
                            {stage.requiredApprovers.slice(0, 2).map((approver, idx) => {
                              const isApproved = stage.currentApprovals.find(a => a.approverEmail === approver.email && a.status === 'approved');
                              return (
                                <div key={idx} className="flex items-center justify-between text-xs">
                                  <span className="text-gray-600 truncate">{approver.email}</span>
                                  <span className={`ml-2 ${isApproved ? 'text-emerald-600' : 'text-gray-400'}`}>
                                    {isApproved ? '✓' : '⏳'}
                                  </span>
                                </div>
                              );
                            })}
                            {stage.requiredApprovers.length > 2 && (
                              <div className="text-xs text-gray-500">
                                +{stage.requiredApprovers.length - 2} more approvers
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            stage.status === 'completed' ? 'bg-gradient-to-r from-emerald-400 to-green-500' :
                            stage.status === 'in_progress' ? 'bg-gradient-to-r from-amber-400 to-orange-500' : 'bg-gray-300'
                          }`}
                          style={{ width: `${stage.progress}%` }}
                        ></div>
                      </div>
                      
                      {/* Status Message */}
                      <div className="text-xs text-gray-600">
                        {stage.status === 'completed' ? 'All approvals completed' :
                         stage.status === 'in_progress' ? 'Awaiting remaining approvals' :
                         stage.isAvailable ? 'Ready for approval' : 'Waiting for previous stage'}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">Loading stage progress...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApprovalWorkflowCard; 