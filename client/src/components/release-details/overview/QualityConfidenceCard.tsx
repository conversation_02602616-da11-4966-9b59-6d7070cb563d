import React from 'react';
import {
  ChartBarIcon,
  BugAntIcon,
  ShieldCheckIcon,
  BeakerIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
} from '@heroicons/react/24/outline';
import type { ConfidenceMetric } from '../../../types/metrics';

interface QualityConfidenceCardProps {
  confidenceMetrics: ConfidenceMetric[];
  overallConfidence: number;
  isLoading?: boolean;
  onViewMetrics: () => void;
  onMetricClick?: (metricId: string) => void;
}

const QualityConfidenceCard: React.FC<QualityConfidenceCardProps> = ({
  confidenceMetrics,
  overallConfidence,
  isLoading = false,
  onViewMetrics,
  onMetricClick,
}) => {
  // State to track scroll position for showing scroll up/down indicators
  const [showScrollUp, setShowScrollUp] = React.useState(false);
  const [showScrollDown, setShowScrollDown] = React.useState(confidenceMetrics.length > 4);

  // Handle scroll events to show/hide scroll indicators
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    // Show scroll up if scrolled down more than 10px
    setShowScrollUp(scrollTop > 10);

    // Show scroll down if not at bottom and content is taller than container
    setShowScrollDown(scrollTop < scrollHeight - clientHeight - 10);
  };

  // Check if we need scroll indicators on mount and when metrics change
  React.useEffect(() => {
    const container = document.getElementById('quality-metrics-container');
    if (container) {
      const needsScroll = container.scrollHeight > container.clientHeight;
      setShowScrollDown(needsScroll);
    }
  }, [confidenceMetrics]);

  // Scroll down function
  const scrollDown = () => {
    const container = document.getElementById('quality-metrics-container');
    if (container) {
      // Scroll to the bottom of the container
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Scroll up function
  const scrollUp = () => {
    const container = document.getElementById('quality-metrics-container');
    if (container) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };

  // Helper function to get the appropriate icon for each metric
  const getMetricIcon = (metricName: string) => {
    const iconClass = 'w-5 h-5';

    switch (metricName) {
      case 'Code Quality':
        return <BugAntIcon className={iconClass} />;
      case 'Code Coverage':
        return <ShieldCheckIcon className={iconClass} />;
      case 'Test Results':
        return <BeakerIcon className={iconClass} />;
      case 'Build Stability':
        return <WrenchScrewdriverIcon className={iconClass} />;
      case 'Change Requests':
        return <DocumentTextIcon className={iconClass} />;
      case 'Change Log':
        return <ClipboardDocumentListIcon className={iconClass} />;
      default:
        return <ChartBarIcon className={iconClass} />;
    }
  };

  // Helper function to get metric tab ID from metric name
  const getMetricTabId = (metricName: string): string => {
    switch (metricName) {
      case 'Code Quality':
        return 'code-quality';
      case 'Code Coverage':
        return 'code-coverage';
      case 'Test Results':
        return 'test-results';
      case 'Build Stability':
        return 'build-stability';
      case 'Change Requests':
        return 'change-requests';
      case 'Change Log':
        return 'changelog';
      default:
        return 'code-quality';
    }
  };

  // Handle metric card click
  const handleMetricClick = (metric: ConfidenceMetric) => {
    if (onMetricClick) {
      const tabId = getMetricTabId(metric.name);
      onMetricClick(tabId);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
                <div>
                  <div className="h-6 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-8 bg-gray-200 rounded w-16 mb-1 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
              </div>
            </div>
            <div className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </div>
        <div className="p-6 flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 pr-2 min-h-0">
            <div className="space-y-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-gray-50 rounded-lg p-4 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-gray-200 rounded w-full"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show empty state if no metrics
  if (confidenceMetrics.length === 0) {
    return (
      <div className="group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="relative bg-gradient-to-br from-gray-50 via-gray-50 to-gray-100 p-6">
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                  <ChartBarIcon className="w-6 h-6 text-gray-500" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-600 mb-1">Quality Confidence</h3>
                  <p className="text-gray-500 font-medium">No metrics available</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-gray-400">--</div>
                <div className="text-gray-400 text-sm font-medium">Score</div>
              </div>
            </div>

            <button
              onClick={onViewMetrics}
              className="w-full bg-gradient-to-r from-gray-400 to-gray-500 text-white py-3 px-6 rounded-lg hover:from-gray-500 hover:to-gray-600 transition-all duration-300 shadow-md hover:shadow-lg font-medium"
            >
              <span className="flex items-center justify-center space-x-2">
                <span>Set Up Metrics</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
        <div className="p-6">
          <div className="text-center text-gray-500">
            <p className="text-sm">
              Metrics will appear here once your team starts pushing code quality, code coverage,
              and test result data.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl flex flex-col h-full">
      {/* Header with Enhanced Styling and Button */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Quality Confidence</h2>
              <p className="text-sm text-gray-600">Overall release readiness assessment</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-blue-600">{overallConfidence}%</div>
            <div className="text-sm text-gray-500">Confidence Score</div>
          </div>
        </div>

        {/* View Detailed Metrics Button - moved inside header */}
        <button
          onClick={onViewMetrics}
          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-6 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-md hover:shadow-lg font-medium"
        >
          <span className="flex items-center justify-center space-x-2">
            <span>View Detailed Metrics Analysis</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              />
            </svg>
          </span>
        </button>
      </div>

      {/* Enhanced Content with Full Available Height and Scroll */}
      <div className="p-6 flex-1 flex flex-col min-h-0">
        <div className="space-y-6 flex-1 flex flex-col min-h-0">
          {/* Tool Metrics Cards with Fixed Height to Show 4 Metrics - show 4 metrics initially */}
          <div className="flex-1 relative overflow-hidden h-40">
            {/* Top fade indicator */}
            <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white to-transparent z-10 pointer-events-none"></div>

            {/* Scroll up button - show when scrolled down */}
            {showScrollUp && (
              <button
                onClick={scrollUp}
                className="absolute top-2 right-4 z-20 flex items-center space-x-2 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full px-3 py-2 shadow-sm border border-gray-200 hover:shadow-md cursor-pointer"
              >
                <div className="flex items-center space-x-1">
                  <svg
                    className="w-3 h-3 animate-bounce"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                  <span className="font-medium">scroll up</span>
                </div>
              </button>
            )}

            {/* Scrollable content with fixed height for exactly 4 metrics */}
            <div
              id="quality-metrics-container"
              className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 pr-2 scroll-smooth"
              onScroll={handleScroll}
            >
              <div className="grid grid-cols-1 gap-2 pt-1 pb-1">
                {confidenceMetrics.map((metric, _index) => (
                  <div
                    key={metric.id}
                    onClick={() => handleMetricClick(metric)}
                    title={`Click to view detailed ${metric.name} analysis`}
                    className={`bg-white rounded-lg p-2.5 border transition-all duration-200 shadow-sm hover:shadow-md overflow-hidden cursor-pointer ${
                      metric.score >= 90
                        ? 'border-emerald-300 bg-emerald-50 hover:bg-emerald-100'
                        : metric.score >= 75
                          ? 'border-blue-200 hover:border-blue-300 hover:bg-blue-50'
                          : metric.score >= 60
                            ? 'border-amber-300 bg-amber-50 hover:bg-amber-100'
                            : 'border-red-300 bg-red-50 hover:bg-red-100'
                    } hover:scale-[1.0005] hover:border-opacity-80`}
                  >
                    <div className="flex items-center justify-between mb-1.5">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200 ${
                            metric.score >= 90
                              ? 'bg-emerald-100 text-emerald-600'
                              : metric.score >= 75
                                ? 'bg-blue-100 text-blue-600'
                                : metric.score >= 60
                                  ? 'bg-amber-100 text-amber-600'
                                  : 'bg-red-100 text-red-600'
                          }`}
                        >
                          {getMetricIcon(metric.name)}
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 text-xs transition-all duration-200">
                            {metric.name}
                          </h4>
                          <p className="text-xs text-gray-500">{metric.label}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div
                          className={`text-lg font-bold transition-all duration-200 ${
                            metric.score >= 90
                              ? 'text-emerald-600'
                              : metric.score >= 75
                                ? 'text-blue-600'
                                : metric.score >= 60
                                  ? 'text-amber-600'
                                  : 'text-red-600'
                          }`}
                        >
                          {metric.score}%
                        </div>
                        <div className="text-xs text-gray-500">
                          {metric.name === 'Code Quality'
                            ? '20% weight'
                            : metric.name === 'Code Coverage'
                              ? '20% weight'
                              : metric.name === 'Test Results'
                                ? '20% weight'
                                : metric.name === 'Build Stability'
                                  ? '20% weight'
                                  : metric.name === 'Change Requests'
                                    ? '10% weight'
                                    : metric.name === 'Change Log'
                                      ? '10% weight'
                                      : '10% weight'}
                        </div>
                      </div>
                    </div>

                    <div>
                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-1 mb-1">
                        <div
                          className={`h-1 rounded-full transition-all duration-300 ${
                            metric.score >= 90
                              ? 'bg-gradient-to-r from-emerald-400 to-green-500'
                              : metric.score >= 75
                                ? 'bg-gradient-to-r from-blue-400 to-blue-600'
                                : metric.score >= 60
                                  ? 'bg-gradient-to-r from-amber-400 to-orange-500'
                                  : 'bg-gradient-to-r from-red-400 to-red-600'
                          }`}
                          style={{ width: `${metric.score}%` }}
                        ></div>
                      </div>

                      {/* Status Message */}
                      <div className="text-xs text-gray-600">
                        {metric.score >= 90
                          ? 'Excellent performance'
                          : metric.score >= 75
                            ? 'Good performance'
                            : metric.score >= 60
                              ? 'Needs improvement'
                              : 'Critical issues detected'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Bottom fade indicator */}
            <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white to-transparent z-10 pointer-events-none"></div>

            {/* Scroll down button - show when there are more metrics below */}
            {showScrollDown && (
              <button
                onClick={scrollDown}
                className="absolute bottom-2 right-4 z-20 flex items-center space-x-2 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full px-3 py-2 shadow-sm border border-gray-200 hover:shadow-md cursor-pointer"
              >
                <div className="flex items-center space-x-1">
                  <svg
                    className="w-3 h-3 animate-bounce"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                  <span className="font-medium">scroll down</span>
                </div>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QualityConfidenceCard;
