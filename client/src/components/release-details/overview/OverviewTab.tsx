import React from 'react';
import ApprovalWorkflowCard from './ApprovalWorkflowCard';
import QualityConfidenceCard from './QualityConfidenceCard';
import type { SignoffStatus } from '../../../services/signoffs';
import type { ConfidenceMetric } from '../../../types/metrics';

interface OverviewTabProps {
  signoffStatus: SignoffStatus | null;
  confidenceMetrics: ConfidenceMetric[];
  overallConfidence: number;
  metricsLoading?: boolean;
  onViewSignoffs: () => void;
  onViewMetrics: () => void;
  onMetricClick?: (metricId: string) => void;
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  signoffStatus,
  confidenceMetrics,
  overallConfidence,
  metricsLoading = false,
  onViewSignoffs,
  onViewMetrics,
  onMetricClick
}) => {
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-stretch">
        {/* Modern Quality Confidence Card */}
        <QualityConfidenceCard 
          confidenceMetrics={confidenceMetrics}
          overallConfidence={overallConfidence}
          isLoading={metricsLoading}
          onViewMetrics={onViewMetrics}
          onMetricClick={onMetricClick}
        />

        {/* Modern Approval Workflow Card */}
        <ApprovalWorkflowCard 
          signoffStatus={signoffStatus}
          onViewSignoffs={onViewSignoffs}
        />
      </div>
    </div>
  );
};

export default OverviewTab; 