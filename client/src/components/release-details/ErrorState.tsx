import React from 'react';

interface ErrorStateProps {
  error?: string | null;
  onGoBack: () => void;
}

const ErrorState: React.FC<ErrorStateProps> = ({ error, onGoBack }) => {
  return (
    <div className="h-full bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Release Not Found</h2>
        <p className="text-gray-500 mb-4">{error || 'The requested release could not be found.'}</p>
        <button
          onClick={onGoBack}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Back to Releases
        </button>
      </div>
    </div>
  );
};

export default ErrorState; 