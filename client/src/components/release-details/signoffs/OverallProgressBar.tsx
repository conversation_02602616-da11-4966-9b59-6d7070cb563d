import React from 'react';
import type { SignoffStatus } from '../../../services/signoffs';

interface OverallProgressBarProps {
  signoffStatus: SignoffStatus;
}

const OverallProgressBar: React.FC<OverallProgressBarProps> = ({ signoffStatus }) => {
  return (
    <div className="relative bg-white rounded-2xl border border-gray-200/60 p-6 transition-all duration-300 transform hover:-translate-y-0.5 hover:scale-[1.01] group" 
         style={{ 
           boxShadow: '0 8px 25px -5px rgba(0, 0, 0, 0.08), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'
         }}>
      
      {/* Subtle top accent line */}
      <div className="absolute top-0 left-6 right-6 h-0.5 bg-gradient-to-r from-transparent via-emerald-400 to-transparent opacity-60 rounded-full"></div>
      
      {/* Main content */}
      <div className="flex items-center justify-between mb-5">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1 tracking-tight">Release Signoff Progress</h3>
          <p className="text-sm text-gray-500 font-medium">Overall completion across all approval stages</p>
        </div>
        <div className="text-right ml-6">
          <div className="relative">
            <div className="text-3xl font-bold text-emerald-600 mb-1 tracking-tight"
                 style={{ 
                   textShadow: '0 2px 4px rgba(16, 185, 129, 0.15)',
                   filter: 'drop-shadow(0 1px 2px rgba(16, 185, 129, 0.1))'
                 }}>
              {signoffStatus.overallProgress}%
            </div>
            <div className="text-xs uppercase tracking-wider font-semibold text-emerald-500 opacity-90">Complete</div>
          </div>
        </div>
      </div>
      
      {/* Enhanced progress bar */}
      <div className="relative mb-4">
        <div className="w-full bg-gradient-to-r from-gray-100 to-gray-200 rounded-full h-3 relative overflow-hidden"
             style={{ 
               boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.06), inset 0 1px 2px rgba(0, 0, 0, 0.1)' 
             }}>
          
          {/* Progress fill */}
          <div 
            className="absolute top-0 left-0 h-full rounded-full transition-all duration-700 ease-out relative overflow-hidden"
            style={{ 
              width: `${signoffStatus.overallProgress}%`,
              background: 'linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%)',
              boxShadow: '0 1px 3px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)'
            }}
          >
            {/* Animated shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-25 animate-pulse rounded-full"
                 style={{ animationDuration: '2s' }}></div>
            
            {/* Subtle highlight on top */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-40 rounded-full"></div>
          </div>
          
          {/* Progress indicator dot */}
          {signoffStatus.overallProgress > 0 && (
            <div 
              className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-emerald-500 transition-all duration-700 ease-out"
              style={{ 
                left: `calc(${signoffStatus.overallProgress}% - 8px)`,
                boxShadow: '0 2px 6px rgba(16, 185, 129, 0.3), 0 1px 3px rgba(0, 0, 0, 0.1)'
              }}
            >
              <div className="absolute inset-1 bg-emerald-500 rounded-full"></div>
            </div>
          )}
        </div>
      </div>
      
      {/* Progress markers */}
      <div className="flex justify-between items-center text-xs">
        <div className="flex items-center space-x-1.5 text-gray-500">
          <div className="w-2 h-2 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full shadow-sm"></div>
          <span className="font-medium">Started</span>
        </div>
        <div className="flex items-center space-x-1.5 text-gray-500">
          <div className="w-2 h-2 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full shadow-sm"></div>
          <span className="font-medium">In Progress</span>
        </div>
        <div className="flex items-center space-x-1.5 text-gray-500">
          <div className="w-2 h-2 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full shadow-sm"></div>
          <span className="font-medium">Ready for Release</span>
        </div>
      </div>
      
      {/* Subtle hover glow effect - removed background gradients */}
      <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
           style={{
             boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)'
           }}></div>
    </div>
  );
};

export default OverallProgressBar; 