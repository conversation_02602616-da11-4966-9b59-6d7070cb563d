import React, { useEffect } from 'react';
import type { SignoffStatus, SignoffStage } from '../../../services/signoffs';

interface StageDetailsGridProps {
  signoffStatus: SignoffStatus;
  isAuthenticated: boolean;
  isSubmitting: boolean;
  authLoading: boolean;
  approvalFormData: {
    buildSha: string;
    buildNumber: string;
    knownIssues: string;
  };
  setApprovalFormData: React.Dispatch<React.SetStateAction<{
    buildSha: string;
    buildNumber: string;
    knownIssues: string;
  }>>;
  selectedSubTeam: string;
  setSelectedSubTeam: React.Dispatch<React.SetStateAction<string>>;
  expandedApprovals: Set<string>;
  setExpandedApprovals: React.Dispatch<React.SetStateAction<Set<string>>>;
  handleSubmitApproval: () => Promise<void>;
  isUserAuthorizedForSubTeam: (subTeam: string) => boolean;
}

const StageDetailsGrid: React.FC<StageDetailsGridProps> = ({
  signoffStatus,
  isAuthenticated,
  isSubmitting,
  authLoading,
  approvalFormData,
  setApprovalFormData,
  selectedSubTeam,
  setSelectedSubTeam,
  expandedApprovals,
  setExpandedApprovals,
  handleSubmitApproval,
  isUserAuthorizedForSubTeam
}) => {
  
  // Handle scroll shadow visibility
  useEffect(() => {
    const handleScrollShadow = (stageId: string) => {
      const container = document.getElementById(`scroll-container-${stageId}`);
      const shadow = document.getElementById(`scroll-shadow-${stageId}`);
      
      if (container && shadow) {
        const { scrollTop, scrollHeight, clientHeight } = container;
        const isScrollable = scrollHeight > clientHeight;
        const isNearBottom = scrollTop + clientHeight >= scrollHeight - 10;
        
        // Show shadow if content is scrollable and not at bottom
        shadow.style.opacity = (isScrollable && !isNearBottom) ? '1' : '0';
      }
    };

    // Set up scroll listeners for each stage
    signoffStatus.stages.forEach(stage => {
      const container = document.getElementById(`scroll-container-${stage.id}`);
      if (container) {
        const listener = () => handleScrollShadow(stage.id);
        container.addEventListener('scroll', listener);
        // Initial check
        setTimeout(() => handleScrollShadow(stage.id), 100);
        
        return () => container.removeEventListener('scroll', listener);
      }
    });
  }, [signoffStatus.stages, expandedApprovals]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {signoffStatus.stages.map((stage: SignoffStage) => (
        <div key={stage.id} className={`relative bg-white rounded-2xl p-5 transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.01] group border flex flex-col h-[700px] ${
          stage.status === 'completed' ? 'border-emerald-200/60' :
          stage.status === 'in_progress' ? 'border-amber-200/60' :
          stage.isAvailable ? 'border-blue-200/60' : 
          'border-gray-200/60 opacity-75'
        }`}
        style={{
          boxShadow: stage.status === 'completed' ? '0 8px 25px -5px rgba(16, 185, 129, 0.15), 0 8px 10px -6px rgba(16, 185, 129, 0.08)' :
                    stage.status === 'in_progress' ? '0 8px 25px -5px rgba(245, 158, 11, 0.15), 0 8px 10px -6px rgba(245, 158, 11, 0.08)' :
                    stage.isAvailable ? '0 8px 25px -5px rgba(59, 130, 246, 0.12), 0 8px 10px -6px rgba(59, 130, 246, 0.06)' :
                    '0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04)'
        }}>
          {/* Subtle top accent line */}
          <div className={`absolute top-0 left-5 right-5 h-0.5 rounded-full opacity-60 ${
            stage.status === 'completed' ? 'bg-gradient-to-r from-transparent via-emerald-400 to-transparent' :
            stage.status === 'in_progress' ? 'bg-gradient-to-r from-transparent via-amber-400 to-transparent' :
            stage.isAvailable ? 'bg-gradient-to-r from-transparent via-blue-400 to-transparent' :
            'bg-gradient-to-r from-transparent via-gray-400 to-transparent'
          }`}></div>

          <div className="flex items-center justify-between mb-4 flex-shrink-0">
            <h3 className="text-lg font-semibold text-gray-900 drop-shadow-sm tracking-tight">{stage.name}</h3>
            <span className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 transform hover:scale-105 border ${
              stage.status === 'completed' ? 'bg-white text-emerald-700 border-emerald-200/60 shadow-sm' :
              stage.status === 'in_progress' ? 'bg-white text-amber-700 border-amber-200/60 shadow-sm' :
              'bg-white text-gray-600 border-gray-200/60 shadow-sm'
            }`}
            style={{
              boxShadow: stage.status === 'completed' ? '0 2px 4px rgba(16, 185, 129, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6)' :
                        stage.status === 'in_progress' ? '0 2px 4px rgba(245, 158, 11, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6)' :
                        '0 2px 4px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
            }}>
              {stage.currentApprovals.filter(a => a.status === 'approved').length} / {stage.minRequiredApprovals} approved
            </span>
          </div>
          
          {/* Scrollable Content Area - Increased height */}
          <div 
            id={`scroll-container-${stage.id}`}
            className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 relative"
          >
            {/* Scroll shadow at bottom when overflowing */}
            <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white via-white/80 to-transparent pointer-events-none z-10 opacity-0 transition-opacity duration-300" id={`scroll-shadow-${stage.id}`}></div>
            
            <div className="space-y-4">
              {/* Functional QA - Enhanced Design */}
              {stage.id === 'functional_qa' && (
                <div className="space-y-4">
                  {/* Pending Sub-teams Badges - Only show if there are pending sub-teams */}
                  {stage.requiredApprovers.some(approver => approver.subTeam && !stage.currentApprovals.find(a => a.subTeam === approver.subTeam && a.status === 'approved')) && (
                    <div className="space-y-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Pending Sub-teams</div>
                      <div 
                        className="overflow-x-auto scrollbar-thin border border-gray-100 rounded-lg p-2 bg-white"
                      >
                        <div className="flex space-x-2 min-w-max">
                      {stage.requiredApprovers
                        .filter(approver => approver.subTeam && !stage.currentApprovals.find(a => a.subTeam === approver.subTeam && a.status === 'approved'))
                        .map((approver) => (
                          <button
                            key={approver.subTeam || `approver-${approver.email}`}
                            onClick={() => setSelectedSubTeam(approver.subTeam!)}
                                className={`flex-shrink-0 px-3 py-1.5 rounded-xl text-sm font-medium transition-all duration-200 cursor-pointer transform hover:scale-[1.02] hover:-translate-y-0.5 ${
                              selectedSubTeam === approver.subTeam
                                    ? 'bg-white text-amber-800 border border-amber-200/60 shadow-md'
                                    : 'bg-white text-amber-700 hover:bg-white border border-amber-200/60 shadow-sm hover:shadow-md'
                            }`}
                                style={{
                                  boxShadow: selectedSubTeam === approver.subTeam 
                                    ? '0 4px 6px -1px rgba(245, 158, 11, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
                                    : '0 2px 4px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
                                }}
                          >
                                <div className="flex items-center space-x-2">
                                  <svg className="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  <span>
                            {approver.subTeam ? 
                              `${approver.subTeam.charAt(0).toUpperCase()}${approver.subTeam.slice(1)}` :
                              'Team'
                            }
                                  </span>
                                </div>
                          </button>
                        ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Enhanced Approval Form - Slightly reduced padding */}
                  {(stage.isAvailable || stage.id === 'functional_qa') && stage.requiredApprovers.some(approver => approver.subTeam && !stage.currentApprovals.find(a => a.subTeam === approver.subTeam && a.status === 'approved')) && (
                    <div className="bg-white rounded-xl p-3 border border-gray-200/50 transition-all duration-200 hover:bg-white hover:border-amber-200/60 hover:shadow-lg"
                         style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <h4 className="font-medium text-gray-900 mb-3 text-sm drop-shadow-sm">Submit Sub-team Approval</h4>
                      {!isAuthenticated && (
                        <div className="mb-3 p-2 bg-white border border-amber-200/60 rounded-xl">
                          <div className="flex items-center space-x-2">
                            <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            <span className="text-sm text-amber-800">Authentication required to submit approvals</span>
                          </div>
                        </div>
                      )}
                      <div className="space-y-2">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Select Sub-team</label>
                          <select 
                            className="w-full px-2 py-1.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white shadow-sm"
                            value={selectedSubTeam}
                            onChange={(e) => setSelectedSubTeam(e.target.value)}
                          >
                            <option value="">Choose a sub-team...</option>
                            {stage.requiredApprovers
                              .filter(approver => approver.subTeam && !stage.currentApprovals.find(a => a.subTeam === approver.subTeam && a.status === 'approved'))
                              .map((approver) => (
                                <option key={approver.subTeam} value={approver.subTeam}>
                                  {approver.subTeam!.charAt(0).toUpperCase() + approver.subTeam!.slice(1)} Team
                                </option>
                              ))}
                          </select>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Test Suite / Execution Link</label>
                          <input
                            type="text"
                            placeholder="Enter test suite or execution link..."
                            value={approvalFormData.buildSha}
                            onChange={(e) => setApprovalFormData(prev => ({ ...prev, buildSha: e.target.value }))}
                            className="w-full px-2 py-1.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm shadow-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Build Link</label>
                          <input
                            type="text"
                            placeholder="Build link..."
                            value={approvalFormData.buildNumber}
                            onChange={(e) => setApprovalFormData(prev => ({ ...prev, buildNumber: e.target.value }))}
                            className="w-full px-2 py-1.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm shadow-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Known Issues</label>
                          <textarea
                            placeholder="Known issues..."
                            rows={2}
                            value={approvalFormData.knownIssues}
                            onChange={(e) => setApprovalFormData(prev => ({ ...prev, knownIssues: e.target.value }))}
                            className="w-full px-2 py-1.5 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none shadow-sm"
                          />
                        </div>
                        <button
                          onClick={handleSubmitApproval}
                          className={`w-full px-4 py-2 rounded-xl transition-all duration-200 font-medium text-sm flex items-center justify-center space-x-2 transform hover:scale-[1.02] hover:-translate-y-0.5 ${
                            !isAuthenticated 
                              ? 'bg-white text-gray-500 cursor-not-allowed border border-gray-200/60'
                              : isSubmitting || authLoading
                              ? 'bg-white text-gray-500 cursor-not-allowed border border-gray-200/60'
                              : !selectedSubTeam
                              ? 'bg-white text-gray-500 cursor-not-allowed border border-gray-200/60'
                              : (selectedSubTeam && !isUserAuthorizedForSubTeam(selectedSubTeam))
                              ? 'bg-white text-gray-500 cursor-not-allowed border border-gray-200/60'
                              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl border border-blue-600'
                          }`}
                          style={{
                            boxShadow: (!isAuthenticated || isSubmitting || authLoading || !selectedSubTeam || (selectedSubTeam && !isUserAuthorizedForSubTeam(selectedSubTeam)))
                              ? '0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
                              : '0 4px 6px -1px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                          }}
                          disabled={isSubmitting || authLoading || !isAuthenticated || !selectedSubTeam || (selectedSubTeam ? !isUserAuthorizedForSubTeam(selectedSubTeam) : false)}
                        >
                          {!isAuthenticated ? (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                              <span>Sign In Required</span>
                            </>
                          ) : isSubmitting ? (
                            <>
                              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span>Submitting...</span>
                            </>
                          ) : authLoading ? (
                            <>
                              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span>Loading...</span>
                            </>
                          ) : !selectedSubTeam ? (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span>Select a Sub-team</span>
                            </>
                          ) : (selectedSubTeam && !isUserAuthorizedForSubTeam(selectedSubTeam)) ? (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                              <span>Not Authorized</span>
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span>Submit Approval</span>
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* Scroll Separator - More visible and attractive */}
                  {stage.currentApprovals.filter(a => a.status === 'approved').length > 0 && (
                    <div className="relative my-6">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t-2 border-dashed border-gray-300"></div>
                      </div>
                      <div className="relative flex justify-center">
                        <button
                          onClick={() => {
                            const completedSection = document.getElementById(`completed-approvals-${stage.id}`);
                            if (completedSection) {
                              completedSection.scrollIntoView({ 
                                behavior: 'smooth', 
                                block: 'start' 
                              });
                            }
                          }}
                          className="px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 text-sm text-blue-700 rounded-xl border-2 border-blue-200 flex items-center space-x-2 hover:from-blue-100 hover:to-purple-100 hover:text-blue-800 hover:border-blue-300 transition-all duration-200 group shadow-lg hover:shadow-xl transform hover:scale-105"
                        >
                          <svg className="w-4 h-4 animate-pulse group-hover:animate-bounce text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                          <span className="font-medium">View {stage.currentApprovals.filter(a => a.status === 'approved').length} completed approvals</span>
                          <svg className="w-4 h-4 animate-pulse group-hover:animate-bounce text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* Completed Approvals Section */}
                  {stage.currentApprovals.filter(a => a.status === 'approved').length > 0 && (
                    <div id={`completed-approvals-${stage.id}`} className="space-y-3">
                      {/* Enhanced Header with better scroll indicator */}
                      
                      
                      {stage.currentApprovals.filter(a => a.status === 'approved').map((approval) => {
                        const isExpanded = expandedApprovals.has(`${stage.id}-${approval.id}`);
                        
                        return (
                          <div key={approval.id} className="bg-white/80 backdrop-blur-sm border border-emerald-200/60 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-200 transform hover:-translate-y-0.5"
                               style={{ boxShadow: '0 4px 6px -1px rgba(16, 185, 129, 0.1), 0 2px 4px -1px rgba(16, 185, 129, 0.06)' }}>
                            <div className="p-2">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                  </div>
                                  <div className="flex-1">
                                    <h4 className="font-semibold text-gray-900 text-sm">
                                      {stage.id === 'functional_qa' && approval.subTeam 
                                        ? approval.subTeam.charAt(0).toUpperCase() + approval.subTeam.slice(1)
                                        : stage.name}
                                    </h4>
                                    <div className="flex items-center space-x-2 mt-0.5">
                                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-white text-emerald-700 border border-emerald-200/60">
                                        ✓ Approved
                                      </span>
                                      <span className="text-xs text-gray-500 font-medium">
                                        {new Date(approval.approvedAt!).toLocaleDateString('en-US', {
                                          month: 'short',
                                          day: 'numeric',
                                          year: 'numeric',
                                          hour: '2-digit',
                                          minute: '2-digit',
                                          timeZoneName: 'short'
                                        })}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <button
                                  onClick={() => {
                                    const key = `${stage.id}-${approval.id}`;
                                    const newExpanded = new Set(expandedApprovals);
                                    if (isExpanded) {
                                      newExpanded.delete(key);
                                    } else {
                                      newExpanded.add(key);
                                    }
                                    setExpandedApprovals(newExpanded);
                                  }}
                                  className="p-1 text-gray-400 hover:text-gray-600 hover:bg-white rounded-full transition-colors flex-shrink-0"
                                >
                                  <svg className="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    {isExpanded ? (
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    ) : (
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    )}
                                  </svg>
                                </button>
                              </div>
                              
                              {/* Enhanced collapsed preview - More compact */}
                              {!isExpanded && (
                                <div className="mt-2 grid grid-cols-2 gap-1">
                                  <div className="flex items-center space-x-1 bg-white px-2 py-1 rounded-lg border border-gray-200/60">
                                    <span className="text-gray-600 text-xs font-medium">#</span>
                                    <span className="font-mono text-xs text-gray-700 font-medium truncate">
                                      {approval.sha && approval.sha.startsWith('http') ? (
                                        <a 
                                          href={approval.sha} 
                                          target="_blank" 
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 break-all"
                                          title={approval.sha}
                                        >
                                          {approval.sha}
                                        </a>
                                      ) : (
                                        approval.sha || 'No test suite link'
                                      )}
                                    </span>
                                  </div>
                                  <div className="flex items-center space-x-1 bg-white px-2 py-1 rounded-lg border border-blue-200/60 min-w-0">
                                    <svg className="w-3 h-3 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    {approval.buildNumber && approval.buildNumber.startsWith('http') ? (
                                      <a 
                                        href={approval.buildNumber} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="font-semibold text-xs text-blue-700 hover:text-blue-800 truncate block"
                                        title={approval.buildNumber}
                                      >
                                        Build Link
                                      </a>
                                    ) : (
                                      <span className="font-semibold text-xs text-blue-700 truncate">
                                        {approval.buildNumber || 'No build link'}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            {isExpanded && (
                              <div className="border-t border-emerald-200/60 bg-white">
                                <div className="p-2 space-y-2">
                                  {/* Enhanced Approver Info - More compact */}
                                  <div className="bg-white rounded-lg p-2 border border-gray-200/60">
                                    <div className="flex items-center justify-between">
                                      <span className="text-sm font-medium text-gray-700 flex items-center space-x-1">
                                        <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                        <span>Approver</span>
                                      </span>
                                      <span className="text-sm text-blue-600 font-medium bg-white px-2 py-0.5 rounded-lg border border-blue-200/60">
                                        {approval.approverEmail}
                                      </span>
                                    </div>
                                  </div>
                                  
                                  {/* Enhanced Timestamp - More compact */}
                                  <div className="bg-white rounded-lg p-2 border border-gray-200/60">
                                    <div className="flex items-center justify-between">
                                      <span className="text-sm font-medium text-gray-700 flex items-center space-x-1">
                                        <svg className="w-3 h-3 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span>Approved At</span>
                                      </span>
                                      <span className="text-sm text-amber-600 font-medium bg-white px-2 py-0.5 rounded-lg border border-amber-200/60">
                                        {new Date(approval.approvedAt!).toLocaleDateString('en-US', {
                                          month: 'short',
                                          day: 'numeric',
                                          year: 'numeric',
                                          hour: '2-digit',
                                          minute: '2-digit',
                                          timeZoneName: 'short'
                                        })}
                                      </span>
                                    </div>
                                  </div>
                                  
                                  {/* Enhanced SHA and Build Number - More compact grid */}
                                  <div className="grid grid-cols-2 gap-1">
                                    <div className="bg-white rounded-lg p-2 border border-gray-200/60">
                                      <div className="text-xs text-gray-600 mb-1 flex items-center space-x-1">
                                        <span className="text-gray-500 font-mono text-sm">#</span>
                                        <span>Test Suite / Execution Link</span>
                                      </div>
                                      <div className="font-mono text-xs text-gray-700 bg-white px-1 py-0.5 rounded border break-all">
                                        {approval.sha && approval.sha.startsWith('http') ? (
                                          <a 
                                            href={approval.sha} 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:text-blue-800 break-all"
                                            title={approval.sha}
                                          >
                                            {approval.sha}
                                          </a>
                                        ) : (
                                          approval.sha || 'No test suite link'
                                        )}
                                      </div>
                                    </div>
                                    
                                    <div className="bg-white rounded-lg p-2 border border-gray-200/60">
                                      <div className="text-xs text-gray-600 mb-1 flex items-center space-x-1">
                                        <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                        <span>Build Link</span>
                                      </div>
                                      <div className="bg-white px-1 py-0.5 rounded border border-blue-200/60 min-w-0">
                                        {approval.buildNumber && approval.buildNumber.startsWith('http') ? (
                                          <a 
                                            href={approval.buildNumber} 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="font-semibold text-xs text-blue-700 hover:text-blue-800 break-all block"
                                            title={approval.buildNumber}
                                          >
                                            {approval.buildNumber}
                                          </a>
                                        ) : (
                                          <div className="font-semibold text-xs text-blue-700 break-all">
                                            {approval.buildNumber || 'No build link'}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  {/* Enhanced Notes - More compact */}
                                  <div className="bg-white rounded-lg p-2 border border-gray-200/60">
                                    <div className="text-xs text-gray-600 mb-1 flex items-center space-x-1">
                                      <svg className="w-3 h-3 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                      </svg>
                                      <span>Notes</span>
                                    </div>
                                    <div className="text-xs text-gray-700 bg-white p-1 rounded border border-orange-200 break-words">
                                      {approval.notes && approval.notes.startsWith('http') ? (
                                        <a 
                                          href={approval.notes} 
                                          target="_blank" 
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 break-all"
                                          title={approval.notes}
                                        >
                                          {approval.notes}
                                        </a>
                                      ) : (
                                        approval.notes || 'No issues reported'
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}

              {/* Enhanced Non-QA stages - E2E QA and Engineering Leadership - More compact */}
              {stage.id !== 'functional_qa' && (
                <div className="space-y-3">
                  {/* Stage status message - More compact */}
                  {!stage.isAvailable ? (
                    <div className="flex items-center justify-center text-amber-600 bg-white rounded-xl p-2 border border-amber-200/60"
                         style={{ boxShadow: '0 2px 4px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <svg className="w-4 h-4 mr-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <span className="text-sm font-medium">Locked until previous stage</span>
                    </div>
                  ) : stage.status === 'completed' || stage.currentApprovals.filter(a => a.status === 'approved').length >= stage.minRequiredApprovals ? (
                    <div className="text-center py-3">
                      <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mx-auto mb-2 shadow-sm border border-emerald-200/60"
                           style={{ boxShadow: '0 4px 6px -1px rgba(16, 185, 129, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                        <svg className="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <p className="text-emerald-700 font-medium mb-1 text-sm">{stage.name} Complete</p>
                      <p className="text-xs text-gray-600">All required approvals obtained</p>
                    </div>
                  ) : (
                    <div className="text-center py-3">
                      <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mx-auto mb-2 shadow-sm border border-amber-200/60"
                           style={{ boxShadow: '0 4px 6px -1px rgba(245, 158, 11, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                        <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <p className="text-amber-700 font-medium mb-1 text-sm">Waiting for {stage.name} approval</p>
                      <div className="text-xs text-amber-600 bg-white px-2 py-1 rounded-lg border border-amber-200/60">
                        {stage.minRequiredApprovals} approval{stage.minRequiredApprovals !== 1 ? 's' : ''} needed
                      </div>
                    </div>
                  )}

                  {/* Enhanced Required Approvers - More compact */}
                  <div className="bg-white rounded-xl p-2 border border-gray-200/50"
                       style={{ boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                    <p className="font-medium mb-2 text-sm text-gray-700 flex items-center space-x-2">
                      <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.121M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      <span>Required approvers:</span>
                    </p>
                    <div className="space-y-1">
                      {stage.requiredApprovers.map((approver, idx) => {
                        const hasApproved = stage.currentApprovals.some(approval => 
                          approval.approverEmail === approver.email && approval.status === 'approved'
                        );
                        
                        const stageIsComplete = stage.status === 'completed' || stage.currentApprovals.filter(a => a.status === 'approved').length >= stage.minRequiredApprovals;
                        
                        return (
                          <div key={idx} className={`flex items-center justify-between px-2 py-1.5 rounded-lg text-sm transition-all duration-200 ${
                            hasApproved 
                              ? 'bg-white border border-emerald-200/60 text-emerald-700' 
                              : stageIsComplete
                              ? 'bg-white border border-gray-200/60 text-gray-600'
                              : 'bg-white border border-amber-200/60 text-amber-700'
                          }`}
                          style={{
                            boxShadow: hasApproved 
                              ? '0 1px 2px rgba(16, 185, 129, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' 
                              : stageIsComplete
                              ? '0 1px 2px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
                              : '0 1px 2px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
                          }}>
                            <span className="font-medium text-sm">{approver.email}</span>
                            <div className="flex items-center space-x-1">
                              {hasApproved ? (
                                <>
                                  <svg className="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                  <span className="text-emerald-600 text-xs font-medium">Approved</span>
                                </>
                              ) : stageIsComplete ? (
                                <>
                                  <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  <span className="text-gray-500 text-xs font-medium">Not Required</span>
                                </>
                              ) : (
                                <>
                                  <svg className="w-3 h-3 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  <span className="text-amber-600 text-xs font-medium">Pending</span>
                                </>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Subtle hover glow effect - removed background gradients */}
          <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
               style={{ boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)' }}></div>
        </div>
      ))}
    </div>
  );
};

export default StageDetailsGrid; 