import React from 'react';
import SignoffLoadingStates from './SignoffLoadingStates';
import OverallProgressBar from './OverallProgressBar';
import ApprovalFlowVisualization from './ApprovalFlowVisualization';
import StageDetailsGrid from './StageDetailsGrid';
import type { SignoffStatus } from '../../../services/signoffs';

interface SignoffsTabProps {
  signoffLoading: boolean;
  signoffError: string | null;
  signoffStatus: SignoffStatus | null;
  // Form and interaction props
  isAuthenticated: boolean;
  isSubmitting: boolean;
  authLoading: boolean;
  approvalFormData: {
    buildSha: string;
    buildNumber: string;
    knownIssues: string;
  };
  setApprovalFormData: React.Dispatch<React.SetStateAction<{
    buildSha: string;
    buildNumber: string;
    knownIssues: string;
  }>>;
  selectedSubTeam: string;
  setSelectedSubTeam: React.Dispatch<React.SetStateAction<string>>;
  expandedApprovals: Set<string>;
  setExpandedApprovals: React.Dispatch<React.SetStateAction<Set<string>>>;
  handleSubmitApproval: () => Promise<void>;
  isUserAuthorizedForSubTeam: (subTeam: string) => boolean;
}

const SignoffsTab: React.FC<SignoffsTabProps> = ({
  signoffLoading,
  signoffError,
  signoffStatus,
  isAuthenticated,
  isSubmitting,
  authLoading,
  approvalFormData,
  setApprovalFormData,
  selectedSubTeam,
  setSelectedSubTeam,
  expandedApprovals,
  setExpandedApprovals,
  handleSubmitApproval,
  isUserAuthorizedForSubTeam
}) => {
  // Show loading/error states first
  if (signoffLoading || signoffError || !signoffStatus) {
    return (
      <div className="space-y-6">
        <SignoffLoadingStates
          isLoading={signoffLoading}
          error={signoffError}
          hasData={!!signoffStatus}
        />
      </div>
    );
  }

  // Main content when data is available
  return (
    <div className="space-y-6">
      {/* Overall Progress Bar */}
      <OverallProgressBar signoffStatus={signoffStatus} />

      {/* Approval Flow Visualization */}
      <ApprovalFlowVisualization signoffStatus={signoffStatus} />

      {/* Real-time Stage Details */}
      <StageDetailsGrid 
        signoffStatus={signoffStatus}
        isAuthenticated={isAuthenticated}
        isSubmitting={isSubmitting}
        authLoading={authLoading}
        approvalFormData={approvalFormData}
        setApprovalFormData={setApprovalFormData}
        selectedSubTeam={selectedSubTeam}
        setSelectedSubTeam={setSelectedSubTeam}
        expandedApprovals={expandedApprovals}
        setExpandedApprovals={setExpandedApprovals}
        handleSubmitApproval={handleSubmitApproval}
        isUserAuthorizedForSubTeam={isUserAuthorizedForSubTeam}
      />
    </div>
  );
};

export default SignoffsTab; 