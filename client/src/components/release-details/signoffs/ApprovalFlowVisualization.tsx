import React, { useState, useEffect } from 'react';
import type { SignoffStatus, SignoffStage } from '../../../services/signoffs';
import { signoffsService } from '../../../services/signoffs';

interface ApprovalFlowVisualizationProps {
  signoffStatus: SignoffStatus;
}

// Interface for approver data
interface ApproverData {
  [subTeam: string]: string[];
}

const ApprovalFlowVisualization: React.FC<ApprovalFlowVisualizationProps> = ({ signoffStatus }) => {
  // State for managing approver data and tooltips
  const [approversData, setApproversData] = useState<ApproverData>({});
  const [hoveredSubTeam, setHoveredSubTeam] = useState<string | null>(null);
  const [loadingApprovers, setLoadingApprovers] = useState(false);

  // Fetch approvers data when component mounts or signoffStatus changes
  useEffect(() => {
    const fetchApproversData = async () => {
      if (!signoffStatus?.releaseId) return;
      
      setLoadingApprovers(true);
      try {
        // Fetch approvers for functional_qa stage
        const functionalQAStage = signoffStatus.stages.find(stage => stage.id === 'functional_qa');
        if (functionalQAStage) {
          const approversResponse = await signoffsService.getApprovers(signoffStatus.releaseId, 'functional_qa');
          
          // Transform the response into a more usable format
          const approversMap: ApproverData = {};
          approversResponse.approvers.forEach(item => {
            if (item.subTeam && item.approvers) {
              approversMap[item.subTeam] = item.approvers;
            }
          });
          
          setApproversData(approversMap);
        }
      } catch (error) {
        console.error('Failed to fetch approvers data:', error);
      } finally {
        setLoadingApprovers(false);
      }
    };

    fetchApproversData();
  }, [signoffStatus?.releaseId]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {signoffStatus.stages.map((stage: SignoffStage) => {
        const approvedCount = stage.currentApprovals.filter(a => a.status === 'approved').length;
        
        return (
          <div
            key={stage.id}
            className={`relative bg-white rounded-2xl p-5 transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.01] group border ${
              stage.status === 'completed' ? 'border-emerald-200/60' :
              stage.status === 'in_progress' ? 'border-amber-200/60' :
              stage.isAvailable ? 'border-blue-200/60' : 
              'border-gray-200/60 opacity-75'
            }`}
            style={{
              boxShadow: stage.status === 'completed' ? '0 8px 25px -5px rgba(16, 185, 129, 0.15), 0 8px 10px -6px rgba(16, 185, 129, 0.08)' :
                        stage.status === 'in_progress' ? '0 8px 25px -5px rgba(245, 158, 11, 0.15), 0 8px 10px -6px rgba(245, 158, 11, 0.08)' :
                        stage.isAvailable ? '0 8px 25px -5px rgba(59, 130, 246, 0.12), 0 8px 10px -6px rgba(59, 130, 246, 0.06)' :
                        '0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04)'
            }}
          >
            {/* Subtle top accent line */}
            <div className={`absolute top-0 left-5 right-5 h-0.5 rounded-full opacity-60 ${
              stage.status === 'completed' ? 'bg-gradient-to-r from-transparent via-emerald-400 to-transparent' :
              stage.status === 'in_progress' ? 'bg-gradient-to-r from-transparent via-amber-400 to-transparent' :
              stage.isAvailable ? 'bg-gradient-to-r from-transparent via-blue-400 to-transparent' :
              'bg-gradient-to-r from-transparent via-gray-400 to-transparent'
            }`}></div>

            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 drop-shadow-sm tracking-tight">{stage.name}</h3>
              <span className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 transform hover:scale-105 border ${
                stage.status === 'completed' ? 'bg-white text-emerald-700 border-emerald-200/60 shadow-sm' :
                stage.status === 'in_progress' ? 'bg-white text-amber-700 border-amber-200/60 shadow-sm' :
                'bg-white text-gray-600 border-gray-200/60 shadow-sm'
              }`}
              style={{
                boxShadow: stage.status === 'completed' ? '0 2px 4px rgba(16, 185, 129, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6)' :
                          stage.status === 'in_progress' ? '0 2px 4px rgba(245, 158, 11, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6)' :
                          '0 2px 4px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
              }}>
                {stage.status === 'in_progress' ? 'In Progress' : 
                 stage.status === 'completed' ? 'Completed' : 'Pending'}
              </span>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600 font-medium">Progress</span>
                <span className="font-semibold text-gray-900 drop-shadow-sm">{stage.progress}%</span>
              </div>
              
              {/* Enhanced progress bar */}
              <div className="relative">
                <div className="w-full bg-gradient-to-r from-gray-100 to-gray-200 rounded-full h-2.5 relative overflow-hidden"
                     style={{ boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.06), inset 0 1px 2px rgba(0, 0, 0, 0.08)' }}>
                <div 
                    className={`absolute top-0 left-0 h-full rounded-full transition-all duration-500 relative overflow-hidden ${
                      stage.status === 'completed' ? 'bg-gradient-to-r from-emerald-400 via-emerald-500 to-green-500' :
                      stage.status === 'in_progress' ? 'bg-gradient-to-r from-amber-400 via-amber-500 to-orange-500' : 
                      'bg-gradient-to-r from-gray-300 to-gray-400'
                  }`}
                    style={{ 
                      width: `${stage.progress}%`,
                      boxShadow: stage.status === 'completed' ? '0 1px 3px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)' :
                                stage.status === 'in_progress' ? '0 1px 3px rgba(245, 158, 11, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3)' :
                                '0 1px 2px rgba(0, 0, 0, 0.1)'
                    }}
                  >
                    {/* Subtle shine effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-25 rounded-full"></div>
                    
                    {/* Top highlight */}
                    <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent opacity-40 rounded-full"></div>
                  </div>
                  
                  {/* Progress indicator dot */}
                  {stage.progress > 0 && (
                    <div 
                      className={`absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-md border-2 transition-all duration-500 ease-out ${
                        stage.status === 'completed' ? 'border-emerald-500' :
                        stage.status === 'in_progress' ? 'border-amber-500' : 'border-gray-400'
                      }`}
                      style={{ 
                        left: `calc(${stage.progress}% - 6px)`,
                        boxShadow: stage.status === 'completed' ? '0 2px 4px rgba(16, 185, 129, 0.3)' :
                                  stage.status === 'in_progress' ? '0 2px 4px rgba(245, 158, 11, 0.3)' :
                                  '0 2px 4px rgba(0, 0, 0, 0.15)'
                      }}
                    >
                      <div className={`absolute inset-0.5 rounded-full ${
                        stage.status === 'completed' ? 'bg-emerald-500' :
                        stage.status === 'in_progress' ? 'bg-amber-500' : 'bg-gray-400'
                      }`}></div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Enhanced approval count */}
              <div className="bg-white/60 backdrop-blur-sm rounded-xl p-2.5 border border-gray-200/50"
                   style={{ boxShadow: '0 2px 4px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                <div className="text-sm text-gray-600 font-medium text-center">
                {stage.id === 'functional_qa' ? (
                  <span>{approvedCount}/{stage.requiredApprovers.length} sub-teams approved</span>
                ) : (
                  <span>{approvedCount}/{stage.minRequiredApprovals} approvals needed</span>
                )}
              </div>
              </div>

              {/* Sub-team Approval Details */}
              {stage.id === 'functional_qa' && stage.requiredApprovers.length > 0 && (
                <div className="space-y-2">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sub-team Status</div>
                  {/* Add scrollable container with max height */}
                  <div className="max-h-48 overflow-y-auto scrollbar-thin border border-gray-100 rounded-lg">
                    <div className="grid grid-cols-1 gap-1.5 p-1">
                      {stage.requiredApprovers.map((approver, index) => {
                        const approval = stage.currentApprovals.find(a => a.subTeam === approver.subTeam);
                        const isApproved = approval?.status === 'approved';
                        const subTeamName = approver.subTeam || 'General';
                        const hasApproversData = approversData[subTeamName] && approversData[subTeamName].length > 0;
                        
                        // Calculate dynamic tooltip positioning
                        const totalItems = stage.requiredApprovers.length;
                        const isInBottomHalf = index >= Math.floor(totalItems / 2);
                        const isLastTwo = index >= totalItems - 2;
                        
                        return (
                          <div 
                            key={approver.subTeam || `approver-${approver.email}`}
                            className={`relative flex items-center justify-between px-2.5 py-1.5 rounded-lg text-xs transition-all duration-200 ${
                              isApproved 
                                ? 'bg-white border border-emerald-200/60 text-emerald-700' 
                                : 'bg-white border border-amber-200/60 text-amber-700'
                            }`}
                            style={{
                              boxShadow: isApproved 
                                ? '0 1px 2px rgba(16, 185, 129, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' 
                                : '0 1px 2px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
                            }}
                          >
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full ${
                                isApproved ? 'bg-emerald-500' : 'bg-amber-500'
                              }`}></div>
                              <span className="font-medium capitalize">
                                {approver.subTeam ? approver.subTeam.toUpperCase() : 'General'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className={`flex items-center space-x-1 ${
                                isApproved ? 'text-emerald-600' : 'text-amber-600'
                              }`}>
                                {isApproved ? (
                                  <>
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span className="font-medium">Approved</span>
                                  </>
                                ) : (
                                  <>
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span className="font-medium">Pending</span>
                                  </>
                                )}
                              </div>
                              
                              {/* Info Icon with Dynamic Hover Tooltip */}
                              {hasApproversData && (
                                <div className="relative">
                                  <div
                                    onMouseEnter={() => setHoveredSubTeam(subTeamName)}
                                    onMouseLeave={() => setHoveredSubTeam(null)}
                                    className={`p-1 rounded-full transition-colors duration-200 cursor-help ${
                                      isApproved ? 'text-emerald-600 hover:bg-emerald-50' : 'text-amber-600 hover:bg-amber-50'
                                    }`}
                                    title="Show authorized approvers"
                                  >
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  </div>
                                  
                                  {/* Dynamic Hover Tooltip */}
                                  {hoveredSubTeam === subTeamName && (
                                    <div className={`absolute z-30 w-72 p-3 bg-white border border-gray-200 rounded-lg shadow-lg ${
                                      isInBottomHalf ? 'bottom-8' : 'top-8'
                                    } ${
                                      isLastTwo ? 'left-0' : 'right-0'
                                    }`}>
                                      <div className="mb-2">
                                        <h4 className={`font-semibold text-sm ${
                                          isApproved ? 'text-emerald-600' : 'text-amber-600'
                                        }`}>
                                          {subTeamName.charAt(0).toUpperCase() + subTeamName.slice(1)} Team Approvers
                                        </h4>
                                      </div>
                                      
                                      <div className="space-y-1.5 max-h-32 overflow-y-auto">
                                        <p className="text-xs text-gray-600 mb-2">Authorized approvers for this sub-team:</p>
                                        {approversData[subTeamName].map((email, emailIndex) => (
                                          <div key={emailIndex} className={`flex items-center space-x-2 p-1.5 rounded-lg border ${
                                            isApproved ? 'bg-emerald-50 border-emerald-100' : 'bg-amber-50 border-amber-100'
                                          }`}>
                                            <svg className={`w-3 h-3 flex-shrink-0 ${
                                              isApproved ? 'text-emerald-600' : 'text-amber-600'
                                            }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            <span className={`text-xs font-medium break-all ${
                                              isApproved ? 'text-emerald-800' : 'text-amber-800'
                                            }`}>{email}</span>
                                          </div>
                                        ))}
                                      </div>
                                      
                                      {/* Optional tooltip arrow */}
                                      <div className={`absolute w-2 h-2 bg-white border transform rotate-45 ${
                                        isInBottomHalf ? (isLastTwo ? 'top-[-4px] left-4' : 'top-[-4px] right-4') 
                                                       : (isLastTwo ? 'bottom-[-4px] left-4' : 'bottom-[-4px] right-4')
                                      } ${
                                        isInBottomHalf ? 'border-b-transparent border-r-transparent' : 'border-t-transparent border-l-transparent'
                                      }`}></div>
                                    </div>
                                  )}
                                </div>
                              )}
                              
                              {/* Loading indicator for approver data */}
                              {!hasApproversData && loadingApprovers && (
                                <div className="p-1">
                                  <svg className={`w-3 h-3 animate-spin ${
                                    isApproved ? 'text-emerald-600' : 'text-amber-600'
                                  }`} fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}

              {/* Non-QA stages approval details */}
              {stage.id !== 'functional_qa' && stage.currentApprovals.length > 0 && (
                <div className="space-y-2">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Approvals</div>
                  <div className="grid grid-cols-1 gap-1.5">
                    {stage.currentApprovals.slice(0, 3).map((approval, index) => (
                      <div 
                        key={approval.id || index}
                        className="flex items-center justify-between px-2.5 py-1.5 rounded-lg text-xs bg-white border border-emerald-200/60 text-emerald-700 transition-all duration-200"
                        style={{ boxShadow: '0 1px 2px rgba(16, 185, 129, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}
                      >
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                          <span className="font-medium">
                            {approval.approverEmail?.split('@')[0] || 'Approver'}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 text-emerald-600">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="font-medium">
                            {approval.approvedAt 
                              ? new Date(approval.approvedAt).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit',
                                  timeZoneName: 'short'
                                })
                              : 'Approved'
                            }
                          </span>
                        </div>
                      </div>
                    ))}
                    {stage.currentApprovals.length > 3 && (
                      <div className="text-xs text-gray-500 text-center py-1">
                        +{stage.currentApprovals.length - 3} more approvals
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Enhanced information for non-QA stages */}
              {stage.id !== 'functional_qa' && stage.status !== 'completed' && (
                <div className="space-y-2">
                  {/* Approver Information */}
                  <div className="space-y-1.5">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Approver Details</div>
                    <div className="bg-slate-50/80 border border-slate-200/60 rounded-lg p-2.5 text-xs"
                         style={{ boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <div className="flex items-center space-x-2 mb-1.5">
                        <svg className="w-3 h-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.121M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span className="font-medium text-slate-700">Required Approvers</span>
                      </div>
                      <div className="text-slate-600">
                        {stage.requiredApprovers.length > 0 ? (
                          <div className="space-y-1">
                            {stage.requiredApprovers.slice(0, 2).map((approver, index) => (
                              <div key={index} className="flex items-center space-x-2">
                                <div className="w-1.5 h-1.5 bg-slate-400 rounded-full"></div>
                                <span>{approver.email || approver.approvers?.[0] || 'Team Lead'}</span>
                              </div>
                            ))}
                            {stage.requiredApprovers.length > 2 && (
                              <div className="text-slate-500 text-xs">
                                +{stage.requiredApprovers.length - 2} more approvers
                              </div>
                            )}
                          </div>
                        ) : (
                          <span>Any authorized team member</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Stage Status & Timeline Information */}
                  {!stage.isAvailable ? (
                    <div className="bg-white border border-amber-200/60 rounded-lg p-2.5 text-xs"
                         style={{ boxShadow: '0 1px 2px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <div className="flex items-center space-x-2 mb-1.5">
                        <svg className="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span className="font-medium text-amber-700">Blocked</span>
                      </div>
                      <div className="text-amber-600">
                        Complete previous stage to unlock
                      </div>
                    </div>
                  ) : (stage.currentApprovals.filter(a => a.status === 'approved').length >= stage.minRequiredApprovals ? (
                    <div className="bg-white border border-emerald-200/60 rounded-lg p-2.5 text-xs"
                         style={{ boxShadow: '0 1px 2px rgba(16, 185, 129, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <div className="flex items-center space-x-2 mb-1.5">
                        <svg className="w-3 h-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span className="font-medium text-emerald-700">Complete</span>
                      </div>
                      <div className="text-emerald-600">
                        All required approvals obtained
                      </div>
                    </div>
                  ) : stage.status === 'in_progress' ? (
                    <div className="bg-white border border-amber-200/60 rounded-lg p-2.5 text-xs"
                         style={{ boxShadow: '0 1px 2px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <div className="flex items-center space-x-2 mb-1.5">
                        <svg className="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span className="font-medium text-amber-700">Active</span>
                      </div>
                      <div className="text-amber-600">
                        {stage.currentApprovals.length > 0 
                          ? `${stage.minRequiredApprovals - stage.currentApprovals.length} more approval${stage.minRequiredApprovals - stage.currentApprovals.length !== 1 ? 's' : ''} needed`
                          : 'Awaiting first approval'
                        }
                      </div>
                      {stage.lastUpdate && (
                        <div className="mt-1.5 pt-1.5 border-t border-amber-200/60 text-amber-600">
                          <div className="flex items-center space-x-1">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Last updated: {new Date(stage.lastUpdate).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit',
                              timeZoneName: 'short'
                            })}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="bg-white border border-blue-200/60 rounded-lg p-2.5 text-xs"
                         style={{ boxShadow: '0 1px 2px rgba(59, 130, 246, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                      <div className="flex items-center space-x-2 mb-1.5">
                        <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                        <span className="font-medium text-blue-700">Ready to Start</span>
                      </div>
                      <div className="text-blue-600">
                        {stage.minRequiredApprovals} approval{stage.minRequiredApprovals !== 1 ? 's' : ''} needed to complete
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {!stage.isAvailable && (
                <div className="flex items-center justify-center text-amber-600 bg-white rounded-xl p-2.5 border border-amber-200/60"
                     style={{ boxShadow: '0 2px 4px rgba(245, 158, 11, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)' }}>
                  <svg className="w-4 h-4 mr-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <span className="text-sm font-medium">Locked until previous stage</span>
                </div>
              )}
            </div>
            
            {/* Subtle hover glow effect - removed background gradients */}
            <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                 style={{ boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)' }}></div>
          </div>
        );
      })}
    </div>
  );
};

export default ApprovalFlowVisualization; 