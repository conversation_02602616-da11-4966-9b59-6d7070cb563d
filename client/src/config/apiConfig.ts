// API base URL
export const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: '/api/auth',
  LOGIN: '/api/auth/login',
  LOGOUT: '/api/auth/logout',
  REFRESH: '/api/auth/refresh',
  
  // Teams
  TEAMS: '/api/teams',
  
  // Releases
  RELEASES: '/api/releases',
  
  // Signoffs
  SIGNOFFS: '/api/signoffs',
  
  // Metrics
  METRICS: '/api/metrics',
  
  // Vault (Azure Key Vault)
  VAULT: '/api/vault',
  VAULT_SECRET: '/api/vault/secret',
  VAULT_SECRETS: '/api/vault/secrets',
} as const;

// Full API URLs
export const getFullApiUrl = (endpoint: string): string => {
  return `${API_BASE_URL}${endpoint}`;
}; 