import { useState, useEffect, useCallback, useRef } from 'react';

export interface CacheConfig {
  key: string;
  maxAge?: number; // in milliseconds, default 15 minutes
  useLocalStorage?: boolean; // default false (session storage)
}

interface CachedData<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface MetricsCacheState<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  lastUpdated: string | null;
  isFromCache: boolean;
}

interface MetricsCacheActions<T> {
  fetchData: (forceRefresh?: boolean) => Promise<void>;
  clearCache: () => void;
  refreshData: () => Promise<void>;
  setData: (data: T) => void;
}

export interface UseMetricsCacheReturn<T> extends MetricsCacheState<T>, MetricsCacheActions<T> {
  dbTimestamp?: string | null;
}

export const DEFAULT_CACHE_DURATION = 60 * 60 * 1000; // 60 minutes

export function useMetricsCache<T>(
  config: CacheConfig,
  fetchFunction: () => Promise<T>,
  dependencies: any[] = []
): UseMetricsCacheReturn<T> {
  const [state, setState] = useState<MetricsCacheState<T>>({
    data: null,
    isLoading: false,
    error: null,
    lastUpdated: null,
    isFromCache: false,
  });

  const [dbTimestamp] = useState<string | null>(null);

  const abortControllerRef = useRef<AbortController | null>(null);
  const fetchFunctionRef = useRef(fetchFunction);

  // Update the fetch function ref when it changes
  useEffect(() => {
    fetchFunctionRef.current = fetchFunction;
  }, [fetchFunction]);

  const getStorageKey = useCallback(() => `metrics_cache_${config.key}`, [config.key]);

  const getStorage = useCallback(() => {
    return config.useLocalStorage ? localStorage : sessionStorage;
  }, [config.useLocalStorage]);

  const getCachedData = useCallback((): CachedData<T> | null => {
    try {
      const storage = getStorage();
      const cached = storage.getItem(getStorageKey());
      if (!cached) return null;

      const parsedData: CachedData<T> = JSON.parse(cached);

      // Check if cache is expired
      if (Date.now() > parsedData.expiresAt) {
        storage.removeItem(getStorageKey());
        return null;
      }

      return parsedData;
    } catch (error) {
      console.warn('Failed to read cached data:', error);
      return null;
    }
  }, [getStorage, getStorageKey]);

  const setCachedData = useCallback(
    (data: T) => {
      try {
        const storage = getStorage();
        const maxAge = config.maxAge ?? DEFAULT_CACHE_DURATION;
        const now = Date.now();

        const cachedData: CachedData<T> = {
          data,
          timestamp: now,
          expiresAt: now + maxAge,
        };

        storage.setItem(getStorageKey(), JSON.stringify(cachedData));
      } catch (error) {
        console.warn('Failed to cache data:', error);
      }
    },
    [getStorage, getStorageKey, config.maxAge]
  );

  const clearCache = useCallback(() => {
    try {
      const storage = getStorage();
      storage.removeItem(getStorageKey());
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }, [getStorage, getStorageKey]);

  const setData = useCallback(
    (data: T) => {
      const now = new Date().toISOString();
      setState((prev) => ({
        ...prev,
        data,
        lastUpdated: now,
        error: null,
        isFromCache: false,
      }));
      setCachedData(data);
    },
    [setCachedData]
  );

  const fetchData = useCallback(
    async (forceRefresh: boolean = false) => {
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Check cache first unless forcing refresh
      if (!forceRefresh) {
        const cachedData = getCachedData();
        if (cachedData) {
          console.log(`Using cached data for ${config.key}`);
          setState((prev) => ({
            ...prev,
            data: cachedData.data,
            lastUpdated: new Date(cachedData.timestamp).toISOString(),
            isLoading: false,
            error: null,
            isFromCache: true,
          }));
          return;
        }
      }

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      setState((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
        isFromCache: false,
      }));

      try {
        console.log(`Fetching fresh data for ${config.key}`);
        const data = await fetchFunctionRef.current();

        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        const now = new Date().toISOString();
        setState((prev) => ({
          ...prev,
          data,
          lastUpdated: now,
          isLoading: false,
          error: null,
          isFromCache: false,
        }));

        // Cache the data
        setCachedData(data);
      } catch (error) {
        // Check if request was aborted
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        const errorObj = error instanceof Error ? error : new Error(String(error));
        console.error(`Failed to fetch data for ${config.key}:`, errorObj);

        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: errorObj,
          isFromCache: false,
        }));
      }
    },
    [config.key, getCachedData, setCachedData]
  );

  const refreshData = useCallback(() => fetchData(true), [fetchData]);

  // Auto-fetch on mount and dependency changes
  useEffect(() => {
    fetchData();

    // Cleanup function to abort ongoing requests
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [config.key, ...dependencies]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    fetchData,
    clearCache,
    refreshData,
    setData,
    dbTimestamp,
  };
}

// Utility function to clear all metrics cache
export function clearAllMetricsCache() {
  const localStorageKeys: string[] = [];
  const sessionStorageKeys: string[] = [];

  // Collect all cache keys from localStorage
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.startsWith('metrics_cache_')) {
      localStorageKeys.push(key);
    }
  }

  // Collect all cache keys from sessionStorage
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key?.startsWith('metrics_cache_')) {
      sessionStorageKeys.push(key);
    }
  }

  // Clear all cache keys
  localStorageKeys.forEach((key) => localStorage.removeItem(key));
  sessionStorageKeys.forEach((key) => sessionStorage.removeItem(key));

  console.log(
    `Cleared ${localStorageKeys.length + sessionStorageKeys.length} metrics cache entries`
  );
}

// Hook for pre-loading cache data
export function useMetricsCachePreloader() {
  const preloadCache = useCallback(
    async <T>(config: CacheConfig, fetchFunction: () => Promise<T>) => {
      const storage = config.useLocalStorage ? localStorage : sessionStorage;
      const key = `metrics_cache_${config.key}`;

      try {
        const cached = storage.getItem(key);
        if (cached) {
          const parsedData: CachedData<T> = JSON.parse(cached);
          if (Date.now() <= parsedData.expiresAt) {
            console.log(`Cache hit for preload: ${config.key}`);
            return parsedData.data;
          }
        }

        console.log(`Preloading data for: ${config.key}`);
        const data = await fetchFunction();

        const maxAge = config.maxAge ?? DEFAULT_CACHE_DURATION;
        const now = Date.now();
        const cachedData: CachedData<T> = {
          data,
          timestamp: now,
          expiresAt: now + maxAge,
        };

        if (config.useLocalStorage) {
          localStorage.setItem(key, JSON.stringify(cachedData));
        } else {
          sessionStorage.setItem(key, JSON.stringify(cachedData));
        }
        return data;
      } catch (error) {
        console.warn(`Failed to preload cache for ${config.key}:`, error);
        throw error;
      }
    },
    []
  );

  return { preloadCache };
}

// New hook specifically for fetching database timestamps for metrics
export function useMetricTimestamp(releaseId: string | undefined, metricType: string) {
  const [dbTimestamp, setDbTimestamp] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchDbTimestamp = useCallback(async () => {
    if (!releaseId) {
      setDbTimestamp(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';
      const response = await fetch(`${API_BASE_URL}/api/metrics/${releaseId}/stored`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch stored metrics: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.data?.releaseMetrics?.[metricType]?.lastUpdated) {
        setDbTimestamp(data.data.releaseMetrics[metricType].lastUpdated);
      } else {
        setDbTimestamp(null);
      }
    } catch (err) {
      console.warn(`Failed to fetch database timestamp for ${metricType}:`, err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setDbTimestamp(null);
    } finally {
      setIsLoading(false);
    }
  }, [releaseId, metricType]);

  useEffect(() => {
    fetchDbTimestamp();
  }, [fetchDbTimestamp]);

  return {
    dbTimestamp,
    isLoading,
    error,
    refetch: fetchDbTimestamp,
  };
}
 