import { useState } from 'react';
import { PDFExportService } from '../services/pdfExport';
import { getReleaseSummaryEmail } from '../utils/emailTemplates';
import type { Release } from '../services/release';
import type { CosmosTeam } from '../services/api';
import type { SignoffStatus } from '../services/signoffs';
import type { ConfidenceMetric } from '../types/metrics';
import { signoffsService } from '../services/signoffs';

export interface UseReleaseActionsProps {
  release: Release | null;
  teams: CosmosTeam[];
  signoffStatus: SignoffStatus | null;
  confidenceMetrics: ConfidenceMetric[];
  currentUser: any;
  selectedSubTeam: string;
  approvalFormData: any;
  setSelectedSubTeam: (subTeam: string) => void;
  setApprovalFormData: (data: any) => void;
  loadSignoffStatus: () => void;
  isUserAuthorizedForSubTeam: (subTeam: string) => boolean;
  showToast: (message: string, type: 'success' | 'error') => void;
}

export const useReleaseActions = ({
  release,
  teams,
  signoffStatus,
  confidenceMetrics,
  currentUser,
  selectedSubTeam,
  approvalFormData,
  setSelectedSubTeam,
  setApprovalFormData,
  loadSignoffStatus,
  isUserAuthorizedForSubTeam,
  showToast
}: UseReleaseActionsProps) => {
  const [pdfGenerating, setPdfGenerating] = useState<Set<string>>(new Set());

  const handlePdfDownload = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!release) return;
    
    // Prevent multiple simultaneous downloads
    if (pdfGenerating.has(release.id)) {
      return;
    }
    
    try {
      // Set loading state
      setPdfGenerating(prev => new Set(prev).add(release.id));
      
      console.log('Generating PDF for release:', release.releaseName);
      
      // Get team information
      const team = teams.find(t => t.id === release.teamId) || null;
      
      // Get signoff status
      let signoffStatus = null;
      try {
        signoffStatus = await signoffsService.getSignoffStatus(release.id);
      } catch (error) {
        console.warn('Could not load signoff data for PDF:', error);
      }
      
      // Export PDF with all data
      await PDFExportService.exportReleasePDF(
        release,
        signoffStatus,
        team,
        confidenceMetrics,
        {
          includeSignoffs: true,
          includeMetrics: true,
          includeTeamInfo: true,
          includeDetailedMetrics: true,
          format: 'a4',
          orientation: 'portrait'
        }
      );
      
      console.log('PDF generated successfully');
      showToast('PDF generated successfully', 'success');
    } catch (error) {
      console.error('Error generating PDF:', error);
      showToast('Failed to generate PDF. Please try again.', 'error');
    } finally {
      // Clear loading state
      setPdfGenerating(prev => {
        const newSet = new Set(prev);
        newSet.delete(release.id);
        return newSet;
      });
    }
  };

  const handleSendEmail = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!release) return;
    
    try {
      const team = teams.find(t => t.id === release.teamId) || null;
      const teamName = team?.teamName || 'Unknown Team';
      const confidenceScore = release.confidenceScore || 0;
      const generatedDate = new Date().toLocaleDateString('en-US', { 
        weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', 
        hour: '2-digit', minute: '2-digit', timeZoneName: 'short' 
      });
      
      // Fetch signoff status for this release
      let signoffStatus = null;
      try {
        signoffStatus = await signoffsService.getSignoffStatus(release.id);
      } catch (error) {
        console.warn('Could not load signoff data for email:', error);
      }
      
      const subject = encodeURIComponent(`Release Summary: ${release.releaseName} | ${teamName} Team`);
      const body = encodeURIComponent(getReleaseSummaryEmail({
        release,
        teamName,
        signoffStatus,
        overallConfidence: confidenceScore,
        url: window.location.origin,
        releaseDetailsUrl: `${window.location.origin}/releases/${release.id}`,
        generatedDate
      }));
      
      const mailtoLink = `mailto:?subject=${subject}&body=${body}`;
      window.open(mailtoLink, '_blank');
    } catch (error) {
      console.error('Error preparing email:', error);
      showToast('Failed to prepare email. Please try again.', 'error');
    }
  };

  const handleSubmitApproval = async () => {
    if (!release?.id || !currentUser) {
      console.error('Missing required data for submission');
      return;
    }

    // Find the current active stage that can be approved
    const currentStage = signoffStatus?.stages.find(stage => 
      stage.isAvailable && stage.status !== 'completed'
    );

    if (!currentStage) {
      console.error('No available stage to approve');
      return;
    }

    // For functional QA, require sub-team selection and authorization
    if (currentStage.id === 'functional_qa') {
      if (!selectedSubTeam) {
        console.error('Sub-team selection required for functional QA');
        return;
      }

      // Check authorization before submitting
      if (!isUserAuthorizedForSubTeam(selectedSubTeam)) {
        console.error('User not authorized for this sub-team');
        return;
      }
    }

    try {
      let requestBody: any;
      let endpoint: string;

      if (currentStage.id === 'functional_qa') {
        // Functional QA approval with sub-team
        requestBody = {
          subTeam: selectedSubTeam,
          approverEmail: currentUser.email,
          sha: approvalFormData.buildSha,
          buildNumber: approvalFormData.buildNumber,
          notes: approvalFormData.knownIssues || 'No issues reported'
        };
        endpoint = `${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/signoffs/${release.id}/approve/functional_qa`;
      } else {
        // E2E QA or Engineering Leadership approval (individual approvers)
        requestBody = {
          approverEmail: currentUser.email,
          sha: approvalFormData.buildSha,
          buildNumber: approvalFormData.buildNumber,
          notes: approvalFormData.knownIssues || 'No issues reported'
        };
        endpoint = `${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/signoffs/${release.id}/approve/${currentStage.id}`;
      }
      
      console.log('Submitting approval with data:', requestBody);
      console.log('Endpoint:', endpoint);
      
      // Call the signoffs API to submit approval
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server error response:', errorData);
        throw new Error(`Failed to submit approval: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      console.log('Approval submitted successfully:', result);

      // Reset form
      setSelectedSubTeam('');
      setApprovalFormData({
        buildSha: '',
        buildNumber: '',
        knownIssues: ''
      });

      // Refresh signoff status
      await loadSignoffStatus();
    } catch (error) {
      console.error('Error submitting approval:', error);
      throw error; // Re-throw so the component can handle it
    }
  };

  return {
    pdfGenerating,
    handlePdfDownload,
    handleSendEmail,
    handleSubmitApproval
  };
}; 