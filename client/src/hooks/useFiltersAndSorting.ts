import { useState, useEffect, useMemo } from 'react';
import type { Release } from '../services/release';
import type { CosmosTeam } from '../services/api';
import { getPlatformIcons, getAvailablePlatforms } from '../utils/releaseHelpers';

export type ViewType = 'grid' | 'table';
export type SortField = 'name' | 'date' | 'team' | 'updatedAt';
export type SortOrder = 'asc' | 'desc';

export interface FilterState {
  team: string;
  platform: string;
  search: string;
}

interface UseFiltersAndSortingProps {
  releases: Release[];
  teams: CosmosTeam[];
  getTeamName: (teamId: string) => string;
}

export const useFiltersAndSorting = ({ releases, teams, getTeamName }: UseFiltersAndSortingProps) => {
  const [viewType, setViewType] = useState<ViewType>('table');
  const [sortField, setSortField] = useState<SortField>('updatedAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filters, setFilters] = useState<FilterState>({
    team: '',
    platform: '',
    search: ''
  });
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Track active filters count
  useEffect(() => {
    setActiveFiltersCount(Object.values(filters).filter(Boolean).length);
  }, [filters]);

  const filteredAndSortedReleases = useMemo(() => {
    const filtered = releases.filter(release => {
      const teamName = getTeamName(release.teamId);
      const platformIcons = getPlatformIcons(release.supportedPlatforms);
      
      // Team filter
      if (filters.team && teamName !== filters.team) return false;
      
      // Platform filter
      if (filters.platform && !platformIcons.some(p => p.name === filters.platform)) return false;
      
      // Search filter
      if (filters.search && !release.releaseName.toLowerCase().includes(filters.search.toLowerCase()) && 
          !teamName.toLowerCase().includes(filters.search.toLowerCase())) return false;
      
      return true;
    });

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case 'name':
          comparison = a.releaseName.localeCompare(b.releaseName);
          break;
        case 'date':
          // Use safe date parsing for sorting to avoid timezone issues
          const getDateForSorting = (dateString: string): Date => {
            const dateParts = dateString.split('-');
            if (dateParts.length === 3) {
              const year = parseInt(dateParts[0]);
              const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
              const day = parseInt(dateParts[2]);
              return new Date(year, month, day);
            }
            return new Date(dateString);
          };
          comparison = getDateForSorting(a.releaseRolloutDate).getTime() - getDateForSorting(b.releaseRolloutDate).getTime();
          break;
        case 'team':
          comparison = getTeamName(a.teamId).localeCompare(getTeamName(b.teamId));
          break;
        case 'updatedAt':
          comparison = new Date(a.updatedAt || Date.now()).getTime() - new Date(b.updatedAt || Date.now()).getTime();
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [releases, filters, sortField, sortOrder, teams, getTeamName]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const clearFilters = () => {
    setFilters({
      team: '',
      platform: '',
      search: ''
    });
  };

  const availablePlatforms = useMemo(() => getAvailablePlatforms(releases), [releases]);

  return {
    viewType,
    setViewType,
    sortField,
    sortOrder,
    filters,
    setFilters,
    activeFiltersCount,
    filteredAndSortedReleases,
    handleSort,
    clearFilters,
    availablePlatforms
  };
}; 