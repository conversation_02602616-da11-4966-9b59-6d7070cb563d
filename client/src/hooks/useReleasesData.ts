import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { releaseService, type Release } from '../services/release';
import { apiService, type CosmosTeam } from '../services/api';

export const useReleasesData = () => {
  const [releases, setReleases] = useState<Release[]>([]);
  const [teams, setTeams] = useState<CosmosTeam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [releasesResult, teamsResult] = await Promise.allSettled([
        releaseService.getReleases(),
        apiService.getTeams()
      ]);

      if (releasesResult.status === 'fulfilled') {
        setReleases(releasesResult.value);
      }

      if (teamsResult.status === 'fulfilled' && teamsResult.value.success) {
        setTeams(teamsResult.value.data || []);
      }
    } catch (error) {
      console.error('Error loading releases data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Refresh data when page becomes visible (e.g., navigating back from release details)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadData();
      }
    };

    const handleFocus = () => {
      loadData();
    };

    // Listen for visibility changes and window focus
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Refresh data when navigating back to releases page
  useEffect(() => {
    // Only refresh if we're on the releases page (not sub-routes)
    if (location.pathname === '/releases') {
      loadData();
    }
  }, [location.pathname]);

  const getTeamName = (teamId: string): string => {
    const team = teams.find(t => t.id === teamId);
    return team?.teamName || 'Unknown Team';
  };

  return {
    releases,
    teams,
    isLoading,
    loadData,
    getTeamName
  };
}; 