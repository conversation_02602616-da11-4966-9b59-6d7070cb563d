import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { releaseService, type Release } from '../services/release';
import { signoffsService, type SignoffStatus } from '../services/signoffs';
import { apiService, type CosmosTeam } from '../services/api';
import type { ConfidenceMetric } from '../types/metrics';

export const useReleaseDetails = (releaseId: string | undefined) => {
  const { user: currentUser, isAuthenticated, isLoading: authLoading } = useAuth();
  
  // Core state
  const [release, setRelease] = useState<Release | null>(null);
  const [teams, setTeams] = useState<CosmosTeam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Tab state
  const [activeTab, setActiveTab] = useState<'overview' | 'signoffs' | 'metrics'>('overview');
  const [activeMetricsTab, setActiveMetricsTab] = useState<string>('code-quality');
  
  // Signoff state
  const [signoffStatus, setSignoffStatus] = useState<SignoffStatus | null>(null);
  const [signoffLoading, setSignoffLoading] = useState(false);
  const [signoffError, setSignoffError] = useState<string | null>(null);
  
  // Form state
  const [selectedSubTeam, setSelectedSubTeam] = useState<string>('');
  const [approvalFormData, setApprovalFormData] = useState({
    stage: '',
    sha: '',
    buildNumber: '',
    notes: ''
  });
  const [expandedApprovals, setExpandedApprovals] = useState<Set<string>>(new Set());
  
  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPdfGenerating, setIsPdfGenerating] = useState(false);
  const [isEmailProcessing, setIsEmailProcessing] = useState(false);
  
  // Team configuration
  const [teamConfig, setTeamConfig] = useState<{
    roles: {
      functional_qa?: {
        [subTeamName: string]: string[];
      };
    };
  } | null>(null);
  
  // UI state
  const [emailDropdownOpen, setEmailDropdownOpen] = useState(false);
  const [hoveredToolIndex, setHoveredToolIndex] = useState<number | null>(null);

  // Real confidence metrics fetched from backend
  const [confidenceMetrics, setConfidenceMetrics] = useState<ConfidenceMetric[]>([]);
  const [overallConfidence, setOverallConfidence] = useState<number>(0);
  const [metricsLoading, setMetricsLoading] = useState(false);

  // Helper function to calculate confidence score with 6-metric weightings
  const calculateNewConfidenceScore = (metrics: ConfidenceMetric[]): number => {
    const weightings = {
      'code-quality': 20,
      'code-coverage': 20,
      'test-results': 20,
      'build-stability': 20,
      'change-requests': 10,
      'change-log': 10
    };

    let totalScore = 0;
    let totalWeight = 0;

    metrics.forEach(metric => {
      const weight = weightings[metric.id as keyof typeof weightings] || 0;
      if (weight > 0) {
        totalScore += metric.score * (weight / 100);
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? Math.round(totalScore) : 0;
  };

  // Load release data
  const loadReleaseData = async () => {
    if (!releaseId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const [releasesResult, teamsResult] = await Promise.allSettled([
        releaseService.getReleases(),
        apiService.getTeams()
      ]);

      if (teamsResult.status === 'fulfilled' && teamsResult.value.success) {
        setTeams(teamsResult.value.data || []);
      }

      if (releasesResult.status === 'fulfilled') {
        const foundRelease = releasesResult.value.find(r => r.id === releaseId);
        if (foundRelease) {
          setRelease(foundRelease);
        } else {
          setError('Release not found');
        }
      } else {
        setError('Failed to load release data');
      }
    } catch (error) {
      console.error('Error loading release data:', error);
      setError('Failed to load release data');
    } finally {
      setIsLoading(false);
    }
  };

  // Load signoff status
  const loadSignoffStatus = async () => {
    if (!release?.id) return;
    
    setSignoffLoading(true);
    setSignoffError(null);
    try {
      const status = await signoffsService.getSignoffStatus(release.id);
      setSignoffStatus(status);
    } catch (error) {
      console.error('Error loading signoff status:', error);
      setSignoffError(error instanceof Error ? error.message : 'Failed to load signoff status');
    } finally {
      setSignoffLoading(false);
    }
  };

  // Fetch team configuration
  const fetchTeamConfig = async (teamId: string) => {
    try {
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/teams/${teamId}/config`);
      if (response.ok) {
        const config = await response.json();
        return config;
      }
      return null;
    } catch (error) {
      console.error('Error fetching team config:', error);
      return null;
    }
  };

  // Authorization helpers
  const isUserAuthorizedForSubTeam = (subTeam: string): boolean => {
    if (!currentUser || !teamConfig?.roles?.functional_qa) {
      return false;
    }
    
    const subTeamApprovers = teamConfig.roles.functional_qa[subTeam.toLowerCase()];
    return subTeamApprovers && subTeamApprovers.includes(currentUser.email);
  };

  const getAuthorizationError = (subTeam: string): string | null => {
    if (!currentUser) {
      return 'Please sign in to submit approvals';
    }
    
    if (!selectedSubTeam) {
      return 'Please select a sub-team';
    }
    
    if (!teamConfig?.roles?.functional_qa) {
      return 'Team configuration not loaded';
    }
    
    if (!isUserAuthorizedForSubTeam(subTeam)) {
      const authorizedUsers = teamConfig?.roles?.functional_qa?.[subTeam.toLowerCase()] || [];
      return `You (${currentUser.email}) are not authorized to approve for ${subTeam.toUpperCase()} team. Authorized approvers: ${authorizedUsers.join(', ')}`;
    }
    
    return null;
  };

  // Helper functions
  const getTeamName = (teamId: string): string => {
    const team = teams.find(t => t.id === teamId);
    return team?.teamName || 'Unknown Team';
  };

  // Fetch real metrics from backend
  const loadReleaseMetrics = async () => {
    if (!releaseId) return;
    
    console.log('🔄 Loading release metrics for releaseId:', releaseId);
    setMetricsLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/metrics/${releaseId}/stored`);
      
      console.log('📡 Metrics API response status:', response.status);
      
      if (response.ok) {
        const apiResponse = await response.json();
        console.log('📊 Received API response:', apiResponse);
        
        // Extract data from the success response structure
        const data = apiResponse.success ? apiResponse.data : apiResponse;
        
        if (data?.releaseMetrics) {
          const metrics: ConfidenceMetric[] = [];
          
          // Process Code Quality metrics
          if (data.releaseMetrics.codeQuality) {
            const codeQualityData = data.releaseMetrics.codeQuality;
            metrics.push({
              id: 'code-quality',
              name: 'Code Quality',
              icon: 'bug-ant',
              score: codeQualityData.score || 0,
              label: codeQualityData.label || 'Code Quality Metrics',
              description: 'Overall code quality based on bugs, vulnerabilities, and code smells',
              color: '#f59e0b', // amber
              details: codeQualityData.rawData || {}
            });
          }

          // Process Code Coverage metrics
          if (data.releaseMetrics.codeCoverage) {
            const codeCoverageData = data.releaseMetrics.codeCoverage;
            metrics.push({
              id: 'code-coverage',
              name: 'Code Coverage',
              icon: 'shield-check',
              score: codeCoverageData.score || 0,
              label: codeCoverageData.label || 'Test Coverage Metrics',
              description: 'Percentage of code covered by automated tests',
              color: '#10b981', // emerald
              details: codeCoverageData.rawData || {}
            });
          }

          // Process Test Results metrics
          if (data.releaseMetrics.testResults) {
            const testResultsData = data.releaseMetrics.testResults;
            metrics.push({
              id: 'test-results',
              name: 'Test Results',
              icon: 'beaker',
              score: testResultsData.score || 0,
              label: testResultsData.label || 'Test Results Metrics',
              description: 'Test execution results and success rates',
              color: '#3b82f6', // blue
              details: testResultsData.rawData || {}
            });
          }

          // Process Changelog metrics (GitHub PRs) - check both naming conventions
          const changelogData = data.releaseMetrics.changeLog || data.releaseMetrics.changelog;
          if (changelogData) {
            metrics.push({
              id: 'change-log',
              name: 'Change Log',
              icon: 'clipboard-document-list',
              score: changelogData.score || 0,
              label: changelogData.label || 'Change Log Metrics',
              description: 'GitHub pull requests and changes',
              color: '#8b5cf6', // purple
              details: changelogData.rawData || {}
            });
          }

          // Process Change Requests metrics
          if (data.releaseMetrics.changeRequests) {
            const changeRequestsData = data.releaseMetrics.changeRequests;
            metrics.push({
              id: 'change-requests',
              name: 'Change Requests',
              icon: 'document-text',
              score: changeRequestsData.score || 0,
              label: changeRequestsData.label || 'Change Requests Metrics',
              description: 'Management of change requests and issues',
              color: '#ef4444', // red
              details: changeRequestsData.rawData || {}
            });
          }

          // Process Build Stability metrics
          if (data.releaseMetrics.buildStability) {
            const buildStabilityData = data.releaseMetrics.buildStability;
            metrics.push({
              id: 'build-stability',
              name: 'Build Stability',
              icon: 'wrench-screwdriver',
              score: buildStabilityData.score || buildStabilityData.statistics?.successRate || 0,
              label: buildStabilityData.label || 'Build Stability Metrics',
              description: 'Build pipeline success rates and stability',
              color: '#06b6d4', // cyan
              details: buildStabilityData.rawData || buildStabilityData.statistics || {}
            });
          }

          

          

          // Use all metrics for confidence calculation (6-metric system)
          const confidenceMetrics = metrics;

          // Calculate overall confidence from stored data or individual metrics with new weightings
          const overallScore = data.confidenceMetrics?.overallScore || 
                             (confidenceMetrics.length > 0 ? calculateNewConfidenceScore(confidenceMetrics) : 0);

          console.log('✅ Processed metrics:', {
            metricsCount: confidenceMetrics.length,
            overallScore,
            metrics: confidenceMetrics.map(m => ({ name: m.name, score: m.score })),
            hasChangelog: !!data.releaseMetrics.changeLog
          });

          setConfidenceMetrics(confidenceMetrics);
          setOverallConfidence(overallScore);
        } else {
          console.log('⚠️ No releaseMetrics found in response');
          setConfidenceMetrics([]);
          setOverallConfidence(0);
        }
      } else {
        console.log('❌ Failed to fetch stored metrics, status:', response.status);
        setConfidenceMetrics([]);
        setOverallConfidence(0);
      }
    } catch (error) {
      console.error('❌ Error loading release metrics:', error);
      setConfidenceMetrics([]);
      setOverallConfidence(0);
    } finally {
      setMetricsLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    if (releaseId) {
      loadReleaseData();
    }
  }, [releaseId]);

  useEffect(() => {
    const initializeTeamConfig = async () => {
      if (release?.teamId) {
        const config = await fetchTeamConfig(release.teamId);
        setTeamConfig(config);
      }
    };

    initializeTeamConfig();
  }, [release?.teamId]);

  useEffect(() => {
    if (release?.id && (activeTab === 'signoffs' || activeTab === 'overview')) {
      loadSignoffStatus();
    }
  }, [release?.id, activeTab]);

  useEffect(() => {
    if (releaseId) {
      loadReleaseMetrics();
    }
  }, [releaseId]);

  return {
    // Core state
    release,
    teams,
    isLoading,
    error,
    
    // Tab state
    activeTab,
    setActiveTab,
    activeMetricsTab,
    setActiveMetricsTab,
    
    // Signoff state
    signoffStatus,
    signoffLoading,
    signoffError,
    
    // Form state
    selectedSubTeam,
    setSelectedSubTeam,
    approvalFormData,
    setApprovalFormData,
    expandedApprovals,
    setExpandedApprovals,
    
    // Loading states
    isSubmitting,
    setIsSubmitting,
    isPdfGenerating,
    setIsPdfGenerating,
    isEmailProcessing,
    setIsEmailProcessing,
    
    // Auth state
    currentUser,
    isAuthenticated,
    authLoading,
    teamConfig,
    
    // UI state
    emailDropdownOpen,
    setEmailDropdownOpen,
    hoveredToolIndex,
    setHoveredToolIndex,
    
    // Computed values
    confidenceMetrics,
    overallConfidence,
    metricsLoading,
    
    // Functions
    loadSignoffStatus,
    loadReleaseMetrics,
    isUserAuthorizedForSubTeam,
    getAuthorizationError,
    getTeamName
  };
}; 