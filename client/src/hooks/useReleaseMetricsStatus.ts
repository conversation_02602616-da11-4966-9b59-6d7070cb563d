import { useState, useEffect, useRef } from 'react';

interface ReleaseMetricsStatus {
  isProcessing: boolean;
  hasMetrics: boolean;
  confidenceScore?: number;
  lastCheck: string | null;
  error: string | null;
}

export const useReleaseMetricsStatus = (releaseId: string | undefined, initialCheck = true) => {
  const [status, setStatus] = useState<ReleaseMetricsStatus>({
    isProcessing: false,
    hasMetrics: false,
    confidenceScore: undefined,
    lastCheck: null,
    error: null
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const maxChecksRef = useRef(0);
  const checksCountRef = useRef(0);

  const checkMetricsStatus = async () => {
    if (!releaseId) return;

    try {
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/metrics/${releaseId}/stored`);
      
      if (response.ok) {
        const data = await response.json();
        const hasValidMetrics = data.releaseMetrics && Object.keys(data.releaseMetrics).length > 0;
        const confidenceScore = data.confidenceMetrics?.overallScore;
        
        setStatus({
          isProcessing: false,
          hasMetrics: hasValidMetrics,
          confidenceScore,
          lastCheck: new Date().toISOString(),
          error: null
        });

        // If we have metrics, stop polling
        if (hasValidMetrics) {
          stopPolling();
        }
      } else {
        // No metrics yet, but not an error - still processing
        setStatus(prev => ({
          ...prev,
          isProcessing: true,
          lastCheck: new Date().toISOString(),
          error: null
        }));
      }
    } catch (error) {
      console.error('Error checking metrics status:', error);
      setStatus(prev => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Failed to check metrics status',
        lastCheck: new Date().toISOString()
      }));
    }

    checksCountRef.current++;
    
    // Stop after max checks to prevent infinite polling
    if (checksCountRef.current >= maxChecksRef.current) {
      console.log('Max polling attempts reached for release metrics');
      stopPolling();
      setStatus(prev => ({
        ...prev,
        isProcessing: false
      }));
    }
  };

  const startPolling = (maxChecks = 20, intervalMs = 2000) => {
    if (intervalRef.current) return; // Already polling

    maxChecksRef.current = maxChecks;
    checksCountRef.current = 0;
    
    setStatus(prev => ({
      ...prev,
      isProcessing: true,
      error: null
    }));

    // Check immediately
    checkMetricsStatus();

    // Then poll every intervalMs
    intervalRef.current = setInterval(checkMetricsStatus, intervalMs);
  };

  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const resetAndStartPolling = () => {
    stopPolling();
    setStatus({
      isProcessing: false,
      hasMetrics: false,
      confidenceScore: undefined,
      lastCheck: null,
      error: null
    });
    startPolling();
  };

  // Auto-start polling if requested
  useEffect(() => {
    if (releaseId && initialCheck) {
      startPolling();
    }

    return () => {
      stopPolling();
    };
  }, [releaseId, initialCheck]);

  return {
    ...status,
    startPolling,
    stopPolling,
    resetAndStartPolling,
    checkNow: checkMetricsStatus
  };
}; 