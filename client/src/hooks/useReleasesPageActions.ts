import { useState } from 'react';
import type { Release } from '../services/release';
import type { CosmosTeam } from '../services/api';
import { signoffsService } from '../services/signoffs';
import { PDFExportService } from '../services/pdfExport';
import { getReleaseSummaryEmail } from '../utils/emailTemplates';

interface UseReleasesPageActionsProps {
  teams: CosmosTeam[];
  showToast: (message: string, type: 'success' | 'error' | 'info') => void;
}

export const useReleasesPageActions = ({ teams, showToast }: UseReleasesPageActionsProps) => {
  const [pdfGenerating, setPdfGenerating] = useState<Set<string>>(new Set());

  const handlePdfDownload = async (release: Release, e: React.MouseEvent) => {
    e.stopPropagation();
    
    // Prevent multiple simultaneous downloads
    if (pdfGenerating.has(release.id)) {
      return;
    }
    
    try {
      // Set loading state
      setPdfGenerating(prev => new Set(prev).add(release.id));
      
      console.log('Generating PDF for release:', release.releaseName);
      
      // Get team information
      const team = teams.find(t => t.id === release.teamId) || null;
      
      // Get signoff status
      let signoffStatus = null;
      try {
        signoffStatus = await signoffsService.getSignoffStatus(release.id);
      } catch (error) {
        console.warn('Could not load signoff data for PDF:', error);
      }
      
      // Get real confidence metrics from API
      let confidenceMetrics = [
        { name: 'Code Quality', score: 0, label: 'SonarQube Analysis', color: 'yellow' },
        { name: 'Test Coverage', score: 0, label: 'Code Coverage', color: 'green' },
        { name: 'Test Results', score: 0, label: 'Test Success Rate', color: 'green' },
      ];
      
      try {
        const metricsResponse = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/metrics/${release.id}/stored`);
        if (metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          if (metricsData.releaseMetrics) {
            // Update with real scores
            confidenceMetrics = [
              { 
                name: 'Code Quality', 
                score: metricsData.releaseMetrics.codeQuality?.score || 0, 
                label: 'SonarQube Analysis', 
                color: (metricsData.releaseMetrics.codeQuality?.score || 0) >= 80 ? 'green' : 'yellow' 
              },
              { 
                name: 'Test Coverage', 
                score: metricsData.releaseMetrics.codeCoverage?.score || 0, 
                label: 'Code Coverage', 
                color: (metricsData.releaseMetrics.codeCoverage?.score || 0) >= 80 ? 'green' : 'yellow' 
              },
              { 
                name: 'Test Results', 
                score: metricsData.releaseMetrics.testResults?.score || 0, 
                label: 'Test Success Rate', 
                color: (metricsData.releaseMetrics.testResults?.score || 0) >= 90 ? 'green' : 'yellow' 
              },
            ];
          }
        }
      } catch (error) {
        console.warn('Could not load real metrics for PDF, using defaults:', error);
      }
      
      // Export PDF with all data
      await PDFExportService.exportReleasePDF(
        release,
        signoffStatus,
        team,
        confidenceMetrics,
        {
          includeSignoffs: true,
          includeMetrics: true,
          includeTeamInfo: true,
          includeDetailedMetrics: true,
          format: 'a4',
          orientation: 'portrait'
        }
      );
      
      console.log('PDF generated successfully');
      showToast('PDF generated successfully', 'success');
    } catch (error) {
      console.error('Error generating PDF:', error);
      showToast('Failed to generate PDF. Please try again.', 'error');
    } finally {
      // Clear loading state
      setPdfGenerating(prev => {
        const newSet = new Set(prev);
        newSet.delete(release.id);
        return newSet;
      });
    }
  };

  const handleSendEmail = async (release: Release, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const team = teams.find(t => t.id === release.teamId) || null;
      const teamName = team?.teamName || 'Unknown Team';
      const confidenceScore = release.confidenceScore || 0;
      const generatedDate = new Date().toLocaleDateString('en-US', { 
        weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', 
        hour: '2-digit', minute: '2-digit', timeZoneName: 'short' 
      });
      
      // Fetch signoff status for this release
      let signoffStatus = null;
      try {
        signoffStatus = await signoffsService.getSignoffStatus(release.id);
      } catch (error) {
        console.warn('Could not load signoff data for email:', error);
      }
      
      // Fetch PCF requests for this release
      let pcfRequests = null;
      try {
        const pcfResponse = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/pcf-requests/${release.id}`);
        if (pcfResponse.ok) {
          pcfRequests = await pcfResponse.json();
        }
      } catch (error) {
        console.warn('Could not load PCF requests for email:', error);
      }
      
      const subject = encodeURIComponent(`Release Summary: ${release.releaseName} | ${teamName} Team`);
      const body = encodeURIComponent(getReleaseSummaryEmail({
        release,
        teamName,
        signoffStatus,
        overallConfidence: confidenceScore,
        url: window.location.origin,
        releaseDetailsUrl: `${window.location.origin}/releases/${release.id}`,
        generatedDate,
        pcfRequests
      }));
      
      const mailtoLink = `mailto:?subject=${subject}&body=${body}`;
      window.open(mailtoLink, '_blank');
    } catch (error) {
      console.error('Error preparing email:', error);
      showToast('Failed to prepare email. Please try again.', 'error');
    }
  };

  return {
    pdfGenerating,
    handlePdfDownload,
    handleSendEmail
  };
}; 