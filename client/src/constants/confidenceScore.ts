/**
 * Centralized Confidence Score Constants
 * Matches server-side DEFAULT_WEIGHTINGS for consistency
 */

export const DEFAULT_WEIGHTINGS = {
  codeQuality: 20,
  codeCoverage: 20,
  testResults: 20,
  buildStability: 20,
  changeRequests: 10,
  changeLog: 10
} as const;

export interface Weightings {
  codeQuality: number;
  codeCoverage: number;
  testResults: number;
  buildStability: number;
  changeRequests: number;
  changeLog: number;
}

export interface ConfidenceMetrics {
  overallScore: number;
  lastCalculated: string;
  weightings: Weightings;
  trending: {
    direction: 'up' | 'down' | 'stable';
    changePercent: number;
    previousScore?: number;
  };
}

/**
 * Get scoring formulas for documentation/display
 */
export const getScoringFormulas = () => ({
  codeQuality: 'Score = 100 - (bugs × 2) - (vulnerabilities × 5) - coverage_penalty - duplication_penalty - (code_smells × 0.1)',
  codeCoverage: 'Score = (Overall Coverage × 0.7) + (New Code Coverage × 0.3)',
  testResults: 'Score = (Passed Tests / Total Tests) × 100',
  buildStability: 'Score = (Successful Builds / Total Builds) × 100',
  changeRequests: 'Score = 100 - min(total×2, 30) - min(open×5, 40) - min(high_priority×8, 30)',
  changeLog: 'Score = 50 + (merged_ratio × 40) + (open_ratio × 10)',
  overall: 'Weighted Sum = Σ(metric_score × weight_percentage) where weights sum to 100%'
});

/**
 * Validate weightings sum to 100
 */
export const validateWeightings = (weightings: Weightings): boolean => {
  const sum = weightings.codeQuality + weightings.codeCoverage + weightings.testResults + 
    weightings.buildStability + weightings.changeRequests + weightings.changeLog;
  return sum === 100;
}; 