export * from './api';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Environment types
export type Environment = 'development' | 'production' | 'test';

// Generic types
export interface SelectOption<T = string> {
  label: string;
  value: T;
  disabled?: boolean;
}

export interface TableColumn<T = any> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
}

export interface FilterOption {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'number';
  options?: SelectOption[];
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatarUrl?: string;
  role: 'admin' | 'user';
  createdAt: string;
  isActive: boolean;
}

// Team types (single platform per team structure)
export interface Team {
  id?: string; // Added by database
  teamName: string;
  metadata: {
    // Platform identification (required)
    platform: 'ios' | 'android' | 'web' | 'backend' | 'desktop' | 'mobile_web';
    platform_type: 'mobile' | 'web' | 'backend';
    
    // Core platform configuration (required)
    github_repo: string;
    repo_default_branch: string;
    looper_url: string;
    looper_team_id?: string; // For build stability metrics
    sonarqube_url: string;
    sonarqube_project_id: string;
    
    // Optional platform-specific fields
    deployment_url?: string; // For web and backend platforms
    app_store_url?: string; // For iOS
    play_store_url?: string; // For Android
    deployment_environment?: string; // e.g., "production", "staging"
    
    // Team communication
    slack_webhook?: string;
    
    [key: string]: any; // Allow additional metadata fields for backward compatibility
  };
  roles: {
    [roleName: string]: string[] | { [subTeamName: string]: string[] };
  };
  release_types: {
    [releaseTypeName: string]: {
      signoff_order: string[];
      enforcement: {
        [roleName: string]: {
          require_all_subteams?: boolean;
          min_signoffs_per_team?: number;
          min_signoffs?: number;
          min_total_signoffs?: number;
          [key: string]: any; // Allow additional enforcement rules
        };
      } & {
        blocking_conditions?: Array<{
          condition: string;
          description: string;
          [key: string]: any; // Allow additional condition properties
        }>;
      };
    };
  };
}

// Project types
export interface Project {
  id: string;
  name: string;
  description?: string;
  team: Team;
  status: 'active' | 'inactive' | 'archived';
  createdAt: string;
  updatedAt: string;
}

// Release types
export type ReleaseType = 'minor' | 'major' | 'patch' | 'hotfix';
export type ReleaseStatus = 'planned' | 'in_progress' | 'completed' | 'cancelled' | 'failed';
export type ReleasePriority = 'low' | 'medium' | 'high' | 'critical';

export interface Release {
  id: string;
  title: string;
  description?: string;
  type: ReleaseType;
  status: ReleaseStatus;
  priority: ReleasePriority;
  teamId: string;
  teamName?: string;
  createdBy: string;
  assignee?: string;
  plannedDate: string;
  releaseDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateReleaseData {
  title: string;
  description?: string;
  type: ReleaseType;
  priority: ReleasePriority;
  teamId: string;
  assignee?: string;
  plannedDate: string;
}

// Utility types for working with flexible team structures
export type TeamRole = string;
export type SubTeam = string;

// Helper functions for working with flexible team structures
export const TeamUtils = {
  // Get all role names from a team
  getRoles: (team: Team): string[] => {
    return Object.keys(team.roles);
  },

  // Get sub-teams for a specific role (empty array if role has no sub-teams)
  getRoleSubTeams: (team: Team, roleName: string): string[] => {
    const roleData = team.roles[roleName];
    if (Array.isArray(roleData)) {
      return []; // No sub-teams, just a flat role
    } else if (typeof roleData === 'object' && roleData !== null) {
      return Object.keys(roleData);
    }
    return [];
  },

  // Get all approvers for a role (flattened from sub-teams if applicable)
  getRoleApprovers: (team: Team, roleName: string): string[] => {
    const roleData = team.roles[roleName];
    if (Array.isArray(roleData)) {
      return roleData;
    } else if (typeof roleData === 'object' && roleData !== null) {
      return Object.values(roleData).flat();
    }
    return [];
  },

  // Check if a role has sub-teams
  roleHasSubTeams: (team: Team, roleName: string): boolean => {
    const roleData = team.roles[roleName];
    return typeof roleData === 'object' && !Array.isArray(roleData);
  },

  // Get release type names for a team
  getReleaseTypes: (team: Team): string[] => {
    return Object.keys(team.release_types);
  },

  // Get signoff order for a specific release type
  getSignoffOrder: (team: Team, releaseType: string): string[] => {
    return team.release_types[releaseType]?.signoff_order || [];
  }
};

// Metrics types
export interface MetricScore {
  score: number;
  label: string;
  lastUpdated: string;
  status: 'success' | 'warning' | 'error';
  details?: Record<string, any>;
}

export interface ConfidenceMetrics {
  overallScore: number;
  lastCalculated: string;
  weightings: {
    codeQuality: number;
    codeCoverage: number;
    testResults: number;
    buildStability: number;
    changeRequests: number;
    changeLog: number;
  };
  trending: {
    direction: 'up' | 'down' | 'stable';
    changePercent: number;
    previousScore?: number;
  };
}
