// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Common Entity Types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// User Types
export interface User extends BaseEntity {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MODERATOR = 'moderator'
}

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role?: UserRole;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Release Types
export type ClientReleaseType = 'minor' | 'major' | 'patch' | 'hotfix';

export interface Release extends BaseEntity {
  releaseName: string;
  teamId: string;
  teamName: string;
  releaseRolloutDate: string;
  releaseType: ClientReleaseType;
  releaseBranch: string;
  supportedPlatforms: {
    mobile: {
      enabled: boolean;
      ios: boolean;
      android: boolean;
    };
    web: {
      enabled: boolean;
      desktop: boolean;
      mobileWeb: boolean;
    };
    backend: {
      enabled: boolean;
    };
  };
  description?: string;
  confidenceScore?: number;
  signoffProgress?: number;
  createdBy: string;
}

export interface CreateReleaseRequest {
  releaseName: string;
  teamId: string;
  releaseRolloutDate: string;
  releaseType: ClientReleaseType;
  releaseBranch: string;
  supportedPlatforms: {
    mobile: {
      enabled: boolean;
      ios: boolean;
      android: boolean;
    };
    web: {
      enabled: boolean;
      desktop: boolean;
      mobileWeb: boolean;
    };
    backend: {
      enabled: boolean;
    };
  };
  description?: string;
}

export interface UpdateReleaseRequest {
  releaseName?: string;
  teamId?: string;
  releaseRolloutDate?: string;
  releaseType?: ClientReleaseType;
  releaseBranch?: string;
  supportedPlatforms?: {
    mobile?: {
      enabled: boolean;
      ios: boolean;
      android: boolean;
    };
    web?: {
      enabled: boolean;
      desktop: boolean;
      mobileWeb: boolean;
    };
    backend?: {
      enabled: boolean;
    };
  };
  description?: string;
}

// Team Types
export interface Team extends BaseEntity {
  id: string;
  name: string;
}

// Error Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: ValidationError[];
}
