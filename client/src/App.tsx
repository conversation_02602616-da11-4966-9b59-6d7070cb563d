import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/layout';
import { DashboardPage, ProjectsPage, HelpPage, ReleasesPage, ReleaseDetailsPage, EnvironmentsPage } from './pages';

function App() {
  return (
    <Router>
      <Routes>
        {/* Public routes */}

        {/* Protected routes with layout */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={
          <Layout>
            <DashboardPage />
          </Layout>
        } />
        <Route path="/releases" element={
          <Layout>
            <ReleasesPage />
          </Layout>
        } />
        <Route path="/releases/:releaseId" element={
          <Layout>
            <ReleaseDetailsPage />
          </Layout>
        } />
        <Route path="/environments" element={
          <Layout>
            <EnvironmentsPage />
          </Layout>
        } />
        <Route path="/projects" element={
          <Layout>
            <ProjectsPage />
          </Layout>
        } />
        <Route path="/help" element={
          <Layout>
            <HelpPage />
          </Layout>
        } />

        {/* Catch all route */}
        <Route path="*" element={
          <Layout>
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-gray-600">Page not found</p>
              </div>
            </div>
          </Layout>
        } />
      </Routes>
    </Router>
  );
}

export default App;
