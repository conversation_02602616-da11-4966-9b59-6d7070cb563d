import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { useReleaseDetails } from '../hooks/useReleaseDetails';
import { useReleaseActions } from '../hooks/useReleaseActions';
import { 
  ReleaseHeader, 
  ReleaseNavigation, 
  OverviewTab, 
  MetricsTab, 
  SignoffsTab, 
  LoadingState, 
  ErrorState 
} from '../components/release-details';
import { useToast } from '../hooks/useToast';
import { ToastNotification } from '../components/ui';
import { useAuth } from '../hooks/useAuth';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend);

const ReleaseDetailsPage: React.FC = () => {
  const { releaseId } = useParams<{ releaseId: string }>();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth(); // Use real authenticated user instead of mock
  const [selectedSubTeam, setSelectedSubTeam] = useState<string>('');
  const [approvalFormData, setApprovalFormData] = useState({
    buildSha: '',
    buildNumber: '',
    knownIssues: ''
  });

  const { toast, showToast, hideToast } = useToast();

  // Use custom hooks for state management
  const releaseDetails = useReleaseDetails(releaseId);
  
  const releaseActions = useReleaseActions({
    release: releaseDetails.release,
    teams: releaseDetails.teams,
    signoffStatus: releaseDetails.signoffStatus,
    confidenceMetrics: releaseDetails.confidenceMetrics,
    currentUser,
    selectedSubTeam,
    approvalFormData,
    setSelectedSubTeam,
    setApprovalFormData,
    loadSignoffStatus: releaseDetails.loadSignoffStatus,
    isUserAuthorizedForSubTeam: releaseDetails.isUserAuthorizedForSubTeam,
    showToast
  });

  // Find the team data for the current release
  const currentTeam = releaseDetails.release?.teamId 
    ? releaseDetails.teams.find(team => team.id === releaseDetails.release?.teamId)
    : undefined;

  // Go back to releases page
  const handleGoBack = () => {
    navigate('/releases');
  };

  const formatReleaseType = (type: string): string => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const getReleaseTypeColor = (type: string): string => {
    switch (type) {
      case 'major': return 'bg-red-100 text-red-800 border-red-200';
      case 'minor': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'patch': return 'bg-green-100 text-green-800 border-green-200';
      case 'hotfix': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleSubmitApproval = async () => {
    try {
      await releaseActions.handleSubmitApproval();
      showToast('Approval submitted successfully', 'success');
    } catch (error) {
      showToast('Failed to submit approval', 'error');
    }
  };

  const handlePdfDownload = async () => {
    try {
      await releaseActions.handlePdfDownload({ stopPropagation: () => {} } as React.MouseEvent);
    } catch (error) {
      showToast('Failed to generate PDF', 'error');
    }
  };

  const handleSendEmail = async () => {
    try {
      await releaseActions.handleSendEmail({ stopPropagation: () => {} } as React.MouseEvent);
    } catch (error) {
      showToast('Failed to send email', 'error');
    }
    };

  // Handle metric card clicks from overview
  const handleMetricClick = (metricId: string) => {
    // Navigate to metrics tab
    releaseDetails.setActiveTab('metrics');
    // Set the specific metric sub-tab
    releaseDetails.setActiveMetricsTab(metricId);
  };

  if (releaseDetails.isLoading) {
    return <LoadingState />;
  }

  if (releaseDetails.error || !releaseDetails.release) {
    return <ErrorState error={releaseDetails.error || 'Release not found'} onGoBack={handleGoBack} />;
  }

  return (
    <div className="h-full bg-gray-50">
      {/* Header */}
      <ReleaseHeader
        release={releaseDetails.release}
        signoffStatus={releaseDetails.signoffStatus}
        overallConfidence={releaseDetails.overallConfidence}
        isPdfGenerating={releaseDetails.isPdfGenerating}
        isEmailProcessing={releaseDetails.isEmailProcessing}
        onPdfDownload={handlePdfDownload}
        onEmailSend={handleSendEmail}
        onGoBack={handleGoBack}
        getTeamName={releaseDetails.getTeamName}
        formatReleaseType={formatReleaseType}
        getReleaseTypeColor={getReleaseTypeColor}
      />

      {/* Navigation */}
      <ReleaseNavigation
        activeTab={releaseDetails.activeTab}
        onTabChange={releaseDetails.setActiveTab}
      />

      {/* Content */}
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          {releaseDetails.activeTab === 'overview' && (
            <OverviewTab 
              signoffStatus={releaseDetails.signoffStatus}
              confidenceMetrics={releaseDetails.confidenceMetrics}
              overallConfidence={releaseDetails.overallConfidence}
              metricsLoading={releaseDetails.metricsLoading}
              onViewSignoffs={() => releaseDetails.setActiveTab('signoffs')}
              onViewMetrics={() => releaseDetails.setActiveTab('metrics')}
              onMetricClick={handleMetricClick}
            />
          )}

          {releaseDetails.activeTab === 'metrics' && (
            <MetricsTab 
              confidenceMetrics={releaseDetails.confidenceMetrics}
              overallConfidence={releaseDetails.overallConfidence}
              activeMetricsTab={releaseDetails.activeMetricsTab}
              setActiveMetricsTab={releaseDetails.setActiveMetricsTab}
              hoveredToolIndex={releaseDetails.hoveredToolIndex}
              setHoveredToolIndex={releaseDetails.setHoveredToolIndex}
              team={currentTeam}
              releaseId={releaseDetails.release.id}
              release={releaseDetails.release}
            />
          )}

          {releaseDetails.activeTab === 'signoffs' && (
            <SignoffsTab
              signoffLoading={releaseDetails.signoffLoading}
              signoffError={releaseDetails.signoffError}
              signoffStatus={releaseDetails.signoffStatus}
              isAuthenticated={releaseDetails.isAuthenticated}
              isSubmitting={releaseDetails.isSubmitting}
              authLoading={releaseDetails.authLoading}
              approvalFormData={approvalFormData}
              setApprovalFormData={setApprovalFormData}
              selectedSubTeam={selectedSubTeam}
              setSelectedSubTeam={setSelectedSubTeam}
              expandedApprovals={releaseDetails.expandedApprovals}
              setExpandedApprovals={releaseDetails.setExpandedApprovals}
              handleSubmitApproval={handleSubmitApproval}
              isUserAuthorizedForSubTeam={releaseDetails.isUserAuthorizedForSubTeam}
            />
          )}
        </div>
      </div>

      {/* Toast Notification */}
      <ToastNotification toast={toast} onClose={hideToast} />
    </div>
  );
};

export default ReleaseDetailsPage; 