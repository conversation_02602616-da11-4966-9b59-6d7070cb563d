import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../utils/cn';
import FloatingActionButton from '../components/ui/FloatingActionButton';
import CreateReleaseModal from '../components/modals/CreateReleaseModal';
import ReleaseCard from '../components/release/ReleaseCard';
import { releaseService, type Release, type CreateReleaseData } from '../services/release';
import { apiService, type CosmosTeam } from '../services/api';
import { useAuth } from '../hooks/useAuth';
import { parseDateSafely, formatDateLocal } from '../utils/dateUtils';

interface CalendarDay {
  date: Date;
  dayName: string;
  formattedDate: string;
  isToday: boolean;
  isCurrentMonth?: boolean;
}

const DashboardPage: React.FC = () => {
  const [viewType, setViewType] = useState<'weekly' | 'monthly'>('weekly');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [releases, setReleases] = useState<Release[]>([]);
  const [teams, setTeams] = useState<CosmosTeam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingReleases, setProcessingReleases] = useState<Set<string>>(new Set());
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load releases and teams in parallel
      const [releasesResult, teamsResult] = await Promise.allSettled([
        releaseService.getReleases(),
        apiService.getTeams()
      ]);

      if (releasesResult.status === 'fulfilled') {
        setReleases(releasesResult.value);
      }

      if (teamsResult.status === 'fulfilled' && teamsResult.value.success) {
        setTeams(teamsResult.value.data || []);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateRelease = async (formData: CreateReleaseData) => {
    try {
      const newRelease = await releaseService.createRelease(formData);
      
      // Add the new release to state immediately
      setReleases(prev => [...prev, newRelease]);
      setShowCreateModal(false);
      
      // Mark this release as processing metrics
      setProcessingReleases(prev => new Set(prev).add(newRelease.id));
      
      // Start polling for metrics completion
      const pollForMetrics = async () => {
        let attempts = 0;
        const maxAttempts = 20; // Reduced from 30 to 20 attempts
        const pollInterval = 3000; // Increased from 2000ms to 3000ms
        
        const poll = async () => {
          attempts++;
          
          try {
            // Check if metrics are available and confidence score is calculated
            const metricsResponse = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/metrics/${newRelease.id}/stored`);
            
            if (metricsResponse.ok) {
              const metricsData = await metricsResponse.json();
              
              // Check if we have the 4 core metrics
              const hasCoreMetrics = metricsData.success && 
                metricsData.data?.releaseMetrics?.codeQuality &&
                metricsData.data?.releaseMetrics?.codeCoverage &&
                metricsData.data?.releaseMetrics?.testResults &&
                metricsData.data?.releaseMetrics?.buildStability;
              
              // Only consider confidence score ready if we have core metrics AND score > 0
              const hasValidConfidenceScore = hasCoreMetrics &&
                metricsData.data?.confidenceMetrics?.overallScore !== undefined &&
                metricsData.data?.confidenceMetrics?.overallScore > 0 &&
                metricsData.data?.confidenceMetrics?.isProcessing === false;
              
              console.log('📊 Polling check:', { 
                success: metricsData.success,
                hasCodeQuality: !!metricsData.data?.releaseMetrics?.codeQuality,
                hasCodeCoverage: !!metricsData.data?.releaseMetrics?.codeCoverage,
                hasTestResults: !!metricsData.data?.releaseMetrics?.testResults,
                hasBuildStability: !!metricsData.data?.releaseMetrics?.buildStability,
                hasChangeRequests: !!metricsData.data?.releaseMetrics?.changeRequests,
                hasChangeLog: !!metricsData.data?.releaseMetrics?.changeLog,
                hasCoreMetrics,
                hasValidConfidenceScore, 
                overallScore: metricsData.data?.confidenceMetrics?.overallScore,
                isProcessing: metricsData.data?.confidenceMetrics?.isProcessing,
                attempt: attempts 
              });
              
              if (hasValidConfidenceScore) {
                // All metrics are ready and confidence score is calculated
                console.log('✅ All metrics ready for release:', newRelease.id, 'Score:', metricsData.data?.confidenceMetrics?.overallScore);
                setProcessingReleases(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(newRelease.id);
                  return newSet;
                });
                
                // Update only the specific release in the releases array with the confidence score
                setReleases(prev => prev.map(release => {
                  if (release.id === newRelease.id) {
                    return {
                      ...release,
                      confidenceMetrics: {
                        overallScore: metricsData.data.confidenceMetrics.overallScore,
                        metrics: [] // Will be populated when user clicks on the release
                      }
                    };
                  }
                  return release;
                }));
                
                return; // Stop polling
              }
            } else {
              console.warn('⚠️ Metrics API response not ok:', metricsResponse.status);
            }
            
            // Continue polling if confidence score not ready and haven't exceeded max attempts
            if (attempts < maxAttempts) {
              setTimeout(poll, pollInterval);
            } else {
              console.log('⏰ Max polling attempts reached for release:', newRelease.id);
              setProcessingReleases(prev => {
                const newSet = new Set(prev);
                newSet.delete(newRelease.id);
                return newSet;
              });
            }
          } catch (error) {
            console.warn('⚠️ Error polling for metrics (attempt', attempts, '):', error);
            if (attempts < maxAttempts) {
              setTimeout(poll, pollInterval); // Continue polling despite errors
            } else {
              console.log('❌ Max polling attempts reached after errors for release:', newRelease.id);
              setProcessingReleases(prev => {
                const newSet = new Set(prev);
                newSet.delete(newRelease.id);
                return newSet;
              });
            }
          }
        };
        
        // Start polling after a short delay to allow backend processing to begin
        setTimeout(poll, 2000); // Increased initial delay from 1000ms to 2000ms
      };
      
      pollForMetrics();
      
    } catch (error) {
      console.error('Error creating release:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleCreateReleaseClick = () => {
    if (!isAuthenticated) {
      console.warn('User must be authenticated to create releases');
      return;
    }
    setShowCreateModal(true);
  };

  const handleReleaseClick = (releaseId: string) => {
    navigate(`/releases/${releaseId}`);
  };

  const getWeekDates = (): CalendarDay[] => {
    const startDate = new Date(currentDate);
    startDate.setDate(currentDate.getDate() - currentDate.getDay() + 1); // Start from Monday
    
    return Array.from({ length: 5 }, (_, i) => {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      return {
        date: new Date(date),
        dayName: date.toLocaleDateString('en-US', { weekday: 'long' }),
        formattedDate: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        isToday: isToday(date)
      };
    });
  };

  const getMonthDates = (): CalendarDay[][] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    const weeks: CalendarDay[][] = [];
    let currentWeek: CalendarDay[] = [];
    
    // Add days from previous month to fill the first week
    const firstDayOfWeek = firstDay.getDay() === 0 ? 7 : firstDay.getDay(); // Convert Sunday (0) to 7
    for (let i = firstDayOfWeek - 1; i > 1; i--) {
      const date = new Date(year, month, 1 - i);
      if (date.getDay() !== 0 && date.getDay() !== 6) { // Exclude weekends
        currentWeek.push({
          date: new Date(date),
          dayName: date.toLocaleDateString('en-US', { weekday: 'long' }),
          formattedDate: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          isToday: isToday(date),
          isCurrentMonth: false
        });
      }
    }
    
    // Add all days of the current month
    for (let date = new Date(firstDay); date <= lastDay; date.setDate(date.getDate() + 1)) {
      if (date.getDay() !== 0 && date.getDay() !== 6) { // Exclude weekends
        if (date.getDay() === 1 && currentWeek.length > 0) { // Start new week on Monday
          weeks.push([...currentWeek]);
          currentWeek = [];
        }
        currentWeek.push({
          date: new Date(date),
          dayName: date.toLocaleDateString('en-US', { weekday: 'long' }),
          formattedDate: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          isToday: isToday(date),
          isCurrentMonth: true
        });
      }
    }
    
    // Add the last week if it has any days
    if (currentWeek.length > 0) {
      weeks.push(currentWeek);
    }
    
    return weeks;
  };

  const navigateCalendar = (direction: 'prev' | 'next') => {
    setIsTransitioning(true);
    const newDate = new Date(currentDate);
    
    if (viewType === 'weekly') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    
    setTimeout(() => {
      setCurrentDate(newDate);
      setIsTransitioning(false);
    }, 150);
  };

  const formatDateRange = (): string => {
    if (viewType === 'weekly') {
      const dates = getWeekDates();
      const start = dates[0].date;
      const end = dates[dates.length - 1].date;
      return `${start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
    } else {
      return currentDate.toLocaleDateString('en-US', { 
        month: 'long', 
        year: 'numeric'
      });
    }
  };

  const isToday = (date: Date): boolean => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  // Replace sample events with releases functionality
  const getReleasesForDate = (date: Date): Release[] => {
    const dateKey = formatDateLocal(date);
    
    return releases.filter(release => {
      const releaseDate = parseDateSafely(release.releaseRolloutDate);
      const releaseDateKey = formatDateLocal(releaseDate);
      
      return releaseDateKey === dateKey;
    });
  };

  const handleDayClick = (date: Date) => {
    if (viewType === 'monthly') {
      setCurrentDate(date);
      setViewType('weekly');
    }
  };

  const dates = viewType === 'weekly' ? getWeekDates() : getMonthDates();
  const MONTHLY_CARD_LIMIT = 2;

  // Show loading state while initial data is being fetched
  if (isLoading) {
    return (
      <div className="h-full bg-gray-50 relative">
        {/* Header - show even while loading */}
        <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-20">
          <div className="flex items-center justify-between">
            {/* Date Navigation - disabled while loading */}
            <div className="flex items-center space-x-4">
              <button
                className="p-2 bg-gray-100 rounded-lg transition-colors opacity-50 cursor-not-allowed"
                disabled
              >
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <h1 className="text-xl font-semibold text-gray-400 min-w-[200px] text-center">
                Loading...
              </h1>
              
              <button
                className="p-2 bg-gray-100 rounded-lg transition-colors opacity-50 cursor-not-allowed"
                disabled
              >
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            {/* View Toggle - disabled while loading */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                className="px-4 py-2 text-sm font-medium rounded-md bg-white text-gray-400 shadow-sm cursor-not-allowed"
                disabled
              >
                Weekly
              </button>
              <button
                className="px-4 py-2 text-sm font-medium rounded-md text-gray-400 cursor-not-allowed"
                disabled
              >
                Monthly
              </button>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="flex items-center justify-center h-[calc(100vh-100px)]">
          <div className="flex flex-col items-center space-y-6">
            {/* Enhanced loading spinner */}
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200"></div>
              <div className="absolute inset-0 animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent"></div>
            </div>
            
            {/* Loading text */}
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Dashboard</h2>
              <p className="text-gray-600">Fetching your release schedule...</p>
            </div>

            {/* Optional: Loading progress indicators */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                <span>Loading releases</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <span>Loading teams</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-50 relative">
      {/* Loading overlay */}
      {isTransitioning && (
        <div className="absolute inset-0 bg-white bg-opacity-50 z-10 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-20">
        <div className="flex items-center justify-between">
          {/* Date Navigation */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigateCalendar('prev')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              disabled={isTransitioning}
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <h1 className="text-xl font-semibold text-gray-900 min-w-[200px] text-center">
              {formatDateRange()}
            </h1>
            
            <button
              onClick={() => navigateCalendar('next')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              disabled={isTransitioning}
            >
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* View Toggle */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewType('weekly')}
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200",
                viewType === 'weekly'
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              Weekly
            </button>
            <button
              onClick={() => setViewType('monthly')}
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200",
                viewType === 'monthly'
                  ? "bg-white text-blue-600 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              Monthly
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="p-6">
        {viewType === 'weekly' ? (
          // Weekly View
        <div className="grid grid-cols-5 gap-4 h-[calc(100vh-200px)]">
            {(dates as CalendarDay[]).map((day, index) => (
            <div
                key={index}
              className={cn(
                  "bg-white rounded-xl border-2 p-4 transition-all duration-200 hover:shadow-md",
                day.isToday
                    ? "border-blue-300 bg-blue-50 shadow-md"
                  : "border-gray-200 hover:border-gray-300"
              )}
            >
              {/* Day Header */}
                <div className="text-center mb-2 pb-2 border-b border-gray-100">
                <div className={cn(
                    "text-sm font-semibold",
                    day.isToday ? "text-blue-700" : "text-gray-700"
                )}>
                  {day.dayName}
                </div>
                <div className={cn(
                    "text-lg font-bold",
                    day.isToday ? "text-blue-600" : "text-gray-900"
                )}>
                    {day.formattedDate}
                  </div>
                </div>

                {/* Events */}
                <div className="relative">
                  <div className="space-y-3 overflow-y-auto h-[calc(100vh-280px)] pb-6">
                    {getReleasesForDate(day.date).map((release) => (
                      <ReleaseCard 
                        key={release.id} 
                        release={release} 
                        teams={teams} 
                        onClick={() => handleReleaseClick(release.id)}
                        isProcessing={processingReleases.has(release.id)}
                      />
                    ))}
                    
                    {getReleasesForDate(day.date).length === 0 && (
                      <div className="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
                        <div className="relative mb-4">
                          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                            <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 100 100">
                              {/* Calendar body */}
                              <rect x="20" y="30" width="45" height="40" rx="3" fill="currentColor"/>
                              <rect x="22" y="35" width="41" height="33" rx="2" fill="#dc2626"/>
                              
                              {/* Calendar tabs */}
                              <rect x="28" y="20" width="4" height="20" rx="2" fill="currentColor"/>
                              <rect x="53" y="20" width="4" height="20" rx="2" fill="currentColor"/>
                              
                              {/* Calendar grid */}
                              <rect x="25" y="40" width="6" height="4" fill="currentColor"/>
                              <rect x="33" y="40" width="6" height="4" fill="currentColor"/>
                              <rect x="41" y="40" width="6" height="4" fill="currentColor"/>
                              <rect x="49" y="40" width="6" height="4" fill="currentColor"/>
                              
                              <rect x="25" y="47" width="6" height="4" fill="currentColor"/>
                              <rect x="33" y="47" width="6" height="4" fill="currentColor"/>
                              <rect x="41" y="47" width="6" height="4" fill="currentColor"/>
                              <rect x="49" y="47" width="6" height="4" fill="currentColor"/>
                              
                              <rect x="25" y="54" width="6" height="4" fill="currentColor"/>
                              <rect x="33" y="54" width="6" height="4" fill="currentColor"/>
                              <rect x="41" y="54" width="6" height="4" fill="currentColor"/>
                              <rect x="49" y="54" width="6" height="4" fill="currentColor"/>
                              
                              {/* Clock circle */}
                              <circle cx="65" cy="60" r="18" fill="currentColor"/>
                              <circle cx="65" cy="60" r="15" fill="#dc2626"/>
                              
                              {/* Clock hands */}
                              <line x1="65" y1="60" x2="65" y2="50" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                              <line x1="65" y1="60" x2="72" y2="60" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                              
                              {/* Clock center dot */}
                              <circle cx="65" cy="60" r="2" fill="currentColor"/>
                              
                              {/* Clock markers */}
                              <circle cx="65" cy="47" r="1" fill="currentColor"/>
                              <circle cx="65" cy="73" r="1" fill="currentColor"/>
                              <circle cx="78" cy="60" r="1" fill="currentColor"/>
                              <circle cx="52" cy="60" r="1" fill="currentColor"/>
                            </svg>
                          </div>
                        </div>
                        <h3 className="text-sm font-semibold text-gray-700 mb-1">No releases scheduled</h3>
                        <p className="text-xs text-gray-500 leading-relaxed">
                          Click the + button to plan a release
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Scroll indicator - shows when there are more than 2 releases */}
                  {getReleasesForDate(day.date).length > 2 && (
                    <div className="absolute bottom-0 left-0 right-0 h-10 bg-gradient-to-t from-white via-white/90 to-transparent pointer-events-none flex items-end justify-center pb-2">
                      <div className="flex items-center space-x-1 text-xs text-gray-600 bg-white/80 backdrop-blur-sm px-2 py-1 rounded-full border border-gray-200 shadow-sm">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        <span className="font-medium">Scroll for more</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Monthly View
          <div className="space-y-4">
            {(dates as CalendarDay[][]).map((week, weekIndex) => (
              <div key={weekIndex} className="grid grid-cols-5 gap-4">
                {week.map((day, dayIndex) => (
                  <div
                    key={`${weekIndex}-${dayIndex}`}
                    className={cn(
                      "bg-white rounded-xl border-2 p-4 transition-all duration-200 hover:shadow-md cursor-pointer",
                      day.isToday
                        ? "border-blue-300 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-gray-300",
                      !day.isCurrentMonth && "opacity-50"
                    )}
                    onClick={() => handleDayClick(day.date)}
                  >
                    {/* Day Header */}
                    <div className="text-center mb-2 pb-2 border-b border-gray-100">
                      <div className={cn(
                        "text-sm font-medium",
                        day.isToday ? "text-blue-700" : "text-gray-700"
                      )}>
                        {day.dayName}
                      </div>
                      <div className={cn(
                        "text-lg font-bold",
                        day.isToday ? "text-blue-600" : "text-gray-900"
                      )}>
                        {day.formattedDate}
                      </div>
                    </div>

                    {/* Events Preview */}
                    <div className="space-y-1">
                      {getReleasesForDate(day.date).slice(0, MONTHLY_CARD_LIMIT).map((release) => (
                        <ReleaseCard
                          key={release.id}
                          release={release}
                          teams={teams}
                          compact={true}
                          onClick={() => handleReleaseClick(release.id)}
                          isProcessing={processingReleases.has(release.id)}
                        />
                      ))}
                
                      {getReleasesForDate(day.date).length > MONTHLY_CARD_LIMIT && (
                        <div className="text-xs text-gray-500 font-medium text-center py-1">
                          +{getReleasesForDate(day.date).length - MONTHLY_CARD_LIMIT} more
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
            ))}
          </div>
        )}
      </div>

      {/* Floating Action Button */}
      {isAuthenticated && (
        <FloatingActionButton
          singleAction={true}
          actionIcon="calendar-timer"
          actionLabel="Create Release"
          onCreateRelease={handleCreateReleaseClick}
        />
      )}

      {/* Create Release Modal */}
      {isAuthenticated && (
        <CreateReleaseModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateRelease}
          teams={teams.map(team => ({ id: team.id, name: team.teamName }))}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default DashboardPage;
