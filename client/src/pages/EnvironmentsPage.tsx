import React, { useState, useEffect } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

interface Environment {
  id: string;
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  errors24h: number;
  responseTime: number;
  version: string;
  lastDeploy: string;
}

interface EnvironmentEvent {
  id: string;
  timestamp: string;
  environment: string;
  eventType: 'deployment' | 'alert' | 'incident' | 'maintenance';
  status: 'success' | 'warning' | 'resolved' | 'scheduled';
  details: string;
}

// Golden Flow interfaces
interface GoldenFlow {
  id: string;
  name: string;
  functionality: string;
  environment: string;
  status: 'passing' | 'failing' | 'warning' | 'running';
  lastRun: string;
  duration: number;
  successRate: number;
  description: string;
  steps: GoldenFlowStep[];
  trend: 'up' | 'down' | 'stable';
}

interface GoldenFlowStep {
  id: string;
  name: string;
  status: 'passed' | 'failed' | 'warning';
  duration: number;
  error?: string;
}

interface FunctionalityFilter {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
}

const EnvironmentsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'stage' | 'teflon' | 'production'>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [environments, setEnvironments] = useState<Environment[]>([]);
  const [recentEvents, setRecentEvents] = useState<EnvironmentEvent[]>([]);
  const [goldenFlows, setGoldenFlows] = useState<GoldenFlow[]>([]);
  const [selectedFunctionality, setSelectedFunctionality] = useState<string>('login');

  // Define functionality filters
  const functionalityFilters: FunctionalityFilter[] = [
    { id: 'all', name: 'All Flows', icon: '🔄', color: 'bg-gray-100 text-gray-800', description: 'All golden flows' },
    { id: 'login', name: 'Login', icon: '🔐', color: 'bg-blue-100 text-blue-800', description: 'User authentication flows' },
    { id: 'signup', name: 'Signup', icon: '📝', color: 'bg-green-100 text-green-800', description: 'User registration flows' },
    { id: 'order', name: 'Order Flow', icon: '🛒', color: 'bg-purple-100 text-purple-800', description: 'End-to-end order process' },
    { id: 'checkout', name: 'Checkout', icon: '💳', color: 'bg-orange-100 text-orange-800', description: 'Payment and checkout flows' },
    { id: 'search', name: 'Search', icon: '🔍', color: 'bg-yellow-100 text-yellow-800', description: 'Product search functionality' },
    { id: 'plp', name: 'PLP', icon: '📋', color: 'bg-indigo-100 text-indigo-800', description: 'Product listing page flows' },
    { id: 'pdp', name: 'PDP', icon: '📄', color: 'bg-pink-100 text-pink-800', description: 'Product detail page flows' },
    { id: 'lists', name: 'Lists', icon: '📝', color: 'bg-teal-100 text-teal-800', description: 'User list management' },
  ];

  // Mock data
  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setEnvironments([
        {
          id: 'stage',
          name: 'Stage',
          status: 'healthy',
          uptime: 99.8,
          errors24h: 3,
          responseTime: 185,
          version: 'v2.1.4',
          lastDeploy: '2h ago'
        },
        {
          id: 'teflon',
          name: 'Teflon',
          status: 'warning',
          uptime: 97.2,
          errors24h: 24,
          responseTime: 312,
          version: 'v2.0.8',
          lastDeploy: '6h ago'
        },
        {
          id: 'production',
          name: 'Production',
          status: 'healthy',
          uptime: 99.95,
          errors24h: 1,
          responseTime: 142,
          version: 'v2.3.1',
          lastDeploy: '1d ago'
        }
      ]);

      // Mock Golden Flows data
      setGoldenFlows([
        // Login flows - across all environments
        {
          id: 'login-prod',
          name: 'User Login Flow',
          functionality: 'login',
          environment: 'production',
          status: 'passing',
          lastRun: '2 minutes ago',
          duration: 2.8,
          successRate: 99.2,
          description: 'Standard user authentication flow',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load login page', status: 'passed', duration: 0.6 },
            { id: '2', name: 'Validate credentials', status: 'passed', duration: 1.2 },
            { id: '3', name: 'Redirect to dashboard', status: 'passed', duration: 1.0 }
          ]
        },
        {
          id: 'login-teflon',
          name: 'SSO Login Flow',
          functionality: 'login',
          environment: 'teflon',
          status: 'failing',
          lastRun: '8 minutes ago',
          duration: 15.7,
          successRate: 65.4,
          description: 'Single Sign-On authentication',
          trend: 'down',
          steps: [
            { id: '1', name: 'Load login page', status: 'passed', duration: 1.2 },
            { id: '2', name: 'SSO authentication', status: 'failed', duration: 12.0, error: 'Timeout waiting for SSO' },
            { id: '3', name: 'Return to app', status: 'failed', duration: 2.5 }
          ]
        },
        {
          id: 'login-stage',
          name: 'User Login Flow',
          functionality: 'login',
          environment: 'stage',
          status: 'passing',
          lastRun: '1 minute ago',
          duration: 3.2,
          successRate: 97.8,
          description: 'Standard user authentication flow',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load login page', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Validate credentials', status: 'passed', duration: 1.4 },
            { id: '3', name: 'Redirect to dashboard', status: 'passed', duration: 1.0 }
          ]
        },

        // Order flows - across all environments
        {
          id: 'order-prod',
          name: 'Complete Order Journey',
          functionality: 'order',
          environment: 'production',
          status: 'passing',
          lastRun: '5 minutes ago',
          duration: 45.3,
          successRate: 94.7,
          description: 'End-to-end order placement',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Browse products', status: 'passed', duration: 8.2 },
            { id: '2', name: 'Add to cart', status: 'passed', duration: 2.1 },
            { id: '3', name: 'Checkout process', status: 'passed', duration: 18.0 },
            { id: '4', name: 'Order confirmation', status: 'passed', duration: 17.0 }
          ]
        },
        {
          id: 'order-teflon',
          name: 'Complete Order Journey',
          functionality: 'order',
          environment: 'teflon',
          status: 'warning',
          lastRun: '3 minutes ago',
          duration: 52.1,
          successRate: 89.2,
          description: 'End-to-end order placement',
          trend: 'down',
          steps: [
            { id: '1', name: 'Browse products', status: 'passed', duration: 9.1 },
            { id: '2', name: 'Add to cart', status: 'passed', duration: 2.8 },
            { id: '3', name: 'Checkout process', status: 'warning', duration: 22.2 },
            { id: '4', name: 'Order confirmation', status: 'passed', duration: 18.0 }
          ]
        },
        {
          id: 'order-stage',
          name: 'Complete Order Journey',
          functionality: 'order',
          environment: 'stage',
          status: 'passing',
          lastRun: '4 minutes ago',
          duration: 48.6,
          successRate: 96.1,
          description: 'End-to-end order placement',
          trend: 'up',
          steps: [
            { id: '1', name: 'Browse products', status: 'passed', duration: 8.8 },
            { id: '2', name: 'Add to cart', status: 'passed', duration: 2.3 },
            { id: '3', name: 'Checkout process', status: 'passed', duration: 19.5 },
            { id: '4', name: 'Order confirmation', status: 'passed', duration: 18.0 }
          ]
        },

        // Search flows - across all environments
        {
          id: 'search-prod',
          name: 'Product Search',
          functionality: 'search',
          environment: 'production',
          status: 'passing',
          lastRun: '1 minute ago',
          duration: 1.8,
          successRate: 99.1,
          description: 'Product search functionality',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Enter search term', status: 'passed', duration: 0.3 },
            { id: '2', name: 'Execute search', status: 'passed', duration: 0.9 },
            { id: '3', name: 'Display results', status: 'passed', duration: 0.6 }
          ]
        },
        {
          id: 'search-advanced-prod',
          name: 'Advanced Search',
          functionality: 'search',
          environment: 'production',
          status: 'passing',
          lastRun: '3 minutes ago',
          duration: 2.4,
          successRate: 97.8,
          description: 'Advanced search with filters',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load filters', status: 'passed', duration: 0.5 },
            { id: '2', name: 'Apply filters', status: 'passed', duration: 1.2 },
            { id: '3', name: 'Execute search', status: 'passed', duration: 0.7 }
          ]
        },
        {
          id: 'search-teflon',
          name: 'Product Search',
          functionality: 'search',
          environment: 'teflon',
          status: 'warning',
          lastRun: '6 minutes ago',
          duration: 3.2,
          successRate: 91.4,
          description: 'Product search functionality',
          trend: 'down',
          steps: [
            { id: '1', name: 'Enter search term', status: 'passed', duration: 0.4 },
            { id: '2', name: 'Execute search', status: 'warning', duration: 2.1 },
            { id: '3', name: 'Display results', status: 'passed', duration: 0.7 }
          ]
        },
        {
          id: 'search-autocomplete-teflon',
          name: 'Search Autocomplete',
          functionality: 'search',
          environment: 'teflon',
          status: 'failing',
          lastRun: '4 minutes ago',
          duration: 5.8,
          successRate: 78.2,
          description: 'Search suggestion functionality',
          trend: 'down',
          steps: [
            { id: '1', name: 'Start typing', status: 'passed', duration: 0.2 },
            { id: '2', name: 'Fetch suggestions', status: 'failed', duration: 4.8, error: 'API timeout' },
            { id: '3', name: 'Display suggestions', status: 'failed', duration: 0.8 }
          ]
        },
        {
          id: 'search-stage',
          name: 'Product Search',
          functionality: 'search',
          environment: 'stage',
          status: 'passing',
          lastRun: '2 minutes ago',
          duration: 2.1,
          successRate: 98.3,
          description: 'Product search functionality',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Enter search term', status: 'passed', duration: 0.3 },
            { id: '2', name: 'Execute search', status: 'passed', duration: 1.2 },
            { id: '3', name: 'Display results', status: 'passed', duration: 0.6 }
          ]
        },
        {
          id: 'search-voice-stage',
          name: 'Voice Search',
          functionality: 'search',
          environment: 'stage',
          status: 'warning',
          lastRun: '7 minutes ago',
          duration: 4.1,
          successRate: 87.5,
          description: 'Voice-activated search',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Enable microphone', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Process voice', status: 'warning', duration: 2.6 },
            { id: '3', name: 'Execute search', status: 'passed', duration: 0.7 }
          ]
        },

        // Login flows - additional variants
        {
          id: 'login-social-prod',
          name: 'Social Media Login',
          functionality: 'login',
          environment: 'production',
          status: 'passing',
          lastRun: '4 minutes ago',
          duration: 3.5,
          successRate: 96.8,
          description: 'Social media authentication',
          trend: 'up',
          steps: [
            { id: '1', name: 'Choose provider', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Redirect to provider', status: 'passed', duration: 1.4 },
            { id: '3', name: 'Return with token', status: 'passed', duration: 1.3 }
          ]
        },
        {
          id: 'login-2fa-teflon',
          name: '2FA Login',
          functionality: 'login',
          environment: 'teflon',
          status: 'warning',
          lastRun: '5 minutes ago',
          duration: 8.2,
          successRate: 92.1,
          description: 'Two-factor authentication',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Enter credentials', status: 'passed', duration: 1.5 },
            { id: '2', name: 'Send 2FA code', status: 'warning', duration: 4.2 },
            { id: '3', name: 'Verify code', status: 'passed', duration: 2.5 }
          ]
        },
        {
          id: 'login-biometric-stage',
          name: 'Biometric Login',
          functionality: 'login',
          environment: 'stage',
          status: 'running',
          lastRun: '30 seconds ago',
          duration: 1.8,
          successRate: 94.2,
          description: 'Fingerprint/face recognition',
          trend: 'up',
          steps: [
            { id: '1', name: 'Request biometric', status: 'passed', duration: 0.3 },
            { id: '2', name: 'Scan biometric', status: 'passed', duration: 1.1 },
            { id: '3', name: 'Authenticate', status: 'passed', duration: 0.4 }
          ]
        },

        // Order flows - additional variants
        {
          id: 'order-guest-prod',
          name: 'Guest Checkout Order',
          functionality: 'order',
          environment: 'production',
          status: 'passing',
          lastRun: '2 minutes ago',
          duration: 38.7,
          successRate: 91.3,
          description: 'Order without account creation',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Add items to cart', status: 'passed', duration: 5.2 },
            { id: '2', name: 'Guest information', status: 'passed', duration: 12.8 },
            { id: '3', name: 'Payment process', status: 'passed', duration: 15.2 },
            { id: '4', name: 'Order confirmation', status: 'passed', duration: 5.5 }
          ]
        },
        {
          id: 'order-subscription-teflon',
          name: 'Subscription Order',
          functionality: 'order',
          environment: 'teflon',
          status: 'failing',
          lastRun: '9 minutes ago',
          duration: 62.4,
          successRate: 73.8,
          description: 'Recurring subscription setup',
          trend: 'down',
          steps: [
            { id: '1', name: 'Select subscription', status: 'passed', duration: 8.4 },
            { id: '2', name: 'Setup billing cycle', status: 'warning', duration: 18.2 },
            { id: '3', name: 'Process payment', status: 'failed', duration: 28.8, error: 'Payment gateway error' },
            { id: '4', name: 'Activate subscription', status: 'failed', duration: 7.0 }
          ]
        },
        {
          id: 'order-bulk-stage',
          name: 'Bulk Order',
          functionality: 'order',
          environment: 'stage',
          status: 'passing',
          lastRun: '6 minutes ago',
          duration: 67.2,
          successRate: 88.9,
          description: 'Large quantity orders',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Upload CSV', status: 'passed', duration: 15.8 },
            { id: '2', name: 'Validate items', status: 'passed', duration: 22.4 },
            { id: '3', name: 'Calculate pricing', status: 'passed', duration: 18.7 },
            { id: '4', name: 'Process order', status: 'passed', duration: 10.3 }
          ]
        },

        // Checkout flows - across all environments
        {
          id: 'checkout-prod',
          name: 'Express Checkout',
          functionality: 'checkout',
          environment: 'production',
          status: 'passing',
          lastRun: '4 minutes ago',
          duration: 8.9,
          successRate: 97.2,
          description: 'Fast checkout for returning customers',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Review cart', status: 'passed', duration: 1.8 },
            { id: '2', name: 'Payment processing', status: 'passed', duration: 5.1 },
            { id: '3', name: 'Order confirmation', status: 'passed', duration: 2.0 }
          ]
        },
        {
          id: 'checkout-saved-payment-prod',
          name: 'Saved Payment Checkout',
          functionality: 'checkout',
          environment: 'production',
          status: 'passing',
          lastRun: '1 minute ago',
          duration: 6.2,
          successRate: 98.8,
          description: 'Checkout with saved payment methods',
          trend: 'up',
          steps: [
            { id: '1', name: 'Load saved cards', status: 'passed', duration: 1.2 },
            { id: '2', name: 'Select payment', status: 'passed', duration: 2.8 },
            { id: '3', name: 'Complete purchase', status: 'passed', duration: 2.2 }
          ]
        },
        {
          id: 'checkout-teflon',
          name: 'Express Checkout',
          functionality: 'checkout',
          environment: 'teflon',
          status: 'warning',
          lastRun: '7 minutes ago',
          duration: 14.2,
          successRate: 88.6,
          description: 'Fast checkout for returning customers',
          trend: 'down',
          steps: [
            { id: '1', name: 'Review cart', status: 'passed', duration: 2.3 },
            { id: '2', name: 'Payment processing', status: 'warning', duration: 9.8 },
            { id: '3', name: 'Order confirmation', status: 'passed', duration: 2.1 }
          ]
        },
        {
          id: 'checkout-wallet-teflon',
          name: 'Digital Wallet Checkout',
          functionality: 'checkout',
          environment: 'teflon',
          status: 'failing',
          lastRun: '12 minutes ago',
          duration: 18.7,
          successRate: 76.3,
          description: 'Apple Pay / Google Pay checkout',
          trend: 'down',
          steps: [
            { id: '1', name: 'Detect wallet', status: 'passed', duration: 1.8 },
            { id: '2', name: 'Authorize payment', status: 'failed', duration: 14.2, error: 'Wallet service timeout' },
            { id: '3', name: 'Complete order', status: 'failed', duration: 2.7 }
          ]
        },
        {
          id: 'checkout-stage',
          name: 'Express Checkout',
          functionality: 'checkout',
          environment: 'stage',
          status: 'passing',
          lastRun: '3 minutes ago',
          duration: 9.5,
          successRate: 95.7,
          description: 'Fast checkout for returning customers',
          trend: 'up',
          steps: [
            { id: '1', name: 'Review cart', status: 'passed', duration: 2.1 },
            { id: '2', name: 'Payment processing', status: 'passed', duration: 5.4 },
            { id: '3', name: 'Order confirmation', status: 'passed', duration: 2.0 }
          ]
        },
        {
          id: 'checkout-installments-stage',
          name: 'Installment Checkout',
          functionality: 'checkout',
          environment: 'stage',
          status: 'warning',
          lastRun: '8 minutes ago',
          duration: 15.3,
          successRate: 89.4,
          description: 'Buy now, pay later checkout',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Check eligibility', status: 'passed', duration: 4.2 },
            { id: '2', name: 'Setup payment plan', status: 'warning', duration: 8.1 },
            { id: '3', name: 'Finalize order', status: 'passed', duration: 3.0 }
          ]
        },

        // PLP flows - across all environments
        {
          id: 'plp-prod',
          name: 'Category Page Load',
          functionality: 'plp',
          environment: 'production',
          status: 'passing',
          lastRun: '2 minutes ago',
          duration: 2.1,
          successRate: 98.9,
          description: 'Product listing page performance',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load category', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Render products', status: 'passed', duration: 0.9 },
            { id: '3', name: 'Load pagination', status: 'passed', duration: 0.4 }
          ]
        },
        {
          id: 'plp-filter-prod',
          name: 'Filter Products',
          functionality: 'plp',
          environment: 'production',
          status: 'passing',
          lastRun: '5 minutes ago',
          duration: 3.4,
          successRate: 97.2,
          description: 'Product filtering functionality',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load filters', status: 'passed', duration: 1.1 },
            { id: '2', name: 'Apply filters', status: 'passed', duration: 1.8 },
            { id: '3', name: 'Update results', status: 'passed', duration: 0.5 }
          ]
        },
        {
          id: 'plp-teflon',
          name: 'Category Page Load',
          functionality: 'plp',
          environment: 'teflon',
          status: 'warning',
          lastRun: '5 minutes ago',
          duration: 4.2,
          successRate: 92.1,
          description: 'Product listing page performance',
          trend: 'down',
          steps: [
            { id: '1', name: 'Load category', status: 'passed', duration: 1.2 },
            { id: '2', name: 'Render products', status: 'warning', duration: 2.4 },
            { id: '3', name: 'Load pagination', status: 'passed', duration: 0.6 }
          ]
        },
        {
          id: 'plp-sort-teflon',
          name: 'Sort Products',
          functionality: 'plp',
          environment: 'teflon',
          status: 'warning',
          lastRun: '9 minutes ago',
          duration: 5.8,
          successRate: 85.7,
          description: 'Product sorting functionality',
          trend: 'down',
          steps: [
            { id: '1', name: 'Select sort option', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Reorder products', status: 'warning', duration: 4.2 },
            { id: '3', name: 'Update display', status: 'passed', duration: 0.8 }
          ]
        },
        {
          id: 'plp-stage',
          name: 'Category Page Load',
          functionality: 'plp',
          environment: 'stage',
          status: 'passing',
          lastRun: '1 minute ago',
          duration: 2.3,
          successRate: 97.8,
          description: 'Product listing page performance',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load category', status: 'passed', duration: 0.9 },
            { id: '2', name: 'Render products', status: 'passed', duration: 1.0 },
            { id: '3', name: 'Load pagination', status: 'passed', duration: 0.4 }
          ]
        },

        // Signup flows - across all environments
        {
          id: 'signup-prod',
          name: 'User Registration',
          functionality: 'signup',
          environment: 'production',
          status: 'passing',
          lastRun: '3 minutes ago',
          duration: 4.8,
          successRate: 96.4,
          description: 'New user signup process',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load signup form', status: 'passed', duration: 0.9 },
            { id: '2', name: 'Validate input', status: 'passed', duration: 1.6 },
            { id: '3', name: 'Create account', status: 'passed', duration: 2.3 }
          ]
        },
        {
          id: 'signup-social-prod',
          name: 'Social Signup',
          functionality: 'signup',
          environment: 'production',
          status: 'passing',
          lastRun: '6 minutes ago',
          duration: 3.2,
          successRate: 98.1,
          description: 'Social media account creation',
          trend: 'up',
          steps: [
            { id: '1', name: 'Choose provider', status: 'passed', duration: 0.7 },
            { id: '2', name: 'Authorize access', status: 'passed', duration: 1.8 },
            { id: '3', name: 'Create profile', status: 'passed', duration: 0.7 }
          ]
        },
        {
          id: 'signup-teflon',
          name: 'User Registration',
          functionality: 'signup',
          environment: 'teflon',
          status: 'warning',
          lastRun: '9 minutes ago',
          duration: 7.2,
          successRate: 88.7,
          description: 'New user signup process',
          trend: 'down',
          steps: [
            { id: '1', name: 'Load signup form', status: 'passed', duration: 1.1 },
            { id: '2', name: 'Validate input', status: 'warning', duration: 3.8 },
            { id: '3', name: 'Create account', status: 'passed', duration: 2.3 }
          ]
        },
        {
          id: 'signup-email-verification-teflon',
          name: 'Email Verification',
          functionality: 'signup',
          environment: 'teflon',
          status: 'failing',
          lastRun: '15 minutes ago',
          duration: 12.4,
          successRate: 72.8,
          description: 'Email verification flow',
          trend: 'down',
          steps: [
            { id: '1', name: 'Send verification email', status: 'warning', duration: 6.2 },
            { id: '2', name: 'Click verification link', status: 'failed', duration: 4.8, error: 'Email service down' },
            { id: '3', name: 'Activate account', status: 'failed', duration: 1.4 }
          ]
        },
        {
          id: 'signup-stage',
          name: 'User Registration',
          functionality: 'signup',
          environment: 'stage',
          status: 'passing',
          lastRun: '1 minute ago',
          duration: 4.2,
          successRate: 98.1,
          description: 'New user signup process',
          trend: 'up',
          steps: [
            { id: '1', name: 'Load signup form', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Validate input', status: 'passed', duration: 1.4 },
            { id: '3', name: 'Create account', status: 'passed', duration: 2.0 }
          ]
        },

        // PDP flows - across all environments
        {
          id: 'pdp-prod',
          name: 'Product Details Load',
          functionality: 'pdp',
          environment: 'production',
          status: 'passing',
          lastRun: '4 minutes ago',
          duration: 3.1,
          successRate: 97.6,
          description: 'Product detail page rendering',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load product data', status: 'passed', duration: 1.2 },
            { id: '2', name: 'Render images', status: 'passed', duration: 1.4 },
            { id: '3', name: 'Load reviews', status: 'passed', duration: 0.5 }
          ]
        },
        {
          id: 'pdp-recommendations-prod',
          name: 'Product Recommendations',
          functionality: 'pdp',
          environment: 'production',
          status: 'passing',
          lastRun: '2 minutes ago',
          duration: 2.8,
          successRate: 96.3,
          description: 'Related product suggestions',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Analyze product', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Fetch recommendations', status: 'passed', duration: 1.4 },
            { id: '3', name: 'Render suggestions', status: 'passed', duration: 0.6 }
          ]
        },
        {
          id: 'pdp-teflon',
          name: 'Product Details Load',
          functionality: 'pdp',
          environment: 'teflon',
          status: 'warning',
          lastRun: '4 minutes ago',
          duration: 8.7,
          successRate: 88.9,
          description: 'Product detail page rendering',
          trend: 'down',
          steps: [
            { id: '1', name: 'Load product data', status: 'passed', duration: 2.1 },
            { id: '2', name: 'Render images', status: 'warning', duration: 5.2 },
            { id: '3', name: 'Load reviews', status: 'passed', duration: 1.4 }
          ]
        },
        {
          id: 'pdp-360-view-teflon',
          name: '360° Product View',
          functionality: 'pdp',
          environment: 'teflon',
          status: 'failing',
          lastRun: '11 minutes ago',
          duration: 12.8,
          successRate: 68.4,
          description: 'Interactive 360-degree product view',
          trend: 'down',
          steps: [
            { id: '1', name: 'Load 360 assets', status: 'warning', duration: 4.2 },
            { id: '2', name: 'Initialize viewer', status: 'failed', duration: 6.8, error: '3D viewer timeout' },
            { id: '3', name: 'Enable interaction', status: 'failed', duration: 1.8 }
          ]
        },
        {
          id: 'pdp-stage',
          name: 'Product Details Load',
          functionality: 'pdp',
          environment: 'stage',
          status: 'passing',
          lastRun: '2 minutes ago',
          duration: 3.8,
          successRate: 95.2,
          description: 'Product detail page rendering',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load product data', status: 'passed', duration: 1.4 },
            { id: '2', name: 'Render images', status: 'passed', duration: 1.8 },
            { id: '3', name: 'Load reviews', status: 'passed', duration: 0.6 }
          ]
        },
        {
          id: 'pdp-compare-stage',
          name: 'Product Comparison',
          functionality: 'pdp',
          environment: 'stage',
          status: 'warning',
          lastRun: '8 minutes ago',
          duration: 5.6,
          successRate: 89.7,
          description: 'Side-by-side product comparison',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load comparison data', status: 'passed', duration: 2.1 },
            { id: '2', name: 'Generate comparison table', status: 'warning', duration: 2.8 },
            { id: '3', name: 'Render side-by-side view', status: 'passed', duration: 0.7 }
          ]
        },

        // Lists flows - across all environments
        {
          id: 'lists-prod',
          name: 'User List Management',
          functionality: 'lists',
          environment: 'production',
          status: 'passing',
          lastRun: '6 minutes ago',
          duration: 2.4,
          successRate: 98.8,
          description: 'User list creation and management',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load user lists', status: 'passed', duration: 0.8 },
            { id: '2', name: 'Create new list', status: 'passed', duration: 1.1 },
            { id: '3', name: 'Save changes', status: 'passed', duration: 0.5 }
          ]
        },
        {
          id: 'lists-wishlist-prod',
          name: 'Wishlist Management',
          functionality: 'lists',
          environment: 'production',
          status: 'passing',
          lastRun: '3 minutes ago',
          duration: 1.8,
          successRate: 99.1,
          description: 'Wishlist add/remove functionality',
          trend: 'up',
          steps: [
            { id: '1', name: 'Load wishlist', status: 'passed', duration: 0.6 },
            { id: '2', name: 'Add/remove items', status: 'passed', duration: 0.8 },
            { id: '3', name: 'Sync changes', status: 'passed', duration: 0.4 }
          ]
        },
        {
          id: 'lists-teflon',
          name: 'User List Management',
          functionality: 'lists',
          environment: 'teflon',
          status: 'passing',
          lastRun: '8 minutes ago',
          duration: 3.1,
          successRate: 94.3,
          description: 'User list creation and management',
          trend: 'stable',
          steps: [
            { id: '1', name: 'Load user lists', status: 'passed', duration: 1.0 },
            { id: '2', name: 'Create new list', status: 'passed', duration: 1.5 },
            { id: '3', name: 'Save changes', status: 'passed', duration: 0.6 }
          ]
        },
        {
          id: 'lists-sharing-teflon',
          name: 'List Sharing',
          functionality: 'lists',
          environment: 'teflon',
          status: 'warning',
          lastRun: '14 minutes ago',
          duration: 6.7,
          successRate: 82.5,
          description: 'Share lists with other users',
          trend: 'down',
          steps: [
            { id: '1', name: 'Generate share link', status: 'passed', duration: 1.8 },
            { id: '2', name: 'Send notification', status: 'warning', duration: 3.6 },
            { id: '3', name: 'Update permissions', status: 'passed', duration: 1.3 }
          ]
        },
        {
          id: 'lists-stage',
          name: 'User List Management',
          functionality: 'lists',
          environment: 'stage',
          status: 'passing',
          lastRun: '3 minutes ago',
          duration: 2.6,
          successRate: 97.1,
          description: 'User list creation and management',
          trend: 'up',
          steps: [
            { id: '1', name: 'Load user lists', status: 'passed', duration: 0.9 },
            { id: '2', name: 'Create new list', status: 'passed', duration: 1.2 },
            { id: '3', name: 'Save changes', status: 'passed', duration: 0.5 }
          ]
        },
        {
          id: 'lists-collaborative-stage',
          name: 'Collaborative Lists',
          functionality: 'lists',
          environment: 'stage',
          status: 'running',
          lastRun: '45 seconds ago',
          duration: 4.2,
          successRate: 91.8,
          description: 'Real-time collaborative list editing',
          trend: 'up',
          steps: [
            { id: '1', name: 'Establish connection', status: 'passed', duration: 1.2 },
            { id: '2', name: 'Sync changes', status: 'passed', duration: 2.1 },
            { id: '3', name: 'Handle conflicts', status: 'passed', duration: 0.9 }
          ]
        }
      ]);

      setRecentEvents([
        {
          id: '1',
          timestamp: '06/21/2025, 13:18:08',
          environment: 'Production',
          eventType: 'deployment',
          status: 'success',
          details: 'v2.3.1 deployed successfully'
        },
        {
          id: '2',
          timestamp: '06/21/2025, 11:18:08',
          environment: 'Teflon',
          eventType: 'alert',
          status: 'warning',
          details: 'High error rate detected (24 errors in 1 hour)'
        },
        {
          id: '3',
          timestamp: '06/21/2025, 07:18:08',
          environment: 'Stage',
          eventType: 'deployment',
          status: 'success',
          details: 'v2.1.4 deployed to staging environment'
        },
        {
          id: '4',
          timestamp: '06/20/2025, 21:18:08',
          environment: 'Teflon',
          eventType: 'incident',
          status: 'resolved',
          details: 'Database connection timeout resolved after 15 minutes'
        },
        {
          id: '5',
          timestamp: '06/20/2025, 09:18:08',
          environment: 'Production',
          eventType: 'maintenance',
          status: 'scheduled',
          details: 'Routine database maintenance completed'
        }
      ]);

      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter golden flows based on selected functionality and environment
  const getFilteredGoldenFlows = (environment?: string) => {
    return goldenFlows.filter(flow => {
      const functionalityMatch = selectedFunctionality === 'all' || flow.functionality === selectedFunctionality;
      const environmentMatch = !environment || flow.environment === environment;
      return functionalityMatch && environmentMatch;
    });
  };

  // Get status counts for overview
  const getGoldenFlowStatusCounts = (environment?: string) => {
    const counts = { passing: 0, failing: 0, warning: 0, running: 0 };
    const flows = getFilteredGoldenFlows(environment);
    flows.forEach(flow => {
      counts[flow.status]++;
    });
    return counts;
  };

  // Function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passing': return '✅';
      case 'failing': return '❌';
      case 'warning': return '⚠️';
      case 'running': return '🔄';
      default: return '⚪';
    }
  };

  // Function to get status color classes
  const getFlowStatusColor = (status: string) => {
    switch (status) {
      case 'passing': return 'bg-green-100 text-green-800 border-green-200';
      case 'failing': return 'bg-red-100 text-red-800 border-red-200';
      case 'warning': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'running': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-amber-100 text-amber-800';
      case 'resolved': return 'bg-blue-100 text-blue-800';
      case 'scheduled': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEnvironmentBadgeColor = (envName: string) => {
    switch (envName.toLowerCase()) {
      case 'production': return 'bg-purple-100 text-purple-800';
      case 'teflon': return 'bg-amber-100 text-amber-800';
      case 'stage': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Chart data for historical trends
  const uptimeChartData = {
    labels: ['Jun 15', 'Jun 16', 'Jun 17', 'Jun 18', 'Jun 19', 'Jun 20', 'Jun 21'],
    datasets: [
      {
        label: 'Stage',
        data: [98.5, 99.2, 98.8, 98.9, 99.5, 99.8, 98.7],
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
      {
        label: 'Teflon',
        data: [97.8, 96.8, 96.9, 97.1, 96.8, 97.2, 96.8],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
      {
        label: 'Production',
        data: [100, 100, 100, 100, 100, 100, 100],
        borderColor: '#8b5cf6',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6,
      }
    ]
  };

  const errorCountChartData = {
    labels: ['Jun 15', 'Jun 16', 'Jun 17', 'Jun 18', 'Jun 19', 'Jun 20', 'Jun 21'],
    datasets: [
      {
        label: 'Stage',
        data: [5, 6, 8, 4, 3, 2, 1],
        backgroundColor: '#3b82f6',
        borderRadius: 4,
      },
      {
        label: 'Teflon',
        data: [35, 30, 29, 27, 30, 18, 35],
        backgroundColor: '#10b981',
        borderRadius: 4,
      },
      {
        label: 'Production',
        data: [1, 1, 1, 2, 1, 1, 1],
        backgroundColor: '#8b5cf6',
        borderRadius: 4,
      }
    ]
  };

  const ResponseTimeGauge = ({ value, label, color }: { value: number; label: string; color: string }) => (
    <div className="flex flex-col items-center">
      <div className="relative">
        <svg width="120" height="120" className="transform -rotate-90">
          <circle
            cx="60"
            cy="60"
            r="45"
            stroke="#f3f4f6"
            strokeWidth="8"
            fill="transparent"
          />
          <circle
            cx="60"
            cy="60"
            r="45"
            stroke={color}
            strokeWidth="8"
            fill="transparent"
            strokeDasharray={`${Math.min(value / 5, 100) * 2.83}, 283`}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
          />
        </svg>
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className="text-2xl font-bold" style={{ color }}>{value}</span>
          <span className="text-xs text-gray-500">ms</span>
        </div>
      </div>
      <span className="text-sm font-semibold text-gray-700 mt-2">{label}</span>
      <span className="text-xs text-gray-500">{value}ms</span>
    </div>
  );

  if (isLoading) {
    return (
      <div className="h-full bg-gray-50">
        <div className="flex items-center justify-center h-full">
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-orange-200"></div>
              <div className="absolute inset-0 animate-spin rounded-full h-16 w-16 border-4 border-orange-600 border-t-transparent"></div>
            </div>
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Environments</h2>
              <p className="text-gray-600">Fetching environment status...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const selectedEnvironment = environments.find(env => env.id === activeTab);

  return (
    <div className="h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            
            <div>
              <div className="flex items-center space-x-3">
                <h1 className="text-2xl font-bold text-gray-900">Environments Overview</h1>
                {/* Mock Data Notice */}
                <div className="bg-amber-50 border border-amber-200 rounded-md px-2 py-1 flex items-center space-x-1">
                  <svg className="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-xs font-medium text-amber-700">Mock Data - Work in Progress</span>
                </div>
              </div>
              <p className="text-gray-600">Monitor your deployment environments</p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs - Completely Redesigned */}
        <div className="mt-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8" aria-label="Tabs">
              {[
                { 
                  id: 'overview', 
                  label: 'Overview', 
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  ),
                  description: 'All environments at a glance'
                },
                { 
                  id: 'stage', 
                  label: 'Stage', 
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  ),
                  description: 'Development testing environment',
                  status: 'healthy'
                },
                { 
                  id: 'teflon', 
                  label: 'Teflon', 
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  ),
                  description: 'Pre-production validation',
                  status: 'warning'
                },
                { 
                  id: 'production', 
                  label: 'Production', 
                  icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  ),
                  description: 'Live production environment',
                  status: 'healthy'
                }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`group relative py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-orange-100 text-orange-600'
                        : 'bg-gray-100 text-gray-400 group-hover:bg-gray-200 group-hover:text-gray-600'
                    }`}>
                      {tab.icon}
                    </div>
                    <div className="text-left">
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold">{tab.label}</span>
                        {tab.status && (
                          <div className={`w-2 h-2 rounded-full ${
                            tab.status === 'healthy' ? 'bg-green-400' : 
                            tab.status === 'warning' ? 'bg-amber-400' : 'bg-red-400'
                          }`}></div>
                        )}
                      </div>
                      {tab.description && (
                        <div className={`text-xs mt-0.5 transition-colors duration-200 ${
                          activeTab === tab.id ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'
                        }`}>
                          {tab.description}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Active indicator */}
                  {activeTab === tab.id && (
                    <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full"></div>
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' ? (
          <div className="space-y-8">
            {/* Environment Cards */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Environments Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {environments.map((env) => (
                  <div
                    key={env.id}
                    className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 hover:scale-[1.02] group cursor-pointer relative overflow-hidden"
                    onClick={() => setActiveTab(env.id as any)}
                    style={{
                      boxShadow: '0 8px 25px -5px rgba(0, 0, 0, 0.08), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'
                    }}
                  >
                    {/* Background gradient overlay */}
                    <div className={`absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300 ${
                      env.status === 'healthy' ? 'bg-gradient-to-br from-green-400 to-green-600' :
                      env.status === 'warning' ? 'bg-gradient-to-br from-amber-400 to-amber-600' :
                      'bg-gradient-to-br from-red-400 to-red-600'
                    }`}></div>

                    {/* Header with icon and status */}
                    <div className="flex items-center justify-between mb-6 relative">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center shadow-lg ${
                          env.status === 'healthy' ? 'bg-gradient-to-br from-green-500 to-green-600' :
                          env.status === 'warning' ? 'bg-gradient-to-br from-amber-500 to-amber-600' :
                          'bg-gradient-to-br from-red-500 to-red-600'
                        }`}>
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {env.id === 'stage' && (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            )}
                            {env.id === 'teflon' && (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            )}
                            {env.id === 'production' && (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            )}
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">{env.name}</h3>
                          <p className="text-sm text-gray-500 font-medium">
                            {env.id === 'stage' ? 'Development Environment' :
                             env.id === 'teflon' ? 'Pre-Production Environment' :
                             'Production Environment'}
                          </p>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border-2 ${getStatusBadgeColor(env.status)} shadow-sm`}>
                        <div className={`w-2 h-2 rounded-full mr-2 animate-pulse ${env.status === 'healthy' ? 'bg-green-500' : env.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'}`}></div>
                        {env.status === 'healthy' ? 'Healthy' : env.status === 'warning' ? 'Warning' : 'Critical'}
                      </span>
                    </div>

                    {/* Metrics Grid */}
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="bg-gray-50 rounded-xl p-4 group-hover:bg-gray-100 transition-colors duration-300">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm font-medium text-gray-600">Uptime</p>
                          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                        </div>
                        <p className="text-2xl font-bold text-gray-900">{env.uptime}%</p>
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                          <div
                            className={`h-1.5 rounded-full transition-all duration-1000 ${
                              env.uptime >= 99 ? 'bg-green-500' : env.uptime >= 95 ? 'bg-amber-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${env.uptime}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 rounded-xl p-4 group-hover:bg-gray-100 transition-colors duration-300">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm font-medium text-gray-600">Errors (24h)</p>
                          <svg className="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                        </div>
                        <p className={`text-2xl font-bold ${env.errors24h > 20 ? 'text-amber-600' : 'text-gray-900'}`}>{env.errors24h}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {env.errors24h > 20 ? 'Above threshold' : 'Within limits'}
                        </p>
                      </div>
                    </div>

                    {/* Additional Info */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-sm font-medium text-gray-600">Response Time</span>
                        </div>
                        <span className={`text-sm font-bold ${env.responseTime > 300 ? 'text-red-600' : env.responseTime > 200 ? 'text-amber-600' : 'text-green-600'}`}>
                          {env.responseTime}ms
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                          <span className="text-sm font-medium text-gray-600">Version</span>
                        </div>
                        <span className="text-sm font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded-md">
                          {env.version}
                        </span>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-sm text-gray-500">Last deploy: {env.lastDeploy}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-gray-400 group-hover:text-orange-500 transition-colors duration-300">
                        <span className="text-xs font-medium">View Details</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Golden Flows Overview Section */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">Golden Flows</h2>
                    <p className="text-gray-600">Critical user journey monitoring across all environments</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">{functionalityFilters.filter(f => f.id !== 'all').length}</div>
                  <div className="text-sm text-gray-500">Flow Types</div>
                </div>
              </div>

              {/* Status Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                {[
                  { status: 'passing', label: 'Passing', icon: '✅', color: 'bg-green-100 text-green-800' },
                  { status: 'warning', label: 'Warning', icon: '⚠️', color: 'bg-amber-100 text-amber-800' },
                  { status: 'failing', label: 'Failing', icon: '❌', color: 'bg-red-100 text-red-800' },
                  { status: 'running', label: 'Running', icon: '🔄', color: 'bg-blue-100 text-blue-800' }
                ].map((item) => {
                  const count = getGoldenFlowStatusCounts()[item.status as keyof ReturnType<typeof getGoldenFlowStatusCounts>];
                  return (
                    <div key={item.status} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-2xl font-bold text-gray-900">{count}</div>
                          <div className="text-sm text-gray-600">{item.label}</div>
                        </div>
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${item.color}`}>
                          <span className="text-xl">{item.icon}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Functionality Tabs */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Golden Flows Performance by Functionality</h3>
                </div>
                
                {/* Tab Navigation */}
                <div className="border-b border-gray-200">
                  <nav className="flex overflow-x-auto" aria-label="Functionality tabs">
                    <button
                      onClick={() => setSelectedFunctionality('all')}
                      className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200 ${
                        selectedFunctionality === 'all'
                          ? 'border-blue-500 text-blue-600 bg-blue-50'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <span className="text-base">🔄</span>
                      <span>All Flows</span>
                    </button>
                    {functionalityFilters.filter(f => f.id !== 'all').map((filter) => (
                      <button
                        key={filter.id}
                        onClick={() => setSelectedFunctionality(filter.id)}
                        className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200 ${
                          selectedFunctionality === filter.id
                            ? 'border-blue-500 text-blue-600 bg-blue-50'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <span className="text-base">{filter.icon}</span>
                        <span>{filter.name}</span>
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {selectedFunctionality === 'all' ? (
                    // All Flows Overview
                    <div className="space-y-6">
                      <div className="text-center">
                        <div className="inline-flex items-center space-x-3 px-4 py-2 rounded-lg bg-blue-100 text-blue-800">
                          <span className="text-xl">🔄</span>
                          <div>
                            <div className="font-semibold">All Golden Flows</div>
                            <div className="text-xs opacity-75">Overview of all functionality performance</div>
                          </div>
                        </div>
                      </div>

                      {/* Compact All Flows Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {functionalityFilters.filter(f => f.id !== 'all').map((filter) => {
                          const flowsForFunc = goldenFlows.filter(f => f.functionality === filter.id);
                          const avgSuccessRate = flowsForFunc.length > 0 
                            ? flowsForFunc.reduce((sum, f) => sum + f.successRate, 0) / flowsForFunc.length 
                            : 0;
                          const avgDuration = flowsForFunc.length > 0 
                            ? flowsForFunc.reduce((sum, f) => sum + f.duration, 0) / flowsForFunc.length 
                            : 0;
                          const worstStatus = flowsForFunc.length > 0
                            ? flowsForFunc.reduce((worst, f) => {
                                const statusPriority = { failing: 4, warning: 3, running: 2, passing: 1 };
                                return statusPriority[f.status as keyof typeof statusPriority] > 
                                       statusPriority[worst as keyof typeof statusPriority] ? f.status : worst;
                              }, 'passing')
                            : 'passing';

                          return (
                            <div 
                              key={filter.id} 
                              className="border-2 border-gray-200 rounded-xl p-3 bg-gray-50 transition-all duration-200 hover:shadow-md cursor-pointer hover:border-blue-300"
                              onClick={() => setSelectedFunctionality(filter.id)}
                            >
                              {/* Functionality Header */}
                              <div className="flex items-center justify-center mb-2">
                                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${filter.color}`}>
                                  <span className="text-base">{filter.icon}</span>
                                </div>
                              </div>

                              {/* Flow Name */}
                              <div className="text-center mb-2">
                                <div className="text-sm font-semibold text-gray-900">{filter.name}</div>
                                <div className="text-xs text-gray-500">{flowsForFunc.length} flows • 3 environments</div>
                              </div>

                              {/* Overall Status */}
                              <div className="flex justify-center mb-2">
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getFlowStatusColor(worstStatus)}`}>
                                  <span className="mr-1 text-xs">{getStatusIcon(worstStatus)}</span>
                                  {worstStatus.charAt(0).toUpperCase() + worstStatus.slice(1)}
                                </span>
                              </div>

                              {/* Aggregate Metrics */}
                              <div className="text-center space-y-1 mb-2">
                                <div>
                                  <div className={`text-base font-bold ${
                                    avgSuccessRate >= 95 ? 'text-green-600' : 
                                    avgSuccessRate >= 85 ? 'text-amber-600' : 'text-red-600'
                                  }`}>
                                    {avgSuccessRate.toFixed(1)}%
                                  </div>
                                  <div className="text-xs text-gray-500">Avg Success</div>
                                </div>
                                <div>
                                  <div className="text-base font-bold text-blue-600">
                                    {avgDuration.toFixed(1)}s
                                  </div>
                                  <div className="text-xs text-gray-500">Avg Duration</div>
                                </div>
                              </div>

                              {/* Environment Status Indicators */}
                              <div className="space-y-1">
                                {['production', 'teflon', 'stage'].map((env) => {
                                  const envFlows = flowsForFunc.filter(f => f.environment === env);
                                  const envStatus = envFlows.length > 0
                                    ? envFlows.reduce((worst, f) => {
                                        const statusPriority = { failing: 4, warning: 3, running: 2, passing: 1 };
                                        return statusPriority[f.status as keyof typeof statusPriority] > 
                                               statusPriority[worst as keyof typeof statusPriority] ? f.status : worst;
                                      }, 'passing')
                                    : 'passing';
                                  const envAvgSuccess = envFlows.length > 0
                                    ? envFlows.reduce((sum, f) => sum + f.successRate, 0) / envFlows.length
                                    : 0;
                                  
                                  return (
                                    <div key={env} className="flex items-center justify-between text-xs">
                                      <div className="flex items-center space-x-1">
                                        <div className={`w-2 h-2 rounded-full ${
                                          envStatus === 'passing' ? 'bg-green-400' :
                                          envStatus === 'warning' ? 'bg-amber-400' : 
                                          envStatus === 'failing' ? 'bg-red-400' : 'bg-blue-400'
                                        }`} />
                                        <span className="text-gray-600 capitalize font-medium">
                                          {env === 'production' ? 'Prod' : env === 'teflon' ? 'Teflon' : 'Stage'}
                                        </span>
                                      </div>
                                      <span className={`font-semibold ${
                                        envAvgSuccess >= 95 ? 'text-green-600' : 
                                        envAvgSuccess >= 85 ? 'text-amber-600' : 'text-red-600'
                                      }`}>
                                        {envAvgSuccess > 0 ? `${envAvgSuccess.toFixed(0)}%` : '-'}
                                      </span>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : (
                    // Individual Functionality View
                    (() => {
                      // Get flows for selected functionality across all environments
                      const flowsForFunctionality = goldenFlows.filter(flow => 
                        flow.functionality === selectedFunctionality
                      );
                      
                      // Group by environment
                      const flowsByEnv = {
                        production: flowsForFunctionality.find(f => f.environment === 'production'),
                        teflon: flowsForFunctionality.find(f => f.environment === 'teflon'),
                        stage: flowsForFunctionality.find(f => f.environment === 'stage')
                      };

                      const selectedFilter = functionalityFilters.find(f => f.id === selectedFunctionality);

                      return (
                        <div className="space-y-6">
                          {/* Functionality Header */}
                          <div className="text-center">
                            <div className={`inline-flex items-center space-x-3 px-4 py-2 rounded-lg ${selectedFilter?.color || 'bg-gray-100 text-gray-800'}`}>
                              <span className="text-xl">{selectedFilter?.icon || '🔄'}</span>
                              <div>
                                <div className="font-semibold">{selectedFilter?.name || selectedFunctionality}</div>
                                <div className="text-xs opacity-75">{selectedFilter?.description}</div>
                              </div>
                            </div>
                          </div>

                          {/* Environment Comparison Cards */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {[
                              { env: 'production', label: 'Production', color: 'border-purple-200 bg-purple-50' },
                              { env: 'teflon', label: 'Teflon', color: 'border-amber-200 bg-amber-50' },
                              { env: 'stage', label: 'Stage', color: 'border-green-200 bg-green-50' }
                            ].map(({ env, label, color }) => {
                              const flow = flowsByEnv[env as keyof typeof flowsByEnv];
                              return (
                                <div key={env} className={`border-2 rounded-xl p-4 ${color} transition-all duration-200 hover:shadow-md`}>
                                  {/* Environment Header */}
                                  <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center space-x-2">
                                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                                        env === 'production' ? 'bg-purple-100 text-purple-600' :
                                        env === 'teflon' ? 'bg-amber-100 text-amber-600' :
                                        'bg-green-100 text-green-600'
                                      }`}>
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          {env === 'production' && (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                          )}
                                          {env === 'teflon' && (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                          )}
                                          {env === 'stage' && (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                          )}
                                        </svg>
                                      </div>
                                      <span className="font-semibold text-gray-900">{label}</span>
                                    </div>
                                    {flow && (
                                      <span className={`text-xs ${
                                        flow.trend === 'up' ? 'text-green-600' : 
                                        flow.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                                      }`}>
                                        {flow.trend === 'up' ? '↗️' : flow.trend === 'down' ? '↘️' : '➡️'}
                                      </span>
                                    )}
                                  </div>

                                  {flow ? (
                                    <div className="space-y-3">
                                      {/* Status */}
                                      <div className="flex justify-center">
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getFlowStatusColor(flow.status)}`}>
                                          <span className="mr-1">{getStatusIcon(flow.status)}</span>
                                          {flow.status.charAt(0).toUpperCase() + flow.status.slice(1)}
                                        </span>
                                      </div>

                                      {/* Key Metrics */}
                                      <div className="grid grid-cols-2 gap-3 text-center">
                                        <div>
                                          <div className={`text-lg font-bold ${
                                            flow.successRate >= 95 ? 'text-green-600' : 
                                            flow.successRate >= 85 ? 'text-amber-600' : 'text-red-600'
                                          }`}>
                                            {flow.successRate.toFixed(1)}%
                                          </div>
                                          <div className="text-xs text-gray-500">Success Rate</div>
                                        </div>
                                        <div>
                                          <div className="text-lg font-bold text-blue-600">
                                            {flow.duration.toFixed(1)}s
                                          </div>
                                          <div className="text-xs text-gray-500">Duration</div>
                                        </div>
                                      </div>

                                      {/* Flow Name and Last Run */}
                                      <div className="text-center">
                                        <div className="text-sm font-medium text-gray-900">{flow.name}</div>
                                        <div className="text-xs text-gray-500">Last run: {flow.lastRun}</div>
                                      </div>

                                      {/* Quick Step Status */}
                                      <div className="flex justify-center space-x-1">
                                        {flow.steps.slice(0, 4).map((step, index) => (
                                          <div
                                            key={step.id}
                                            className={`w-3 h-3 rounded-full ${
                                              step.status === 'passed' ? 'bg-green-400' :
                                              step.status === 'failed' ? 'bg-red-400' :
                                              'bg-amber-400'
                                            }`}
                                            title={`Step ${index + 1}: ${step.name} (${step.status})`}
                                          />
                                        ))}
                                        {flow.steps.length > 4 && (
                                          <div className="w-3 h-3 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span className="text-xs text-gray-600">+</span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="text-center py-6">
                                      <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-2">
                                        <span className="text-gray-400">—</span>
                                      </div>
                                      <div className="text-sm text-gray-500">Not configured</div>
                                      <div className="text-xs text-gray-400">No flow available</div>
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })()
                  )}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  Showing flows across all environments • Last updated: 2 minutes ago
                </div>
                
              </div>
            </div>

            {/* Historical Trends */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Historical Trends</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Uptime Percentage (7 Days)</h3>
                  <div className="h-64">
                    <Line
                      data={uptimeChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: false,
                            min: 95,
                            max: 101,
                            grid: { 
                              color: '#f3f4f6'
                            },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          },
                          x: {
                            grid: { display: false },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          }
                        },
                        plugins: {
                          legend: {
                            position: 'top' as const,
                            labels: { 
                              color: '#374151', 
                              font: { size: 12 },
                              usePointStyle: true,
                              pointStyle: 'circle',
                              padding: 20
                            }
                          },
                          tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            cornerRadius: 8,
                            padding: 12
                          }
                        },
                        elements: {
                          point: {
                            radius: 4,
                            hoverRadius: 6,
                            borderWidth: 2
                          },
                          line: {
                            borderWidth: 3
                          }
                        },
                        interaction: {
                          intersect: false,
                          mode: 'index'
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Error Count (7 Days)</h3>
                  <div className="h-64">
                    <Line
                      data={errorCountChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true,
                            grid: { 
                              color: '#f3f4f6'
                            },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          },
                          x: {
                            grid: { display: false },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          }
                        },
                        plugins: {
                          legend: {
                            position: 'top' as const,
                            labels: { 
                              color: '#374151', 
                              font: { size: 12 },
                              usePointStyle: true,
                              pointStyle: 'rect',
                              padding: 20
                            }
                          },
                          tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            cornerRadius: 8,
                            padding: 12
                          }
                        },
                        elements: {
                          line: {
                            borderWidth: 3
                          }
                        },
                        interaction: {
                          intersect: false,
                          mode: 'index'
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Response Time Gauges */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Average Response Time (Current Hour)</h2>
              <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-sm">
                <div className="flex justify-around items-center">
                  <ResponseTimeGauge value={185} label="Stage" color="#10b981" />
                  <ResponseTimeGauge value={312} label="Teflon" color="#ef4444" />
                  <ResponseTimeGauge value={142} label="Production" color="#10b981" />
                </div>
                <div className="flex justify-center mt-6 space-x-8 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Good (&lt;200ms)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                    <span>OK (200-300ms)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span>Slow (&gt;300ms)</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Events */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Events & Deployments</h2>
              <div className="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Events & Deployments</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recentEvents.map((event) => (
                        <tr key={event.id} className="hover:bg-gray-50 transition-colors duration-200">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{event.timestamp}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEnvironmentBadgeColor(event.environment)}`}>
                              {event.environment}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">{event.eventType}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEventStatusColor(event.status)}`}>
                              {event.status === 'success' && '✓ Success'}
                              {event.status === 'warning' && '⚠ Warning'}
                              {event.status === 'resolved' && '✓ Resolved'}
                              {event.status === 'scheduled' && 'ℹ Scheduled'}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">{event.details}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
                  <span className="text-sm text-gray-500">Showing 5 of 5 recent events</span>
                  <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">View all events</button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Individual Environment View
          selectedEnvironment && (
            <div className="space-y-8">
              {/* Environment Header */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <rect x="3" y="4" width="18" height="12" rx="2" />
                        <rect x="7" y="8" width="4" height="4" rx="1" />
                        <rect x="13" y="8" width="4" height="4" rx="1" />
                      </svg>
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900">{selectedEnvironment.name} Environment</h1>
                      <p className="text-gray-600">Detailed monitoring and metrics</p>
                    </div>
                  </div>
                  <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium border ${getStatusBadgeColor(selectedEnvironment.status)}`}>
                    <div className={`w-2 h-2 rounded-full mr-2 ${selectedEnvironment.status === 'healthy' ? 'bg-green-500' : selectedEnvironment.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'}`}></div>
                    {selectedEnvironment.status === 'healthy' ? 'Healthy' : selectedEnvironment.status === 'warning' ? 'Warning' : 'Critical'}
                  </span>
                </div>
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <span className="text-sm font-medium text-gray-600">Uptime</span>
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{selectedEnvironment.uptime}%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${selectedEnvironment.uptime}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <span className="text-sm font-medium text-gray-600">Errors (24h)</span>
                  </div>
                  <div className={`text-3xl font-bold mb-2 ${selectedEnvironment.errors24h > 20 ? 'text-amber-600' : 'text-gray-900'}`}>
                    {selectedEnvironment.errors24h}
                  </div>
                  <div className="text-sm text-gray-500">24-hour period</div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="text-sm font-medium text-gray-600">Response Time</span>
                  </div>
                  <div className={`text-3xl font-bold mb-2 ${selectedEnvironment.responseTime > 300 ? 'text-red-600' : selectedEnvironment.responseTime > 200 ? 'text-amber-600' : 'text-green-600'}`}>
                    {selectedEnvironment.responseTime}ms
                  </div>
                  <div className="text-sm text-gray-500">Average response</div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-9 0v16a2 2 0 002 2h6a2 2 0 002-2V4M9 8h6m-6 4h6m-6 4h6" />
                      </svg>
                    </div>
                    <span className="text-sm font-medium text-gray-600">Version</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-2">{selectedEnvironment.version}</div>
                  <div className="text-sm text-gray-500">Current version</div>
                </div>
              </div>

              {/* Detailed Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Uptime Trend (30 Days)</h3>
                  <div className="h-64">
                    <Line
                      data={{
                        labels: ['Jun 15', 'Jun 16', 'Jun 17', 'Jun 18', 'Jun 19', 'Jun 20', 'Jun 21'],
                        datasets: [{
                          label: selectedEnvironment.name,
                          data: selectedEnvironment.id === 'stage' ? [98.5, 99.2, 98.8, 98.9, 99.5, 99.8, 98.7] :
                                selectedEnvironment.id === 'teflon' ? [97.8, 96.8, 96.9, 97.1, 96.8, 97.2, 96.8] :
                                [100, 100, 100, 100, 100, 100, 100],
                          borderColor: '#10b981',
                          backgroundColor: 'rgba(16, 185, 129, 0.1)',
                          tension: 0.4,
                          fill: true,
                          pointRadius: 4,
                          pointHoverRadius: 6,
                          borderWidth: 3,
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: { 
                            beginAtZero: false, 
                            min: 95, 
                            max: 101,
                            grid: { 
                              color: '#f3f4f6'
                            },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          },
                          x: { 
                            grid: { display: false },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          }
                        },
                        plugins: { 
                          legend: { display: false },
                          tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            cornerRadius: 8,
                            padding: 12
                          }
                        },
                        elements: {
                          point: {
                            radius: 4,
                            hoverRadius: 6,
                            borderWidth: 2
                          },
                          line: {
                            borderWidth: 3
                          }
                        },
                        interaction: {
                          intersect: false,
                          mode: 'index'
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Error Count Trend (30 Days)</h3>
                  <div className="h-64">
                    <Line
                      data={{
                        labels: ['Jun 15', 'Jun 16', 'Jun 17', 'Jun 18', 'Jun 19', 'Jun 20', 'Jun 21'],
                        datasets: [{
                          label: 'Errors',
                          data: selectedEnvironment.id === 'stage' ? [5, 6, 8, 4, 3, 2, 1] :
                                selectedEnvironment.id === 'teflon' ? [35, 30, 29, 27, 30, 18, 35] :
                                [1, 1, 1, 2, 1, 1, 1],
                          backgroundColor: '#ef4444'
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: { 
                            beginAtZero: true,
                            grid: { 
                              color: '#f3f4f6'
                            },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          },
                          x: { 
                            grid: { display: false },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          }
                        },
                        plugins: { 
                          legend: { display: false },
                          tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            cornerRadius: 8,
                            padding: 12
                          }
                        },
                        elements: {
                          line: {
                            borderWidth: 3
                          }
                        },
                        interaction: {
                          intersect: false,
                          mode: 'index'
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Response Time Trend (30 Days)</h3>
                  <div className="h-64">
                    <Line
                      data={{
                        labels: ['Jun 15', 'Jun 16', 'Jun 17', 'Jun 18', 'Jun 19', 'Jun 20', 'Jun 21'],
                        datasets: [{
                          label: 'Response Time',
                          data: selectedEnvironment.id === 'stage' ? [190, 195, 200, 180, 185, 175, 160] :
                                selectedEnvironment.id === 'teflon' ? [320, 310, 305, 315, 300, 295, 312] :
                                [145, 140, 138, 142, 144, 140, 142],
                          borderColor: '#3b82f6',
                          backgroundColor: 'rgba(59, 130, 246, 0.1)',
                          tension: 0.4,
                          fill: true,
                          pointRadius: 4,
                          pointHoverRadius: 6,
                          pointBackgroundColor: '#3b82f6',
                          pointBorderColor: '#ffffff',
                          pointBorderWidth: 2,
                          pointHoverBackgroundColor: '#3b82f6',
                          pointHoverBorderColor: '#ffffff',
                          pointHoverBorderWidth: 2
                        }]
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: { 
                            beginAtZero: false,
                            grid: { 
                              color: '#f3f4f6'
                            },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          },
                          x: { 
                            grid: { display: false },
                            ticks: { 
                              color: '#6b7280', 
                              font: { size: 11 },
                              padding: 8
                            }
                          }
                        },
                        plugins: { 
                          legend: { display: false },
                          tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            cornerRadius: 8,
                            padding: 12
                          }
                        },
                        elements: {
                          point: {
                            radius: 4,
                            hoverRadius: 6,
                            borderWidth: 2
                          },
                          line: {
                            borderWidth: 3
                          }
                        },
                        interaction: {
                          intersect: false,
                          mode: 'index'
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Current Response Time</h3>
                  
                  {/* Main Gauge Section */}
                  <div className="flex flex-col items-center mb-6">
                    <div className="relative mb-4">
                      <svg width="160" height="160" className="transform -rotate-90">
                        <circle
                          cx="80"
                          cy="80"
                          r="60"
                          stroke="#f3f4f6"
                          strokeWidth="10"
                          fill="transparent"
                        />
                        <circle
                          cx="80"
                          cy="80"
                          r="60"
                          stroke={selectedEnvironment.responseTime > 300 ? '#ef4444' : selectedEnvironment.responseTime > 200 ? '#f59e0b' : '#10b981'}
                          strokeWidth="10"
                          fill="transparent"
                          strokeDasharray={`${Math.min(selectedEnvironment.responseTime / 5, 100) * 3.77}, 377`}
                          strokeLinecap="round"
                          className="transition-all duration-1000 ease-out"
                        />
                      </svg>
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <span className="text-3xl font-bold" style={{ color: selectedEnvironment.responseTime > 300 ? '#ef4444' : selectedEnvironment.responseTime > 200 ? '#f59e0b' : '#10b981' }}>
                          {selectedEnvironment.responseTime}
                        </span>
                        <span className="text-lg text-gray-500 font-medium">ms</span>
                      </div>
                    </div>
                    
                    {/* Performance Status */}
                    <div className={`px-3 py-1.5 rounded-full text-sm font-semibold ${
                      selectedEnvironment.responseTime > 300 ? 'bg-red-100 text-red-800' :
                      selectedEnvironment.responseTime > 200 ? 'bg-amber-100 text-amber-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {selectedEnvironment.responseTime > 300 ? 'Slow Response' :
                       selectedEnvironment.responseTime > 200 ? 'Moderate Response' :
                       'Fast Response'}
                    </div>
                  </div>

                  {/* Simple Performance Scale */}
                  <div className="flex justify-center space-x-6 text-xs">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-gray-600">Good (&lt;200ms)</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="text-gray-600">OK (200-300ms)</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="text-gray-600">Slow (&gt;300ms)</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Golden Flows for this Environment */}
              <div>
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">Golden Flows - {selectedEnvironment.name}</h2>
                      <p className="text-gray-600">Critical user journey monitoring for this environment</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">{getFilteredGoldenFlows(selectedEnvironment.id).length}</div>
                    <div className="text-sm text-gray-500">Active Flows</div>
                  </div>
                </div>

                {/* Filter Controls for Environment */}
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Filter by Functionality</h3>
                    <div className="flex flex-wrap gap-2">
                      {functionalityFilters.map((filter) => (
                        <button
                          key={filter.id}
                          onClick={() => setSelectedFunctionality(filter.id)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 border-2 ${
                            selectedFunctionality === filter.id
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          title={filter.description}
                        >
                          <span className="text-base">{filter.icon}</span>
                          <span>{filter.name}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Status Summary for Environment */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                  {[
                    { status: 'passing', label: 'Passing', icon: '✅', color: 'bg-green-100 text-green-800' },
                    { status: 'warning', label: 'Warning', icon: '⚠️', color: 'bg-amber-100 text-amber-800' },
                    { status: 'failing', label: 'Failing', icon: '❌', color: 'bg-red-100 text-red-800' },
                    { status: 'running', label: 'Running', icon: '🔄', color: 'bg-blue-100 text-blue-800' }
                  ].map((item) => {
                    const count = getGoldenFlowStatusCounts(selectedEnvironment.id)[item.status as keyof ReturnType<typeof getGoldenFlowStatusCounts>];
                    return (
                      <div key={item.status} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-2xl font-bold text-gray-900">{count}</div>
                            <div className="text-sm text-gray-600">{item.label}</div>
                          </div>
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${item.color}`}>
                            <span className="text-xl">{item.icon}</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Golden Flows Grid for Environment */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  {getFilteredGoldenFlows(selectedEnvironment.id).map((flow) => (
                    <div key={flow.id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300 p-3">
                      {/* Header: Icon + Name + Status */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className={`w-6 h-6 rounded-md flex items-center justify-center ${
                            functionalityFilters.find(f => f.id === flow.functionality)?.color || 'bg-gray-100 text-gray-800'
                          }`}>
                            <span className="text-sm">
                              {functionalityFilters.find(f => f.id === flow.functionality)?.icon || '🔄'}
                            </span>
                          </div>
                          <div>
                            <h3 className="text-sm font-semibold text-gray-900">{flow.name}</h3>
                            <p className="text-xs text-gray-500">Last: {flow.lastRun}</p>
                          </div>
                        </div>
                        <span className={`text-xs ${
                          flow.trend === 'up' ? 'text-green-600' : 
                          flow.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {flow.trend === 'up' ? '↗️' : flow.trend === 'down' ? '↘️' : '➡️'}
                        </span>
                      </div>

                      {/* Status Badge */}
                      <div className="flex justify-center mb-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getFlowStatusColor(flow.status)}`}>
                          <span className="mr-1 text-xs">{getStatusIcon(flow.status)}</span>
                          {flow.status.charAt(0).toUpperCase() + flow.status.slice(1)}
                        </span>
                      </div>

                      {/* Metrics */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="text-center">
                          <div className={`text-sm font-bold ${
                            flow.successRate >= 95 ? 'text-green-600' : 
                            flow.successRate >= 85 ? 'text-amber-600' : 'text-red-600'
                          }`}>
                            {flow.successRate.toFixed(1)}%
                          </div>
                          <div className="text-xs text-gray-500">Success</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-bold text-blue-600">{flow.duration.toFixed(1)}s</div>
                          <div className="text-xs text-gray-500">Duration</div>
                        </div>
                      </div>

                      {/* Quick Step Status Dots */}
                      <div className="flex justify-center space-x-1">
                        {flow.steps.slice(0, 4).map((step, index) => (
                          <div
                            key={step.id}
                            className={`w-2 h-2 rounded-full ${
                              step.status === 'passed' ? 'bg-green-400' :
                              step.status === 'failed' ? 'bg-red-400' :
                              'bg-amber-400'
                            }`}
                            title={`Step ${index + 1}: ${step.name} (${step.status}) - ${step.duration.toFixed(1)}s`}
                          />
                        ))}
                        {flow.steps.length > 4 && (
                          <div className="w-2 h-2 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-xs text-gray-600">+</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {getFilteredGoldenFlows(selectedEnvironment.id).length === 0 && (
                  <div className="bg-white rounded-xl border border-gray-200 p-12 text-center">
                    <div className="text-6xl mb-4">🔍</div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Golden Flows Found</h3>
                    <p className="text-gray-600">No flows match your current filter criteria for {selectedEnvironment.name} environment.</p>
                  </div>
                )}
              </div>

              {/* Recent Events for this Environment */}
              <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Events for {selectedEnvironment.name}</h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {recentEvents
                      .filter(event => event.environment.toLowerCase() === selectedEnvironment.name.toLowerCase())
                      .map((event) => (
                        <div key={event.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                          <div className={`w-3 h-3 rounded-full mt-1.5 ${
                            event.status === 'success' ? 'bg-green-500' : 
                            event.status === 'warning' ? 'bg-amber-500' : 
                            event.status === 'resolved' ? 'bg-blue-500' : 'bg-purple-500'
                          }`}></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-900 capitalize">{event.eventType}</span>
                              <span className="text-xs text-gray-500">{event.timestamp}</span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{event.details}</p>
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>

              {/* Deployment Information */}
              <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Deployment Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Last Deployment</p>
                      <p className="font-semibold text-gray-900">{selectedEnvironment.lastDeploy}</p>
                      <p className="text-xs text-gray-500">6/21/2025, 1:18:08 PM</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Current Version</p>
                      <p className="font-semibold text-blue-600">{selectedEnvironment.version}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default EnvironmentsPage; 