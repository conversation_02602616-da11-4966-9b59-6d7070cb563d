import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Hooks
import { useReleasesData } from '../hooks/useReleasesData';
import { useFiltersAndSorting } from '../hooks/useFiltersAndSorting';
import { useToast } from '../hooks/useToast';
import { useReleasesPageActions } from '../hooks/useReleasesPageActions';

// Services
import { releaseAPI, type UpdateReleaseRequest } from '../services/release';
import type { Release } from '../types/api';

// Components
import {
  ViewToggle,
  SearchBar,
  SortControls,
  FilterControls,
  ToastNotification,
  EmptyState
} from '../components/ui';
import { ReleaseGrid, ReleaseTable } from '../components/release';
import EditReleaseModal from '../components/modals/EditReleaseModal';

const ReleasesPage: React.FC = () => {
  const navigate = useNavigate();

  // Custom hooks
  const { releases, teams, isLoading, getTeamName, loadData } = useReleasesData();
  const { toast, showToast, hideToast } = useToast();
  const { pdfGenerating, handlePdfDownload, handleSendEmail } = useReleasesPageActions({ teams, showToast });
  const {
    viewType,
    setViewType,
    sortField,
    sortOrder,
    filters,
    setFilters,
    activeFiltersCount,
    filteredAndSortedReleases,
    handleSort,
    clearFilters,
    availablePlatforms
  } = useFiltersAndSorting({ releases, teams, getTeamName });

  // Edit state
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedRelease, setSelectedRelease] = useState<Release | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleReleaseClick = (releaseId: string) => {
    navigate(`/releases/${releaseId}`);
  };

  const handleEditRelease = (release: Release, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedRelease(release);
    setShowEditModal(true);
  };

  const handleUpdateRelease = async (updateData: UpdateReleaseRequest) => {
    if (!selectedRelease) return;

    setIsUpdating(true);
    try {
      await releaseAPI.updateRelease(selectedRelease.id, updateData);
      await loadData(); // Refresh the releases list
      setShowEditModal(false);
      setSelectedRelease(null);
      showToast('Release updated successfully', 'success');
    } catch (error) {
      console.error('Error updating release:', error);
      showToast('Failed to update release', 'error');
      throw error; // Re-throw to let the modal handle the error
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteRelease = async (releaseId: string) => {
    try {
      await releaseAPI.deleteRelease(releaseId);
      await loadData(); // Refresh the releases list
      setShowEditModal(false);
      setSelectedRelease(null);
      showToast('Release deleted successfully', 'success');
    } catch (error) {
      console.error('Error deleting release:', error);
      showToast('Failed to delete release', 'error');
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleCloseEditModal = () => {
    if (!isUpdating) {
      setShowEditModal(false);
      setSelectedRelease(null);
    }
  };

  if (isLoading) {
    return (
      <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200"></div>
            <div className="absolute inset-0 animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
          </div>
          <p className="text-gray-600 font-medium">Loading releases...</p>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-20">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold text-gray-900">All Releases</h1>
            <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">
              {filteredAndSortedReleases.length} of {releases.length}
            </span>
          </div>

          {/* View Toggle */}
          <ViewToggle viewType={viewType} onViewTypeChange={setViewType} />
        </div>

        {/* Filters */}
        <div className="flex flex-wrap items-center gap-3">
          {/* Search */}
          <SearchBar
              value={filters.search}
            onChange={(value) => setFilters(prev => ({ ...prev, search: value }))}
          />

          {/* Filter Controls */}
          <FilterControls
            filters={filters}
            onFiltersChange={setFilters}
            teams={teams}
            availablePlatforms={availablePlatforms}
            activeFiltersCount={activeFiltersCount}
            onClearFilters={clearFilters}
          />

          {/* Sort Controls */}
          <SortControls
            sortField={sortField}
            sortOrder={sortOrder}
            onSort={handleSort}
          />
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {filteredAndSortedReleases.length === 0 ? (
          <EmptyState
            hasReleases={releases.length > 0}
            activeFiltersCount={activeFiltersCount}
            onClearFilters={clearFilters}
          />
        ) : viewType === 'grid' ? (
          <ReleaseGrid
            releases={filteredAndSortedReleases}
            getTeamName={getTeamName}
            onReleaseClick={handleReleaseClick}
          />
        ) : (
          <ReleaseTable
            releases={filteredAndSortedReleases}
            getTeamName={getTeamName}
            onReleaseClick={handleReleaseClick}
            onPdfDownload={handlePdfDownload}
            onSendEmail={handleSendEmail}
            onEditRelease={handleEditRelease}
            pdfGenerating={pdfGenerating}
          />
        )}
      </div>

      {/* Toast Notification */}
      <ToastNotification toast={toast} onClose={hideToast} />

      {/* Edit Release Modal */}
      {selectedRelease && (
        <EditReleaseModal
          isOpen={showEditModal}
          onClose={handleCloseEditModal}
          onSubmit={handleUpdateRelease}
          onDelete={handleDeleteRelease}
          release={selectedRelease}
          teams={teams.map(team => ({ id: team.id, name: team.teamName }))}
          isLoading={isUpdating}
        />
      )}
    </div>
  );
};

export default ReleasesPage; 