import React, { useState, useEffect } from 'react';
import { cn } from '../utils';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface HelpCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
}

const HelpPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const ITEMS_PER_PAGE = 5;

  const categories: HelpCategory[] = [
    {
      id: 'all',
      name: 'All Topics',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      ),
      description: 'Browse all help topics'
    },
    {
      id: 'getting-started',
      name: 'Getting Started',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
        </svg>
      ),
      description: 'Learn the basics'
    },
    {
      id: 'releases',
      name: 'Release Management',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
        </svg>
      ),
      description: 'Managing releases and deployments'
    },
    {
      id: 'metrics',
      name: 'Quality Metrics',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
        </svg>
      ),
      description: 'Code quality and testing metrics'
    },
    {
      id: 'signoffs',
      name: 'Signoffs & Approvals',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      ),
      description: 'Approval workflows and tracking'
    },
    {
      id: 'authentication',
      name: 'Authentication',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
        </svg>
      ),
      description: 'Login and security'
    },
    {
      id: 'troubleshooting',
      name: 'Troubleshooting',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
      ),
      description: 'Common issues and solutions'
    }
  ];

  const faqs: FAQItem[] = [
    // Getting Started
    {
      id: '1',
      question: 'What is the Engineering Excellence platform?',
      answer: 'The Engineering Excellence platform is a comprehensive release management system that provides confidence scoring, quality metrics tracking, approval workflows, and environment monitoring. It integrates with SonarQube, GitHub, Looper, and ServiceNow to give you complete visibility into your software delivery process.',
      category: 'getting-started'
    },
    {
      id: '2',
      question: 'How do I get started with the platform?',
      answer: 'To get started: 1) Sign in using your GitHub or Azure account, 2) Browse the Dashboard to see releases and confidence scores, 3) Visit the Releases page to view all releases, 4) Click the floating + button to create your first release, 5) Navigate to Environments to monitor deployment status.',
      category: 'getting-started'
    },
    {
      id: '3',
      question: 'What are the main sections of the platform?',
      answer: 'The platform has four main sections: 📊 Dashboard (release overview with confidence scores), 🚀 Releases (detailed release management), 🖥️ Environments (deployment monitoring), and 💬 Help (this support section). Each section provides different views and capabilities for managing your software delivery.',
      category: 'getting-started'
    },
    {
      id: '4',
      question: 'What are confidence scores and how are they calculated?',
      answer: 'Confidence scores are calculated from 6 key metrics: Code Quality (20%), Code Coverage (20%), Test Results (20%), Build Stability (20%), Change Requests (10%), and Changelog (10%). Scores range from 0-100% and update automatically when new metric data is available. The higher the score, the more confident you can be in the release quality.',
      category: 'getting-started'
    },

    // Release Management
    {
      id: '5',
      question: 'How do I create a new release?',
      answer: 'Click the floating + button (visible only when authenticated). Fill in: Release name, Team selection, Release type (Major/Minor/Patch/Hotfix), Release branch, Target platforms (iOS, Android, Desktop Web, Mobile Web, Backend), Release date, and optional description. The platform validates all configurations before creation.',
      category: 'releases'
    },
    {
      id: '6',
      question: 'How does platform selection work?',
      answer: 'Platform selection uses a three-state system: Unselected (gray), Enabled but unselected sub-options (orange), and Fully selected (blue with checkmarks). You must select specific sub-options like iOS/Android for Mobile or Desktop/Mobile Web for Web platforms. Backend is a simple toggle.',
      category: 'releases'
    },
    {
      id: '7',
      question: 'What are the different release types and their purposes?',
      answer: '🔧 Patch - Bug fixes and security updates, ✨ Minor - New features and improvements, 🚀 Major - Significant changes and new capabilities, 🔥 Hotfix - Emergency fixes for critical production issues. Each type may have different approval requirements and workflows.',
      category: 'releases'
    },
    {
      id: '8',
      question: 'How do I track release progress and view metrics?',
      answer: 'Navigate to any release details page and use the three tabs: Overview (summary and confidence score), Release Metrics (6 detailed metric tabs), and Signoff Tracker (approval workflows). Each metric tab shows real-time data with database timestamps and refresh capabilities.',
      category: 'releases'
    },

    // Quality Metrics
    {
      id: '9',
      question: 'What quality metrics does the platform track?',
      answer: 'The platform tracks 6 key metrics: 1) Code Quality (SonarQube - bugs, vulnerabilities, code smells), 2) Code Coverage (test coverage percentages), 3) Test Results (test execution and success rates), 4) Build Stability (Looper - build success rates and duration), 5) Change Requests (ServiceNow tickets), 6) Changelog (GitHub pull requests).',
      category: 'metrics'
    },
    {
      id: '10',
      question: 'How often are metrics updated?',
      answer: 'Metrics are updated in real-time when new data is available. The platform shows database timestamps indicating when each metric was last updated. You can manually refresh any metric tab to fetch the latest data. Cached data is used for better performance, with automatic fallback to stored database values.',
      category: 'metrics'
    },
    {
      id: '11',
      question: 'What does "Cached" vs "Stored Data" mean in metrics?',
      answer: '"Cached" means data from your browser session for faster loading. "Stored Data" means fallback data from the database when live API calls fail (e.g., network issues with SonarQube). Database timestamps only update when truly new metric snapshots are received, ensuring accuracy.',
      category: 'metrics'
    },
    {
      id: '12',
      question: 'How do I interpret Code Quality metrics?',
      answer: 'Code Quality metrics from SonarQube include: Bugs (coding errors), Vulnerabilities (security issues), Code Smells (maintainability issues), Coverage (test coverage %), and Ratings (A-E scale). The tab shows both "New Code" (recent changes) and "Overall Code" (entire project) metrics with detailed tooltips.',
      category: 'metrics'
    },
    {
      id: '13',
      question: 'What information is shown in Build Stability metrics?',
      answer: 'Build Stability integrates with Looper to show: Total builds, Success rate percentage, Failure counts, Average build time, Build status pie chart, and Build duration trends over time. This helps identify build reliability and performance patterns.',
      category: 'metrics'
    },
    {
      id: '14',
      question: 'How do Change Requests integrate with ServiceNow?',
      answer: 'The platform connects to ServiceNow to fetch change requests for your team. It shows request numbers (clickable links to ServiceNow), descriptions, priorities (Critical/High/Medium/Low), status (Open/Resolved/Closed), and opened dates. This helps track change management compliance.',
      category: 'metrics'
    },

    // Signoffs and Approvals
    {
      id: '15',
      question: 'How does the signoff workflow work?',
      answer: 'Signoffs have three sequential stages: 1) Functional QA (sub-team approvals like PLP, PLATFORM, HOME), 2) E2E QA (end-to-end testing approval), 3) Engineering Leadership (final release approval). Each stage must be completed before the next unlocks.',
      category: 'signoffs'
    },
    {
      id: '16',
      question: 'How do I submit an approval for my sub-team?',
      answer: 'In the Signoff Tracker tab: 1) Select your authorized sub-team, 2) Fill in Test Suite/Execution Link (formerly Build SHA), 3) Add Build Link (formerly Build Number), 4) Document any Known Issues, 5) Click Submit Approval. You must be authenticated and authorized for the selected sub-team.',
      category: 'signoffs'
    },
    {
      id: '17',
      question: 'How can I see who is authorized to approve for each sub-team?',
      answer: 'Hover over the info icon (ⓘ) next to any sub-team status card to see a tooltip listing all authorized approvers. This information is pulled from team configuration files and shows the email addresses of people who can submit approvals for that sub-team.',
      category: 'signoffs'
    },
    {
      id: '18',
      question: 'What information is captured in each approval?',
      answer: 'Each approval records: Approver email and timestamp, Test Suite/Execution Link, Build Link, Known issues and notes, Sub-team information. Completed approvals show full details when expanded and include clickable links where applicable.',
      category: 'signoffs'
    },

    // Authentication
    {
      id: '19',
      question: 'What authentication methods are supported?',
      answer: 'The platform supports GitHub and Azure Active Directory authentication. Click your profile avatar in the sidebar to sign in. Authentication is required for creating releases, submitting approvals, accessing team-specific data, and viewing authorized approver information.',
      category: 'authentication'
    },
    {
      id: '20',
      question: 'Why can\'t I see the create release button?',
      answer: 'The floating + button for creating releases is only visible to authenticated users. Please sign in through the sidebar profile section to access release creation and other collaborative features.',
      category: 'authentication'
    },
    {
      id: '21',
      question: 'How do team permissions and authorization work?',
      answer: 'Team permissions are based on your authenticated account and team membership. You can only submit approvals for sub-teams you\'re authorized to represent. The system shows "Not Authorized" for restricted actions and displays authorized approvers via hover tooltips.',
      category: 'authentication'
    },

    // Troubleshooting
    {
      id: '22',
      question: 'Why are my metrics showing "Cached" or "Stored Data"?',
      answer: 'This is normal behavior for performance optimization. "Cached" means recent data from your session. "Stored Data" appears when live APIs (SonarQube, Looper, ServiceNow) are unavailable, using database fallback. The database timestamps ensure you see when data was actually updated, not just cached.',
      category: 'troubleshooting'
    },
    {
      id: '23',
      question: 'My metrics aren\'t updating after clicking refresh',
      answer: 'Check the timestamp display - if it shows "Stored Data", the live API may be unavailable. For SonarQube metrics, ensure you\'re on the corporate network. For Looper, verify the service is accessible. The platform automatically falls back to stored database values when live APIs fail.',
      category: 'troubleshooting'
    },
    {
      id: '24',
      question: 'I can\'t submit an approval for my sub-team',
      answer: 'Verify: 1) You\'re signed in, 2) You\'re authorized for the selected sub-team (check hover tooltip), 3) All required fields are filled, 4) The approval stage is unlocked. If issues persist, check browser console for detailed error messages.',
      category: 'troubleshooting'
    },
    {
      id: '25',
      question: 'The confidence score seems stuck or incorrect',
      answer: 'Confidence scores update automatically when all 6 metrics are available. If stuck at 0%, it means not all metrics have been collected yet. The system requires Code Quality, Code Coverage, Test Results, Build Stability, Change Requests, and Changelog data before calculating the final score.',
      category: 'troubleshooting'
    },
    {
      id: '26',
      question: 'How do I report bugs or request new features?',
      answer: 'Use the "Send Email" button at the bottom of this help section to contact support. Include details about your issue, browser information, steps to reproduce problems, or describe the feature you\'d like to see. All feedback is reviewed and helps improve the platform.',
      category: 'troubleshooting'
    }
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const totalPages = Math.ceil(filteredFAQs.length / ITEMS_PER_PAGE);
  const displayedFAQs = filteredFAQs.slice(currentPage * ITEMS_PER_PAGE, (currentPage + 1) * ITEMS_PER_PAGE);

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  // Reset to first page when category or search changes
  useEffect(() => {
    setCurrentPage(0);
    setExpandedFAQ(null);
  }, [selectedCategory, searchQuery]);

  return (
    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-4">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-3">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h1a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1">Help & Support</h1>
            <p className="text-base text-gray-600">Find answers to your questions about the Engineering Excellence platform</p>
          </div>

          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-xl leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-6 h-[600px] flex flex-col">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
              <nav className="space-y-2 flex-1">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={cn(
                      "w-full flex items-center px-3 py-3 rounded-xl text-left transition-all duration-200",
                      selectedCategory === category.id
                        ? "bg-blue-50 text-blue-700 border-2 border-blue-200"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent"
                    )}
                  >
                    <div className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-lg mr-3",
                      selectedCategory === category.id
                        ? "bg-blue-500 text-white"
                        : "bg-gray-100 text-gray-600"
                    )}>
                      {category.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{category.name}</p>
                      <p className="text-xs text-gray-500 truncate">{category.description}</p>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 flex flex-col h-[600px]">
              {/* Results Header */}
              <div className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {selectedCategory === 'all' ? 'All Questions' : categories.find(c => c.id === selectedCategory)?.name}
                  </h2>
                  <div className="text-sm text-gray-500">
                    {filteredFAQs.length > 0 ? (
                      <span>
                        {currentPage * ITEMS_PER_PAGE + 1}-{Math.min((currentPage + 1) * ITEMS_PER_PAGE, filteredFAQs.length)} of {filteredFAQs.length} results
                      </span>
                    ) : (
                      <span>0 results</span>
                    )}
                  </div>
                </div>
              </div>

              {/* FAQ List - Exactly 5 questions */}
              <div className="flex-1 flex flex-col">
                <div className="divide-y divide-gray-200 flex-1">
                  {displayedFAQs.length > 0 ? (
                    displayedFAQs.map((faq) => (
                      <div key={faq.id} className="px-6 py-4">
                        <button
                          onClick={() => toggleFAQ(faq.id)}
                          className="w-full flex items-center justify-between text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg p-2 -m-2"
                        >
                          <h3 className="text-lg font-medium text-gray-900 pr-4">{faq.question}</h3>
                          <div className={cn(
                            "flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 transition-transform duration-200",
                            expandedFAQ === faq.id && "rotate-180 bg-blue-100"
                          )}>
                            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </div>
                        </button>
                        
                        {expandedFAQ === faq.id && (
                          <div className="mt-4 pl-2">
                            <div className="prose prose-sm max-w-none">
                              <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="px-6 py-12 text-center h-full flex flex-col justify-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                      <p className="text-gray-500">Try adjusting your search or browse different categories.</p>
                    </div>
                  )}
                </div>
                
                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between flex-shrink-0">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
                      disabled={currentPage === 0}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",
                        currentPage === 0
                          ? "text-gray-400 cursor-not-allowed"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                      Previous
                    </button>
                    
                    <div className="flex items-center space-x-2">
                      {Array.from({ length: totalPages }, (_, i) => (
                        <button
                          key={i}
                          onClick={() => setCurrentPage(i)}
                          className={cn(
                            "w-8 h-8 rounded-lg text-sm font-medium transition-all duration-200",
                            currentPage === i
                              ? "bg-blue-500 text-white"
                              : "text-gray-700 hover:bg-gray-100"
                          )}
                        >
                          {i + 1}
                        </button>
                      ))}
                    </div>
                    
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
                      disabled={currentPage === totalPages - 1}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",
                        currentPage === totalPages - 1
                          ? "text-gray-400 cursor-not-allowed"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      Next
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                )}
              </div>

              {/* Contact Support - Always visible at bottom */}
              <div className="flex-shrink-0 p-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-b-xl">
                <div className="flex items-center text-white">
                  <div className="flex-shrink-0">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <h3 className="text-sm font-semibold">Still need help?</h3>
                    <p className="text-xs text-blue-100 mt-1">Can't find the answer you're looking for?</p>
                  </div>
                  <div className="ml-4">
                    <a
                      href="mailto:<EMAIL>?subject=Engineering Excellence Platform - Support Request"
                      className="bg-white text-blue-600 px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-blue-50 transition-colors inline-flex items-center"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      Send Email
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;
