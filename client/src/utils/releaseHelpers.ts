import type { Release } from '../types/api';

export const formatDateSafely = (dateString: string): string => {
  if (!dateString) return 'No date';
  
  try {
    const dateParts = dateString.split('-');
    if (dateParts.length === 3) {
      const year = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed in Date constructor
      const day = parseInt(dateParts[2]);
      const date = new Date(year, month, day);
      
      // Return in a consistent format
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    }
    
    // Fallback to standard parsing
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  } catch (error) {
    console.warn('Failed to parse date:', dateString, error);
    return dateString; // Return original string if parsing fails
  }
};

export const formatReleaseType = (type: string): string => {
  switch (type) {
    case 'minor': return 'Minor Release';
    case 'major': return 'Major Release';
    case 'patch': return 'Patch Release';
    case 'hotfix': return 'Hotfix';
    case 'regular': return 'Regular Release';
    case 'emergency': return 'Emergency Release';
    default: return 'Release';
  }
};

export const getReleaseTypeColor = (type: string): string => {
  switch (type) {
    case 'major': return 'bg-red-100 text-red-800';
    case 'minor': return 'bg-blue-100 text-blue-800';
    case 'patch': return 'bg-green-100 text-green-800';
    case 'hotfix': return 'bg-orange-100 text-orange-800';
    case 'regular': return 'bg-gray-100 text-gray-800';
    case 'emergency': return 'bg-red-200 text-red-900';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export const getPlatformsList = (platforms: Release['supportedPlatforms']): string => {
  if (!platforms) return 'Not specified';
  
  // Handle the new platform structure
  if (typeof platforms === 'object' && platforms.mobile && platforms.web && platforms.backend) {
    const activePlatforms = [];
    
    // Check if mobile platform is enabled and has sub-options selected
    if (platforms.mobile.enabled && (platforms.mobile.ios || platforms.mobile.android)) {
      activePlatforms.push('Mobile');
    }
    
    // Check if web platform is enabled and has sub-options selected
    if (platforms.web.enabled && (platforms.web.desktop || platforms.web.mobileWeb)) {
      activePlatforms.push('Web');
    }
    
    // Check if backend platform is enabled
    if (platforms.backend.enabled) {
      activePlatforms.push('Backend');
    }
    
    return activePlatforms.length > 0 ? activePlatforms.join(', ') : 'None specified';
  }
  
  // Fallback for old structure or string/array formats
  if (typeof platforms === 'string') return platforms;
  if (Array.isArray(platforms)) return platforms.join(', ');
  
  return 'Not specified';
};

export const getPlatformIcons = (platforms: Release['supportedPlatforms']): Array<{ icon: string; name: string }> => {
  const iconsList: Array<{ icon: string; name: string }> = [];
  
  // Mobile platforms - only show specific platforms, not category
  if (platforms.mobile?.enabled) {
    if (platforms.mobile.ios) iconsList.push({ icon: '📱', name: 'iOS' });
    if (platforms.mobile.android) iconsList.push({ icon: '🤖', name: 'Android' });
  }
  
  // Web platforms - only show specific platforms, not category
  if (platforms.web?.enabled) {
    if (platforms.web.desktop) iconsList.push({ icon: '🖥️', name: 'Desktop Web' });
    if (platforms.web.mobileWeb) iconsList.push({ icon: '🌐', name: 'Mobile Web' });
  }
  
  // Backend
  if (platforms.backend?.enabled) {
    iconsList.push({ icon: '🛠️', name: 'Backend' });
  }
  
  return iconsList;
};

export const getPlatformIconsString = (platforms: Release['supportedPlatforms']): string => {
  return getPlatformIcons(platforms).map(p => p.icon).join(' ');
};

export const getAvailablePlatforms = (releases: Release[]): string[] => {
  const platformSet = new Set<string>();
  releases.forEach(release => {
    // Check if mobile platform is enabled and has sub-options selected
    if (release.supportedPlatforms.mobile?.enabled && 
        (release.supportedPlatforms.mobile.ios || release.supportedPlatforms.mobile.android)) {
      platformSet.add('Mobile');
    }
    
    // Check if web platform is enabled and has sub-options selected
    if (release.supportedPlatforms.web?.enabled && 
        (release.supportedPlatforms.web.desktop || release.supportedPlatforms.web.mobileWeb)) {
      platformSet.add('Web');
    }
    
    // Check if backend platform is enabled
    if (release.supportedPlatforms.backend?.enabled) {
      platformSet.add('Backend');
    }
  });
  return Array.from(platformSet);
}; 