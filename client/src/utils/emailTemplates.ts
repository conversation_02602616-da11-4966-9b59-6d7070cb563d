// Utility for generating release summary email templates

interface ReleaseSummaryEmailParams {
  release: any;
  teamName: string;
  signoffStatus: any;
  overallConfidence: number;
  url: string;
  releaseDetailsUrl: string;
  generatedDate: string;
  pcfRequests?: any[];
}

function formatPlatforms(platforms: any): string {
  if (!platforms) return 'Not specified';
  if (typeof platforms === 'string') return platforms;
  if (Array.isArray(platforms)) return platforms.join(', ');
  if (typeof platforms === 'object') {
    // Handle the new platform structure with nested objects
    if (platforms.mobile && platforms.web && platforms.backend) {
      const activePlatforms = [];
      
      // Check mobile platform and its sub-options
      if (platforms.mobile.enabled && (platforms.mobile.ios || platforms.mobile.android)) {
        const mobileOptions = [];
        if (platforms.mobile.ios) mobileOptions.push('iOS');
        if (platforms.mobile.android) mobileOptions.push('Android');
        activePlatforms.push(`Mobile (${mobileOptions.join(', ')})`);
      }
      
      // Check web platform and its sub-options
      if (platforms.web.enabled && (platforms.web.desktop || platforms.web.mobileWeb)) {
        const webOptions = [];
        if (platforms.web.desktop) webOptions.push('Desktop');
        if (platforms.web.mobileWeb) webOptions.push('Mobile Web');
        activePlatforms.push(`Web (${webOptions.join(', ')})`);
      }
      
      // Check backend platform
      if (platforms.backend.enabled) {
        activePlatforms.push('Backend');
      }
      
      return activePlatforms.length > 0 ? activePlatforms.join(', ') : 'None specified';
    }
    
    // Fallback for old structure
    const activePlatforms = [];
    if (platforms.web) activePlatforms.push('Web');
    if (platforms.mobile) activePlatforms.push('Mobile');
    if (platforms.api) activePlatforms.push('API');
    return activePlatforms.length > 0 ? activePlatforms.join(', ') : 'None specified';
  }
  return 'Not specified';
}

function formatReleaseType(type: string): string {
  if (!type) return 'Release';
  return type.charAt(0).toUpperCase() + type.slice(1);
}

export function getReleaseSummaryEmail({
  release,
  teamName,
  signoffStatus,
  overallConfidence,
  url,
  releaseDetailsUrl,
  generatedDate,
  pcfRequests
}: ReleaseSummaryEmailParams) {
  // Format PCF requests summary
  const formatPCFRequestsSummary = () => {
    if (!pcfRequests || pcfRequests.length === 0) {
      return 'No PCF requests';
    }

    const totalRequests = pcfRequests.length;
    const approvedRequests = pcfRequests.filter(req => req.status === 'approved').length;
    const pendingRequests = pcfRequests.filter(req => req.status === 'pending').length;
    const rejectedRequests = pcfRequests.filter(req => req.status === 'rejected').length;

    return `${totalRequests} total (${approvedRequests} approved, ${pendingRequests} pending, ${rejectedRequests} rejected)`;
  };

  return `Dear Team,

Please find the below summary and key details for release ${release.releaseName} from team ${teamName}.

═══════════════════════════════════════════════════════════════════════════════════
                                RELEASE SUMMARY
═══════════════════════════════════════════════════════════════════════════════════

📦 Release Name:     ${release.releaseName}
🏢 Team:             ${teamName}
📅 Release Date:     ${new Date(release.releaseRolloutDate || Date.now()).toLocaleDateString('en-US', { 
  weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' 
})}
🏷️  Release Type:    ${formatReleaseType(release.releaseType)}
🌐 Platforms:        ${formatPlatforms(release.supportedPlatforms)}
🔄 Last Updated:     ${new Date(release.updatedAt || Date.now()).toLocaleDateString('en-US', {
  weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit', timeZoneName: 'short'
})}
📝 Description:      ${release.description || 'No description provided'}

═══════════════════════════════════════════════════════════════════════════════════
                                DETAILED METRICS
═══════════════════════════════════════════════════════════════════════════════════

📊 Signoff Progress:    ${signoffStatus?.overallProgress || 0}%
🎯 Confidence Score:    ${overallConfidence}%
✅ Approval Status:     ${signoffStatus?.stages.map((stage: any) => 
  `${stage.name}: ${stage.currentApprovals.filter((a: any) => a.status === 'approved').length}/${stage.minRequiredApprovals}`
).join(', ')}
🔥 PCF Requests:        ${formatPCFRequestsSummary()}

═══════════════════════════════════════════════════════════════════════════════════
                                ACCESS LINKS
═══════════════════════════════════════════════════════════════════════════════════

🔗 Platform Access:   ${url}
📋 Release Details:   ${releaseDetailsUrl}
📊 All Releases:      ${url}/releases

═══════════════════════════════════════════════════════════════════════════════════

This summary was generated automatically by the Engineering Excellence Platform on ${generatedDate}.
For detailed metrics, approvals, and real-time status, please use the platform links above.

Best regards,
Engineering Excellence Platform
`;
} 