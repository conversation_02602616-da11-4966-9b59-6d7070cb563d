// Utility functions for date handling without timezone issues

/**
 * Safely parse a date string without timezone conversion issues
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Date object in local timezone
 */
export const parseDateSafely = (dateString: string): Date => {
  const dateParts = dateString.split('-');
  if (dateParts.length === 3) {
    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
    const day = parseInt(dateParts[2]);
    return new Date(year, month, day);
  }
  return new Date(dateString);
};

/**
 * Format a date object to YYYY-MM-DD string in local timezone
 * @param date - Date object to format
 * @returns Date string in YYYY-MM-DD format
 */
export const formatDateLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Compare two dates by their local date values (ignoring time and timezone)
 * @param date1 - First date
 * @param date2 - Second date
 * @returns true if dates are the same day
 */
export const isSameLocalDate = (date1: Date, date2: Date): boolean => {
  return formatDateLocal(date1) === formatDateLocal(date2);
};

/**
 * Format date safely for display without timezone issues
 * @param dateString - Date string to format
 * @returns Formatted date string for display
 */
export const formatDateSafely = (dateString: string): string => {
  try {
    const date = parseDateSafely(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Format execution time (in minutes and seconds) to a readable format
 * Converts to hours if duration is greater than 100 minutes
 * @param minutes - Minutes part of the duration
 * @param seconds - Seconds part of the duration (optional)
 * @returns Formatted duration string
 */
export const formatExecutionTime = (minutes: number, seconds: number = 0): string => {
  const totalMinutes = minutes + seconds / 60;

  if (totalMinutes >= 100) {
    const hours = Math.floor(totalMinutes / 60);
    const remainingMinutes = Math.floor(totalMinutes % 60);
    return `${hours}h ${remainingMinutes}m`;
  }

  return `${Math.floor(minutes)}m ${Math.round(seconds)}s`;
};

/**
 * Format duration string into a more readable format
 * Converts "XXXm YYs" format into appropriate hours/minutes/seconds
 * @param duration - Duration string in the format "XXXm YYs"
 * @returns Formatted duration string
 */
export const formatDuration = (duration: string): string => {
  if (!duration) return '0s';

  // Extract minutes and seconds using regex
  const regex = /(\d+)m\s*(\d+)s/;
  const matches = duration.match(regex);

  if (!matches) return duration; // Return original if no match

  const minutes = parseInt(matches[1]);
  const seconds = parseInt(matches[2]);

  // Convert to hours if more than 100 minutes
  if (minutes >= 100) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    // Format with appropriate units
    const parts = [];
    if (hours > 0) parts.push(`${hours}h`);
    if (remainingMinutes > 0) parts.push(`${remainingMinutes}m`);
    if (seconds > 0) parts.push(`${seconds}s`);

    return parts.join(' ');
  }

  // For duration less than 100 minutes, keep original format but clean it up
  return `${minutes}m ${seconds}s`;
};

/**
 * Formats a timestamp with timezone information
 * @param timestamp - The timestamp to format
 * @returns Formatted time string with timezone (e.g., "Today 2:45 PM PDT" or "Dec 15 2:45 PM PST")
 */
export const formatTimestampWithTimezone = (timestamp: string | Date): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  const now = new Date();
  
  // Check if it's today
  const isToday = date.toDateString() === now.toDateString();
  
  // Check if it's yesterday
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  const isYesterday = date.toDateString() === yesterday.toDateString();
  
  const timeString = date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short',
  });
  
  if (isToday) {
    return `Today ${timeString}`;
  } else if (isYesterday) {
    return `Yesterday ${timeString}`;
  } else {
    // For older dates, show month and day
    const dateString = date.toLocaleDateString([], {
      month: 'short',
      day: 'numeric',
    });
    return `${dateString} ${timeString}`;
  }
};

/**
 * Gets the current timezone abbreviation
 * @returns Timezone abbreviation (e.g., "PDT", "EST")
 */
export const getCurrentTimezone = (): string => {
  const formatter = new Intl.DateTimeFormat('en-US', { timeZoneName: 'short' });
  const parts = formatter.formatToParts(new Date());
  const timeZonePart = parts.find(part => part.type === 'timeZoneName');
  return timeZonePart?.value || 'UTC';
};

/**
 * Formats a date with full timezone information
 * @param timestamp - The timestamp to format
 * @returns Formatted date string with timezone
 */
export const formatDateWithTimezone = (timestamp: string | Date): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  
  return date.toLocaleString([], {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short',
  });
};
