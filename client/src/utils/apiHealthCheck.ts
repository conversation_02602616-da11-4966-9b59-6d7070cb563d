export interface ApiHealthResult {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}

export interface ApiTimeoutConfig {
  timeout: number;
  description: string;
}

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';

// Timeout configurations
const DEFAULT_TIMEOUT = 5000; // 5 seconds
const EXTENDED_TIMEOUT = 15000; // 15 seconds

// Cache for API health status (valid for 2 minutes)
const healthCache = new Map<string, { result: ApiHealthResult; timestamp: number }>();
const HEALTH_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

// Constants
const HEALTH_CHECK_ENDPOINTS = {
  github: '/api/health/github',
  sonarqube: '/api/health/sonarqube',
  looper: '/api/health/looper',
  servicenow: '/api/health/servicenow'
};

/**
 * Quick health check for an API endpoint
 * @param endpoint - The endpoint to check (relative to base URL)
 * @param quickTimeout - Timeout for the health check itself (default 3 seconds)
 */
export const checkApiHealth = async (endpoint: string, quickTimeout: number = 3000): Promise<ApiHealthResult> => {
  const startTime = Date.now();
  
  // Check cache first
  const cached = healthCache.get(endpoint);
  if (cached && (Date.now() - cached.timestamp) < HEALTH_CACHE_DURATION) {
    return cached.result;
  }

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), quickTimeout);
    
    await fetch(endpoint, {
      method: 'GET',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    const result = {
      isHealthy: true,
      responseTime: Date.now() - startTime
    };
    
    // Cache the result
    healthCache.set(endpoint, { result, timestamp: Date.now() });
    
    return result;
  } catch (error: any) {
    const result = {
      isHealthy: false,
      responseTime: Date.now() - startTime,
      error: error.message
    };
    
    // Cache the negative result too
    healthCache.set(endpoint, { result, timestamp: Date.now() });
    
    return result;
  }
};

/**
 * Get appropriate timeout configuration based on API health
 * @param apiType - Type of API (e.g., 'github', 'sonarqube', 'looper', 'servicenow')
 * @param healthEndpoint - Health check endpoint for the API
 */
export const getAdaptiveTimeout = async (
  apiType: string,
  healthEndpoint: string
): Promise<ApiTimeoutConfig> => {
  try {
    const healthResult = await checkApiHealth(healthEndpoint);
    
    if (healthResult.isHealthy) {
      // API is healthy - use longer timeout for better reliability
      if (healthResult.responseTime < 1000) {
        // Very responsive API
        return {
          timeout: 30000, // 30 seconds
          description: `${apiType} API is healthy (${healthResult.responseTime}ms response) - using extended timeout`
        };
      } else {
        // Slower but healthy API
        return {
          timeout: 45000, // 45 seconds
          description: `${apiType} API is slow but healthy (${healthResult.responseTime}ms response) - using extended timeout`
        };
      }
    } else {
      // API is not responding well - use short timeout for quick fallback
      return {
        timeout: 5000, // 5 seconds
        description: `${apiType} API appears unhealthy (${healthResult.error}) - using short timeout for quick fallback`
      };
    }
  } catch (error) {
    // Health check failed - use short timeout
    return {
      timeout: 5000, // 5 seconds
      description: `${apiType} API health check failed - using short timeout for quick fallback`
    };
  }
};

/**
 * Get adaptive timeout for GitHub API based on health check
 */
export const getGitHubTimeout = async (): Promise<ApiTimeoutConfig> => {
  try {
    await fetch(`${API_BASE_URL}${HEALTH_CHECK_ENDPOINTS.github}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    // API is healthy, use normal timeout
    return {
      timeout: DEFAULT_TIMEOUT,
      description: '✅ GitHub API is healthy, using normal timeout'
    };
  } catch (error) {
    // API is not healthy, use extended timeout
    return {
      timeout: EXTENDED_TIMEOUT,
      description: '⚠️ GitHub API health check failed, using extended timeout'
    };
  }
};

/**
 * Get timeout configuration for SonarQube API
 */
export const getSonarQubeTimeout = async (): Promise<ApiTimeoutConfig> => {
  return getAdaptiveTimeout('SonarQube', HEALTH_CHECK_ENDPOINTS.sonarqube);
};

/**
 * Get timeout configuration for Looper API
 */
export const getLooperTimeout = async (): Promise<ApiTimeoutConfig> => {
  return getAdaptiveTimeout('Looper', HEALTH_CHECK_ENDPOINTS.looper);
};

/**
 * Get timeout configuration for ServiceNow API
 */
export const getServiceNowTimeout = async (): Promise<ApiTimeoutConfig> => {
  return getAdaptiveTimeout('ServiceNow', HEALTH_CHECK_ENDPOINTS.servicenow);
};

/**
 * Clear health cache (useful for testing or forced refresh)
 */
export const clearHealthCache = (): void => {
  healthCache.clear();
}; 