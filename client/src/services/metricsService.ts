import axios from 'axios';
import type { CosmosTeam } from './api';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';

// Configure axios instance for metrics
const metricsApi = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 15000,
  withCredentials: true,
});

// Request interceptor to add auth token if available
metricsApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
metricsApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
    }
    return Promise.reject(error);
  }
);

export interface MetricsApiResponse {
  component?: {
    measures?: Array<{
      metric: string;
      value?: string;
      period?: {
        value: string;
      };
    }>;
  };
}

export interface ProcessedMetrics {
  [key: string]: string;
}

// Process SonarQube measures response into a flat object
export function processMetrics(response: MetricsApiResponse): ProcessedMetrics {
  if (!response?.component?.measures) {
    throw new Error('Invalid response format from SonarQube API');
  }

  return response.component.measures.reduce((acc: ProcessedMetrics, measure) => {
    // Handle both regular metrics and period-based new code metrics
    if (measure.period && measure.period.value !== undefined) {
      // New code metrics have period.value
      acc[measure.metric] = measure.period.value;
    } else if (measure.value !== undefined) {
      // Regular metrics have direct value
      acc[measure.metric] = measure.value;
    }
    return acc;
  }, {});
}

// Stored metrics fetchers for releases
export async function fetchStoredCodeQualityMetrics(releaseId: string): Promise<ProcessedMetrics> {
  const response = await metricsApi.get(`/metrics/${releaseId}/stored`);
  
  if (response.data.success && response.data.data?.releaseMetrics?.codeQuality?.rawData) {
    return response.data.data.releaseMetrics.codeQuality.rawData;
  }
  
  throw new Error('No stored code quality metrics found for this release');
}

export async function fetchStoredCodeCoverageMetrics(releaseId: string): Promise<ProcessedMetrics> {
  const response = await metricsApi.get(`/metrics/${releaseId}/stored`);
  
  if (response.data.success && response.data.data?.releaseMetrics?.codeCoverage?.rawData) {
    return response.data.data.releaseMetrics.codeCoverage.rawData;
  }
  
  throw new Error('No stored code coverage metrics found for this release');
}

export async function fetchStoredTestResultsMetrics(releaseId: string): Promise<ProcessedMetrics> {
  const response = await metricsApi.get(`/metrics/${releaseId}/stored`);
  
  if (response.data.success && response.data.data?.releaseMetrics?.testResults?.rawData) {
    return response.data.data.releaseMetrics.testResults.rawData;
  }
  
  throw new Error('No stored test results metrics found for this release');
}

// Stored change requests fetcher for releases
export async function fetchStoredChangeRequestsMetrics(releaseId: string): Promise<any> {
  const response = await metricsApi.get(`/metrics/${releaseId}/stored`);

  if (response.data.success && response.data.data?.releaseMetrics?.changeRequests) {
    return response.data.data.releaseMetrics.changeRequests;
  }

  throw new Error('No stored change requests found for this release');
}

// Change Requests Metrics Service
export async function fetchChangeRequestsMetrics(team: CosmosTeam, releaseId?: string): Promise<any> {
  const result = await fetchChangeRequestsMetricsWithMetadata(team, releaseId);
  return result.data;
}

// Change Requests Metrics Service with metadata (from working code)
export async function fetchChangeRequestsMetricsWithMetadata(team: CosmosTeam, releaseId?: string): Promise<MetricsResult<any>> {
  const fetchLive = async (): Promise<any> => {
    if (!team.metadata?.serviceNowId) {
      throw new Error('Team does not have ServiceNow ID configured');
    }

    // Use ServiceNow API to fetch change requests
    const response = await metricsApi.get(
      `/servicenow/change-requests/${releaseId}`
    );

    // Check if the server returned fallback data
    if (response.data.isFromStored) {
      console.log('🔍 Server returned stored fallback data - will mark as stored');
      // If server used fallback, we need to indicate this to the smart fallback logic
      // by throwing a special error that will trigger the stored data path
      const fallbackError = new Error('Server used stored fallback');
      (fallbackError as any).isServerFallback = true;
      (fallbackError as any).fallbackData = response.data;
      throw fallbackError;
    }

    return response.data;
  };

  const fetchStored = async (): Promise<any> => {
    if (!releaseId) {
      throw new Error('No releaseId provided for stored data retrieval');
    }
    return await fetchStoredChangeRequestsMetrics(releaseId);
  };

  // Custom smart fallback that handles server-side fallback
  try {
    console.log(`🔍 Starting smart fetch for Change Requests`);

    // Try live data first
    console.log(`📡 Attempting to fetch live Change Requests`);
    const liveData = await fetchLive();
    console.log(`✅ Successfully fetched live Change Requests`);
    return { data: liveData, isFromStored: false };

  } catch (liveError: any) {
    // Check if this is a server-side fallback
    if (liveError.isServerFallback && liveError.fallbackData) {
      console.log(`✅ Using server-side stored fallback for Change Requests`);
      return { data: liveError.fallbackData, isFromStored: true };
    }

    console.warn(`❌ Live Change Requests API failed:`, liveError.message);

    // Use client-side fallback for other errors
    if (shouldUseFallbackData(liveError, releaseId)) {
      try {
        console.log(`🔄 Falling back to stored Change Requests for release: ${releaseId}`);
        const storedData = await fetchStored();
        console.log(`✅ Successfully retrieved stored Change Requests fallback`);
        return { data: storedData, isFromStored: true };
      } catch (storedError: any) {
        console.error(`❌ Both live and stored Change Requests failed:`, {
          liveError: liveError.message,
          storedError: storedError.message
        });
        throw liveError;
      }
    } else {
      console.log(`❌ Change Requests error does not warrant fallback, re-throwing:`, liveError.message);
      throw liveError;
    }
  }
}

// Stored changelog fetcher for releases  
export async function fetchStoredChangelogMetrics(releaseId: string): Promise<any> {
  const response = await metricsApi.get(`/metrics/${releaseId}/stored`);

  if (response.data.success && response.data.data?.releaseMetrics) {
    const releaseMetrics = response.data.data.releaseMetrics;

    // Check for 'changeLog' first (current naming convention)
    if (releaseMetrics.changeLog?.rawData) {
      return releaseMetrics.changeLog.rawData;
    }

    // Fallback to 'changelog' naming convention
    if (releaseMetrics.changelog?.rawData) {
      return releaseMetrics.changelog.rawData;
    }

    // If no rawData, try to return the whole object (for backward compatibility)
    if (releaseMetrics.changeLog) {
      return releaseMetrics.changeLog;
    }

    if (releaseMetrics.changelog) {
      return releaseMetrics.changelog;
    }
  }

  throw new Error('No stored changelog data found for this release');
}

// Enhanced error classification and fallback decision logic (from working code)
export function shouldUseFallbackData(error: any, releaseId?: string): boolean {
  // If no releaseId, we can't use stored fallback
  if (!releaseId) {
    console.log('❌ No releaseId provided, cannot use stored fallback');
    return false;
  }

  // Network connectivity issues (definitely use fallback)
  const networkErrors = [
    'NetworkError',
    'Failed to fetch',
    'ECONNREFUSED',
    'ENOTFOUND',
    'EHOSTUNREACH',
    'ENETUNREACH',
    'ECONNRESET',
    'ETIMEDOUT'
  ];

  // SSL/Certificate issues (common in corporate environments)
  const sslErrors = [
    'certificate',
    'SSL',
    'CERT_',
    'self signed certificate',
    'unable to verify the first certificate'
  ];

  // Server/Service errors (API is reachable but failing)
  const serverErrors = [
    404, 500, 502, 503, 504, // HTTP status codes
    'Request failed with status code 404',
    'Request failed with status code 500',
    'Request failed with status code 502',
    'Request failed with status code 503',
    'Request failed with status code 504',
    'API Error 404',
    'API Error 500',
    'API Error 502',
    'API Error 503',
    'API Error 504',
    'Internal Server Error',
    'Service Unavailable',
    'Bad Gateway',
    'Gateway Timeout',
    'Not Found',
    'Release not found', // Specific to Build Stability API
    '"error":"Release not found"', // JSON structured error
    'error":"Release not found', // Partial JSON match
    'Team not found',
    'Project not found',
    'Branch not found'
  ];

  // Timeout errors (service not responding)
  const timeoutErrors = [
    'timeout',
    'AbortError',
    'TIMEOUT',
    'Request timeout'
  ];

  // VPN/Corporate network specific errors
  const vpnErrors = [
    'ENOTFOUND',
    'getaddrinfo ENOTFOUND',
    'DNS lookup failed',
    'Name resolution failed'
  ];

  const errorMessage = error?.message || '';
  const errorCode = error?.code || '';
  const statusCode = error?.response?.status;

  // Also check if error response contains structured error data
  const errorResponseText = error?.response?.data ? JSON.stringify(error.response.data) : '';
  const fullErrorContext = `${errorMessage} ${errorCode} ${errorResponseText}`.toLowerCase();

  // Check each category of errors
  const hasNetworkError = networkErrors.some(err =>
    errorMessage.includes(err) || errorCode.includes(err)
  );

  const hasSSLError = sslErrors.some(err =>
    errorMessage.includes(err) || errorCode.includes(err)
  );

  const hasServerError = serverErrors.some(err => {
    if (typeof err === 'number') {
      return statusCode === err;
    }
    // Check in both original message and full context (case-insensitive)
    return errorMessage.includes(err) ||
           errorCode.includes(err) ||
           fullErrorContext.includes(err.toLowerCase());
  });

  const hasTimeoutError = timeoutErrors.some(err =>
    errorMessage.includes(err) || errorCode.includes(err)
  );

  const hasVPNError = vpnErrors.some(err =>
    errorMessage.includes(err) || errorCode.includes(err)
  );

  // Log the error analysis for debugging
  console.log('🔍 Enhanced Error Analysis:', {
    errorMessage,
    errorCode,
    statusCode,
    errorResponseText,
    fullErrorContext,
    hasNetworkError,
    hasSSLError,
    hasServerError,
    hasTimeoutError,
    hasVPNError
  });

  // Decision logic: Use fallback for any of these conditions
  const shouldFallback = hasNetworkError || hasSSLError || hasServerError || hasTimeoutError || hasVPNError;

  if (shouldFallback) {
    console.log('✅ Error qualifies for stored data fallback');
  } else {
    console.log('❌ Error does not qualify for stored data fallback (likely auth/permission issue)');
  }

  return shouldFallback;
}

// Enhanced cache-aware data fetching strategy (from working code)
async function fetchWithSmartFallback<T>(
  fetchLiveData: () => Promise<T>,
  fetchStoredData: () => Promise<T>,
  releaseId?: string,
  metricName?: string
): Promise<{ data: T; isFromStored: boolean }> {
  console.log(`🔍 Starting smart fetch for ${metricName || 'metric'}`);

  try {
    // Always try live data first for fresh information
    console.log(`📡 Attempting to fetch live ${metricName || 'data'}`);
    const liveData = await fetchLiveData();
    console.log(`✅ Successfully fetched live ${metricName || 'data'}`);
    return { data: liveData, isFromStored: false };

  } catch (liveError: any) {
    console.warn(`❌ Live ${metricName || 'API'} failed:`, liveError.message);

    // Use enhanced fallback decision logic
    if (shouldUseFallbackData(liveError, releaseId)) {
      try {
        console.log(`🔄 Falling back to stored ${metricName || 'data'} for release: ${releaseId}`);
        const storedData = await fetchStoredData();
        console.log(`✅ Successfully retrieved stored ${metricName || 'data'} fallback`);
        return { data: storedData, isFromStored: true };
      } catch (storedError: any) {
        console.error(`❌ Both live and stored ${metricName || 'data'} failed:`, {
          liveError: liveError.message,
          storedError: storedError.message
        });
        // Re-throw the original live error as it's more informative
        throw liveError;
      }
    } else {
      // For auth/permission errors, don't use fallback - user needs to know
      console.log(`❌ ${metricName || 'API'} error does not warrant fallback, re-throwing:`, liveError.message);
      throw liveError;
    }
  }
}

// Enhanced result interface (from working code)
export interface MetricsResult<T> {
  data: T;
  isFromStored: boolean;
}

// Stored build stability fetcher for releases
export async function fetchStoredBuildStabilityMetrics(releaseId: string): Promise<any> {
  const response = await metricsApi.get(`/metrics/${releaseId}/stored`);
  
  if (response.data.success && response.data.data?.releaseMetrics?.buildStability) {
    return response.data.data.releaseMetrics.buildStability;
  }
  
  throw new Error('No stored build stability data found for this release');
}

// Code Quality Metrics Service
export async function fetchCodeQualityMetrics(team: CosmosTeam, releaseId?: string): Promise<ProcessedMetrics> {
  const result = await fetchCodeQualityMetricsWithMetadata(team, releaseId);
  return result.data;
}

// Code Quality Metrics Service with metadata (from working code)
export async function fetchCodeQualityMetricsWithMetadata(team: CosmosTeam, releaseId?: string): Promise<MetricsResult<ProcessedMetrics>> {
  const fetchLive = async (): Promise<ProcessedMetrics> => {
    const metrics = [
      'bugs', 'vulnerabilities', 'code_smells', 'ncloc', 'complexity',
      'reliability_rating', 'security_rating', 'sqale_rating', 'coverage',
      'duplicated_lines_density', 'tests', 'new_bugs', 'new_vulnerabilities',
      'new_code_smells', 'new_lines', 'new_coverage', 'new_duplicated_lines_density',
      'new_reliability_rating', 'new_security_rating', 'new_maintainability_rating'
    ].join(',');

    const response = await metricsApi.get<MetricsApiResponse>(
      `/sonarqube/teams/${encodeURIComponent(team.teamName)}/metrics?metrics=${metrics}`
    );

    return processMetrics(response.data);
  };

  const fetchStored = async (): Promise<ProcessedMetrics> => {
    if (!releaseId) {
      throw new Error('No releaseId provided for stored data retrieval');
    }
    return await fetchStoredCodeQualityMetrics(releaseId);
  };

  return await fetchWithSmartFallback(
    fetchLive,
    fetchStored,
    releaseId,
    'Code Quality'
  );
}

// Code Coverage Metrics Service
export async function fetchCodeCoverageMetrics(team: CosmosTeam, releaseId?: string): Promise<ProcessedMetrics> {
  const result = await fetchCodeCoverageMetricsWithMetadata(team, releaseId);
  return result.data;
}

// Code Coverage Metrics Service with metadata (from working code)
export async function fetchCodeCoverageMetricsWithMetadata(team: CosmosTeam, releaseId?: string): Promise<MetricsResult<ProcessedMetrics>> {
  const fetchLive = async (): Promise<ProcessedMetrics> => {
    const response = await metricsApi.get<MetricsApiResponse>(
      `/sonarqube/teams/${encodeURIComponent(team.teamName)}/coverage`
    );

    return processMetrics(response.data);
  };

  const fetchStored = async (): Promise<ProcessedMetrics> => {
    if (!releaseId) {
      throw new Error('No releaseId provided for stored data retrieval');
    }
    return await fetchStoredCodeCoverageMetrics(releaseId);
  };

  return await fetchWithSmartFallback(
    fetchLive,
    fetchStored,
    releaseId,
    'Code Coverage'
  );
}

// Test Results Metrics Service
export async function fetchTestResultsMetrics(team: CosmosTeam, releaseId?: string): Promise<ProcessedMetrics> {
  const result = await fetchTestResultsMetricsWithMetadata(team, releaseId);
  return result.data;
}

// Test Results Metrics Service with metadata (from working code)
export async function fetchTestResultsMetricsWithMetadata(team: CosmosTeam, releaseId?: string): Promise<MetricsResult<ProcessedMetrics>> {
  const fetchLive = async (): Promise<ProcessedMetrics> => {
    // For test results, we can use the coverage endpoint since it includes test metrics
    const response = await metricsApi.get<MetricsApiResponse>(
      `/sonarqube/teams/${encodeURIComponent(team.teamName)}/coverage`
    );

    return processMetrics(response.data);
  };

  const fetchStored = async (): Promise<ProcessedMetrics> => {
    if (!releaseId) {
      throw new Error('No releaseId provided for stored data retrieval');
    }
    return await fetchStoredTestResultsMetrics(releaseId);
  };

  return await fetchWithSmartFallback(
    fetchLive,
    fetchStored,
    releaseId,
    'Test Results'
  );
}

// Build Stability Metrics Service
export async function fetchBuildStabilityMetrics(team: CosmosTeam, releaseId?: string, releaseBranch?: string): Promise<any> {
  const result = await fetchBuildStabilityMetricsWithMetadata(team, releaseId, releaseBranch);
  return result.data;
}

// Build Stability Metrics Service with metadata (from working code)
export async function fetchBuildStabilityMetricsWithMetadata(team: CosmosTeam, releaseId?: string, releaseBranch?: string): Promise<MetricsResult<any>> {
  const fetchLive = async (): Promise<any> => {
    if (!team.metadata?.looper_team_id) {
      throw new Error('Team does not have Looper team ID configured');
    }

    // Determine the branch to use: releaseBranch > repo_default_branch > 'main'
    const branch = releaseBranch || team.metadata?.repo_default_branch || 'main';
    console.log(`🔍 Using branch for build stability: ${branch}`);

    // Use team-based Looper API
    const response = await metricsApi.get(
      `/looper/teams/${encodeURIComponent(team.id)}/stability?branch=${encodeURIComponent(branch)}`
    );



    // Check if the server returned fallback data
    if (response.data.isFromStored) {
      // If server used fallback, we need to indicate this to the smart fallback logic
      const fallbackError = new Error('Server used stored fallback');
      (fallbackError as any).isServerFallback = true;
      (fallbackError as any).fallbackData = response.data.data;
      throw fallbackError;
    }

    // Extract the actual data from the response
    if (response.data.success && response.data.data) {
      return response.data.data;
    } else {
      throw new Error('Invalid response format from build stability API');
    }
  };

  const fetchStored = async (): Promise<any> => {
    if (!releaseId) {
      throw new Error('No releaseId provided for stored data retrieval');
    }
    return await fetchStoredBuildStabilityMetrics(releaseId);
  };

  // Custom smart fallback that handles server-side fallback
  try {
    // Try live data first
    const liveData = await fetchLive();
    return { data: liveData, isFromStored: false };

  } catch (liveError: any) {
    // Check if this is a server-side fallback
    if (liveError.isServerFallback && liveError.fallbackData) {
      return { data: liveError.fallbackData, isFromStored: true };
    }

    console.warn(`❌ Live Build Stability API failed:`, liveError.message);

    // Use client-side fallback for other errors
    if (shouldUseFallbackData(liveError, releaseId)) {
      try {
        console.log(`🔄 Falling back to stored Build Stability for release: ${releaseId}`);
        const storedData = await fetchStored();
        console.log(`✅ Successfully retrieved stored Build Stability fallback`);
        return { data: storedData, isFromStored: true };
      } catch (storedError: any) {
        console.error(`❌ Both live and stored Build Stability failed:`, {
          liveError: liveError.message,
          storedError: storedError.message
        });
        throw liveError;
      }
    } else {
      console.log(`❌ Build Stability error does not warrant fallback, re-throwing:`, liveError.message);
      throw liveError;
    }
  }
}

// Security Metrics Service (placeholder - adjust endpoint as needed)
export async function fetchSecurityMetrics(team: CosmosTeam): Promise<any> {
  // This would be replaced with actual security metrics endpoint
  const response = await metricsApi.get(
    `/security-metrics/teams/${encodeURIComponent(team.teamName)}`
  );

  return response.data;
}

// Unified metrics fetcher factory
export function createMetricsFetcher(metricType: string) {
  return (team: CosmosTeam) => {
    switch (metricType) {
      case 'code-quality':
        return fetchCodeQualityMetrics(team);
      case 'code-coverage':
        return fetchCodeCoverageMetrics(team);
      case 'test-results':
        return fetchTestResultsMetrics(team);
      case 'build-stability':
        return fetchBuildStabilityMetrics(team);
      case 'security':
        return fetchSecurityMetrics(team);
      default:
        throw new Error(`Unknown metric type: ${metricType}`);
    }
  };
}

// Error classification helper
export function isMetricsError(error: any): boolean {
  return error?.response?.status >= 400 || error?.code === 'NETWORK_ERROR';
}

export function getMetricsErrorMessage(error: any): string {
  if (error?.response?.status === 404) {
    return 'Team or project not found in SonarQube';
  }
  if (error?.response?.status === 403) {
    return 'Access denied to SonarQube metrics';
  }
  if (error?.response?.status >= 500) {
    return 'SonarQube server error';
  }
  if (error?.code === 'NETWORK_ERROR') {
    return 'Network connection error';
  }
  return error?.message || 'Failed to fetch metrics';
} 