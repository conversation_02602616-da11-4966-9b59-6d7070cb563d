import api from './api';
import { API_ENDPOINTS } from '../config/apiConfig';

export interface VaultSecret {
  name: string;
  value: string;
}

export interface VaultResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export const vaultService = {
  /**
   * Get a secret from Azure Key Vault
   * @param secretName - Name of the secret to retrieve
   * @returns Promise resolving to the secret value
   */
  async getSecret(secretName: string): Promise<string> {
    try {
      const response = await api.get(`${API_ENDPOINTS.VAULT_SECRET}/${secretName}`);
      return response.data.value;
    } catch (error) {
      console.error(`Error fetching secret ${secretName}:`, error);
      throw error;
    }
  },

  /**
   * Set a secret in Azure Key Vault
   * @param secretName - Name of the secret
   * @param value - Value of the secret
   * @returns Promise resolving to success response
   */
  async setSecret(secretName: string, value: string): Promise<VaultResponse> {
    try {
      const response = await api.post(API_ENDPOINTS.VAULT_SECRET, {
        name: secretName,
        value: value
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting secret ${secretName}:`, error);
      throw error;
    }
  },

  /**
   * Delete a secret from Azure Key Vault
   * @param secretName - Name of the secret to delete
   * @returns Promise resolving to success response
   */
  async deleteSecret(secretName: string): Promise<VaultResponse> {
    try {
      const response = await api.delete(`${API_ENDPOINTS.VAULT_SECRET}/${secretName}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting secret ${secretName}:`, error);
      throw error;
    }
  },

  /**
   * List all secrets in Azure Key Vault
   * @returns Promise resolving to array of secret names
   */
  async listSecrets(): Promise<string[]> {
    try {
      const response = await api.get(API_ENDPOINTS.VAULT_SECRETS);
      return response.data.secrets || [];
    } catch (error) {
      console.error('Error listing secrets:', error);
      throw error;
    }
  }
}; 