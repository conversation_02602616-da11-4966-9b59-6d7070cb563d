import axios from 'axios';
import type { ApiResponse, Release, CreateReleaseRequest, UpdateReleaseRequest } from '../types/api';
import { formatDateLocal } from '../utils/dateUtils';

// Re-export types for use by other modules
export type { Release, CreateReleaseRequest, UpdateReleaseRequest } from '../types/api';

// Additional type alias for compatibility
export type CreateReleaseData = CreateReleaseRequest;

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 10000,
  withCredentials: true,
});

// Request interceptor to add auth token if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Enhanced release interface for internal use with additional computed fields
export interface EnhancedRelease extends Release {
  signoffProgress?: number;
  progress?: number;
  isConfidenceProcessing?: boolean;
  confidenceMetrics?: {
    overallScore: number;
    isProcessing?: boolean;
    [key: string]: any;
  };
  releaseMetrics?: {
    confidenceMetrics?: {
      overallScore: number;
    };
    [key: string]: any;
  };
}

export const releaseAPI = {
  // Get all releases
  getReleases: () => api.get<ApiResponse<Release[]>>('/releases'),
  
  // Get a single release
  getRelease: (id: string) => api.get<ApiResponse<Release>>(`/releases/${id}`),
  
  // Create a new release
  createRelease: (data: CreateReleaseRequest) => api.post<ApiResponse<Release>>('/releases', data),
  
  // Update a release
  updateRelease: (id: string, data: UpdateReleaseRequest) => 
    api.put<ApiResponse<Release>>(`/releases/${id}`, data),
  
  // Delete a release
  deleteRelease: (id: string) => api.delete<ApiResponse<void>>(`/releases/${id}`),
  
  // Get releases for a specific date range
  getReleasesByDateRange: (startDate: string, endDate: string) => 
    api.get<ApiResponse<Release[]>>(`/releases?startDate=${startDate}&endDate=${endDate}`),
  
  // Get releases for a specific team
  getReleasesByTeam: (teamId: string) => 
    api.get<ApiResponse<Release[]>>(`/releases?teamId=${teamId}`),
};

// Release service class for additional functionality
class ReleaseService {
  async createRelease(data: CreateReleaseRequest): Promise<Release> {
    try {
      const response = await releaseAPI.createRelease(data);
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      throw new Error(response.data.message || 'Failed to create release');
    } catch (error: any) {
      console.error('Error creating release:', error);
      throw new Error(error.response?.data?.message || 'Failed to create release');
    }
  }

  async getReleases(): Promise<EnhancedRelease[]> {
    try {
      const response = await releaseAPI.getReleases();
      if (response.data.success && response.data.data) {
        const releases = response.data.data;
        
        // Map releases to include signoff progress from the progress field and confidence score
        const mappedReleases = releases.map(release => ({
          ...release,
          signoffProgress: (release as any).progress || 0,
          confidenceScore: (release as any).confidenceMetrics?.overallScore || 
                          release.confidenceScore || 
                          (release as any).releaseMetrics?.confidenceMetrics?.overallScore || 
                          0, // No fallback dummy data - use 0 if no real metrics available
          // Add processing state indicator
          isConfidenceProcessing: (release as any).confidenceMetrics?.isProcessing || 
                                 ((release as any).confidenceMetrics?.overallScore === 0 && 
                                  !(release as any).confidenceMetrics?.isProcessing === false)
        }));
        
        console.log('✅ Release Service: Using enhanced platform structure', {
          totalReleases: mappedReleases.length,
          platformStructure: mappedReleases.length > 0 ? {
            mobile: mappedReleases[0].supportedPlatforms.mobile,
            web: mappedReleases[0].supportedPlatforms.web,
            backend: mappedReleases[0].supportedPlatforms.backend
          } : null,
          progressValues: mappedReleases.map(r => ({ 
            name: r.releaseName, 
            progress: r.signoffProgress,
            confidence: r.confidenceScore,
            isProcessing: (r as any).isConfidenceProcessing
          }))
        });
        
        return mappedReleases;
      }
      return [];
    } catch (error) {
      console.error('Error fetching releases:', error);
      return [];
    }
  }

  async getReleasesForDate(date: Date): Promise<EnhancedRelease[]> {
    try {
      const dateStr = formatDateLocal(date);
      
      const response = await releaseAPI.getReleasesByDateRange(dateStr, dateStr);
      if (response.data.success && response.data.data) {
        const releases = response.data.data;
        
        // Map releases to include signoff progress from the progress field
        return releases.map(release => ({
          ...release,
          signoffProgress: (release as any).progress || 0
        }));
      }
      return [];
    } catch (error) {
      console.error('Error fetching releases for date:', error);
      return [];
    }
  }
}

export const releaseService = new ReleaseService(); 