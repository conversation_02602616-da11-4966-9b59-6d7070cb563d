import { apiClient } from './api-client';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export class ChatService {
  private static instance: ChatService;
  private conversationHistory: ChatMessage[] = [];

  private constructor() {}

  public static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  public async sendMessage(releaseId: string, message: string): Promise<ChatMessage> {
    try {
      const response = await apiClient.post(`/api/chat/${releaseId}/message`, {
        message,
        conversationHistory: this.conversationHistory
      });

      const newMessage = response.data as ChatMessage;
      this.conversationHistory.push({ role: 'user', content: message });
      this.conversationHistory.push(newMessage);

      return newMessage;
    } catch (error: any) {
      console.error('Error sending chat message:', error);

      // Preserve the original error information for better error handling
      if (error?.response) {
        // Server responded with an error status
        const serverError = new Error(error.response.data?.error || 'Server error');
        (serverError as any).response = error.response;
        throw serverError;
      } else if (error?.request) {
        // Request was made but no response received
        const networkError = new Error('Network error - no response received');
        (networkError as any).code = 'NETWORK_ERROR';
        throw networkError;
      } else {
        // Something else happened
        throw new Error(error?.message || 'Failed to send chat message');
      }
    }
  }

  public getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }

  public clearConversationHistory(): void {
    this.conversationHistory = [];
  }
} 