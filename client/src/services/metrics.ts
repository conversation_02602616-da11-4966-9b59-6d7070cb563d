import axios from 'axios';
import type { CosmosTeam } from './api';
import { fetchCodeQualityMetrics, fetchCodeCoverageMetrics, fetchTestResultsMetrics } from './metricsService';

const API_BASE_URL = `${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001'}/api`;

// Types for metrics responses
export interface MetricScore {
  score: number;
  label: string;
  lastUpdated: string;
  status: 'success' | 'warning' | 'error';
  details?: Record<string, any>;
}

export interface ReleaseMetrics {
  codeQuality?: MetricScore & {
    bugs?: number;
    vulnerabilities?: number;
    codeSmells?: number;
    coverage?: number;
    maintainabilityRating?: 'A' | 'B' | 'C' | 'D' | 'E';
    reliabilityRating?: 'A' | 'B' | 'C' | 'D' | 'E';
    securityRating?: 'A' | 'B' | 'C' | 'D' | 'E';
  };
  codeCoverage?: MetricScore & {
    coverage?: number;
    linesToCover?: number;
    uncoveredLines?: number;
    lineCoverage?: number;
    conditionsToCover?: number;
    uncoveredConditions?: number;
    tests?: number;
    testErrors?: number;
    testFailures?: number;
    skippedTests?: number;
    testSuccessDensity?: number;
    testExecutionTime?: number;
  };
  testResults?: MetricScore & {
    totalTests?: number;
    testErrors?: number;
    testFailures?: number;
    skippedTests?: number;
    testSuccessDensity?: number;
    testExecutionTime?: number;
  };
}

export interface ConfidenceMetrics {
  overallScore: number;
  lastCalculated: string;
  weightings: {
    codeQuality: number;
    testCoverage: number;
    testResults: number;
  };
  trending: {
    direction: 'up' | 'down' | 'stable';
    changePercent: number;
    previousScore?: number;
  };
}

export interface MetricsData {
  releaseId: string;
  releaseMetrics: ReleaseMetrics;
  confidenceMetrics: ConfidenceMetrics;
  lastUpdated: string;
  refreshed: boolean;
}

export interface ReleaseMetricsData {
  releaseId: string;
  teamId: string;
  teamName: string;
  releaseMetrics: ReleaseMetrics;
  confidenceMetrics: ConfidenceMetrics;
  lastMetricsUpdate?: string;
  updatedAt?: string;
}

export interface WeightingsUpdateRequest {
  weightings: {
    codeQuality: number;
    testCoverage: number;
    testResults: number;
  };
}

export const metricsService = {
  /**
   * Get metrics for a release
   */
  async getMetrics(releaseId: string, refresh = false): Promise<MetricsData> {
    const url = new URL(`${API_BASE_URL}/metrics/${releaseId}`);
    if (refresh) {
      url.searchParams.set('refresh', 'true');
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get metrics');
    }

    return response.json();
  },

  /**
   * Force refresh metrics for a release
   */
  async refreshMetrics(releaseId: string): Promise<{
    message: string;
    releaseId: string;
    releaseMetrics: ReleaseMetrics;
    confidenceMetrics: ConfidenceMetrics;
    lastUpdated: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/metrics/${releaseId}/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to refresh metrics');
    }

    return response.json();
  },

  /**
   * Update confidence score weightings
   */
  async updateWeightings(releaseId: string, weightings: WeightingsUpdateRequest['weightings']): Promise<{
    message: string;
    confidenceMetrics: ConfidenceMetrics;
  }> {
    const response = await fetch(`${API_BASE_URL}/metrics/${releaseId}/weightings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ weightings }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update weightings');
    }

    return response.json();
  },
};

// Function to fetch and store all metrics for a release and calculate confidence score
export const fetchAndStoreAllMetrics = async (team: CosmosTeam, releaseId: string): Promise<{ 
  confidenceScore: number; 
  metrics: { codeQuality?: any; codeCoverage?: any; testResults?: any }; 
}> => {
  console.log(`🔄 Fetching and storing all metrics for release: ${releaseId}, team: ${team.teamName}`);
  
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002';
  const metrics: { codeQuality?: any; codeCoverage?: any; testResults?: any } = {};
  const scores: { codeQuality?: number; codeCoverage?: number; testResults?: number } = {};
  
  try {
    // Fetch all three metric types in parallel
    const [codeQualityResult, codeCoverageResult, testResultsResult] = await Promise.allSettled([
      fetchCodeQualityMetrics(team),
      fetchCodeCoverageMetrics(team), 
      fetchTestResultsMetrics(team)
    ]);

    // Store Code Quality metrics if successful
    if (codeQualityResult.status === 'fulfilled' && codeQualityResult.value) {
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/metrics/${releaseId}/code-quality`,
          {
            teamId: team.id,
            metrics: codeQualityResult.value
          }
        );
        if (response.data) {
          metrics.codeQuality = codeQualityResult.value;
          scores.codeQuality = response.data.score || calculateCodeQualityScore(codeQualityResult.value);
        }
      } catch (error) {
        console.warn('Failed to store code quality metrics:', error);
      }
    }

    // Store Code Coverage metrics if successful
    if (codeCoverageResult.status === 'fulfilled' && codeCoverageResult.value) {
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/metrics/${releaseId}/test-coverage`,
          {
            teamId: team.id,
            metrics: codeCoverageResult.value
          }
        );
        if (response.data) {
          metrics.codeCoverage = codeCoverageResult.value;
          scores.codeCoverage = response.data.score || calculateCoverageScore(codeCoverageResult.value);
        }
      } catch (error) {
        console.warn('Failed to store code coverage metrics:', error);
      }
    }

    // Store Test Results metrics if successful
    if (testResultsResult.status === 'fulfilled' && testResultsResult.value) {
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/metrics/${releaseId}/test-results`,
          {
            teamId: team.id,
            metrics: testResultsResult.value
          }
        );
        if (response.data) {
          metrics.testResults = testResultsResult.value;
          scores.testResults = response.data.score || calculateTestResultsScore(testResultsResult.value);
        }
      } catch (error) {
        console.warn('Failed to store test results metrics:', error);
      }
    }

    // Calculate overall confidence score using weighted average
    let overallScore = 0;
    let totalWeight = 0;
    
    if (scores.codeQuality !== undefined) {
      overallScore += scores.codeQuality * 0.4; // 40% weight
      totalWeight += 40;
    }
    if (scores.codeCoverage !== undefined) {
      overallScore += scores.codeCoverage * 0.35; // 35% weight
      totalWeight += 35;
    }
    if (scores.testResults !== undefined) {
      overallScore += scores.testResults * 0.25; // 25% weight
      totalWeight += 25;
    }

    const confidenceScore = totalWeight > 0 ? Math.round(overallScore) : 0;
    
    console.log(`✅ Metrics processed for release ${releaseId}:`, {
      scores,
      confidenceScore,
      totalWeight
    });

    return {
      confidenceScore,
      metrics
    };
  } catch (error) {
    console.error('❌ Error fetching and storing metrics:', error);
    return {
      confidenceScore: 0,
      metrics: {}
    };
  }
};

// Helper functions to calculate individual metric scores
const calculateCodeQualityScore = (data: any): number => {
  if (!data) return 0;
  
  // Simple score calculation based on ratings and coverage
  let score = 100;
  
  // Deduct points for poor reliability rating
  const reliabilityRating = parseFloat(data.reliability_rating || '1.0');
  if (reliabilityRating > 1.0) score -= (reliabilityRating - 1.0) * 15;
  
  // Deduct points for poor security rating
  const securityRating = parseFloat(data.security_rating || '1.0');
  if (securityRating > 1.0) score -= (securityRating - 1.0) * 15;
  
  // Deduct points for poor maintainability rating
  const maintainabilityRating = parseFloat(data.sqale_rating || '1.0');
  if (maintainabilityRating > 1.0) score -= (maintainabilityRating - 1.0) * 10;
  
  // Factor in bugs and vulnerabilities per LOC
  const ncloc = parseInt(data.ncloc || '1');
  const bugs = parseInt(data.bugs || '0');
  const vulnerabilities = parseInt(data.vulnerabilities || '0');
  
  if (ncloc > 0) {
    const bugDensity = (bugs / ncloc) * 1000; // bugs per 1000 lines
    const vulnDensity = (vulnerabilities / ncloc) * 1000; // vulnerabilities per 1000 lines
    
    score -= Math.min(bugDensity * 5, 20); // max 20 points deduction for bugs
    score -= Math.min(vulnDensity * 10, 30); // max 30 points deduction for vulnerabilities
  }
  
  return Math.max(0, Math.round(score));
};

const calculateCoverageScore = (data: any): number => {
  if (!data || !data.coverage) return 0;
  
  const coverage = parseFloat(data.coverage);
  
  // Simple coverage-based score
  if (coverage >= 90) return 100;
  if (coverage >= 80) return 90;
  if (coverage >= 70) return 75;
  if (coverage >= 60) return 60;
  if (coverage >= 50) return 45;
  if (coverage >= 40) return 30;
  if (coverage >= 30) return 20;
  return Math.max(0, Math.round(coverage / 2));
};

const calculateTestResultsScore = (data: any): number => {
  if (!data || !data.tests) return 0;
  
  const total = parseInt(data.tests || '0');
  const failures = parseInt(data.test_failures || '0');
  const errors = parseInt(data.test_errors || '0');
  
  if (total === 0) return 0;
  
  const successRate = ((total - failures - errors) / total) * 100;
  
  // Test results score based on success rate
  if (successRate >= 99) return 100;
  if (successRate >= 95) return 90;
  if (successRate >= 90) return 80;
  if (successRate >= 80) return 65;
  if (successRate >= 70) return 50;
  return Math.max(0, Math.round(successRate / 2));
}; 