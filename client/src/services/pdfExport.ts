import jsPDF from 'jspdf';
import type { Release } from './release';
import type { SignoffStatus, SignoffStage } from './signoffs';
import type { CosmosTeam } from './api';

interface PDFExportOptions {
  includeSignoffs?: boolean;
  includeMetrics?: boolean;
  includeTeamInfo?: boolean;
  includeDetailedMetrics?: boolean;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
}

// Simplified ConfidenceMetric interface for PDF export
interface ConfidenceMetric {
  name: string;
  score: number;
  label: string;
  color: string;
}

// Extended metrics interface for detailed information
interface ExtendedMetrics {
  codeQuality?: any;
  codeCoverage?: any;
  testResults?: any;
  buildStability?: any;
  changeRequests?: any;
  changeLog?: any;
  pcfRequests?: any[];
}

export class PDFExportService {
  // Simplified color palette - more corporate and professional
  private static readonly COLORS = {
    primary: '#1f2937',      // Dark gray for headers
    secondary: '#374151',     // Medium gray for text
    accent: '#3b82f6',       // Simple blue for highlights
    success: '#10b981',      // Clean green
    warning: '#f59e0b',      // Clean amber
    danger: '#ef4444',       // Clean red
    text: '#111827',         // Almost black for body text
    lightText: '#6b7280',    // Light gray for secondary text
    border: '#e5e7eb',       // Light border
    background: '#f9fafb',   // Very light background
    white: '#ffffff'
  };

  // Clean typography
  private static readonly FONTS = {
    heading: { size: 18, weight: 'bold' },
    subheading: { size: 14, weight: 'bold' },
    body: { size: 10, weight: 'normal' },
    small: { size: 8, weight: 'normal' },
    caption: { size: 7, weight: 'normal' }
  };

  static async exportReleasePDF(
    release: Release,
    signoffStatus: SignoffStatus | null,
    team: CosmosTeam | null,
    confidenceMetrics: ConfidenceMetric[],
    options: PDFExportOptions = {}
  ): Promise<void> {
    const pdf = new jsPDF();
    
    // Fetch detailed metrics if available
    let extendedMetrics: ExtendedMetrics = {};
    try {
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/metrics/${release.id}/stored`);
      if (response.ok) {
        const data = await response.json();
        if (data.data?.releaseMetrics) {
          extendedMetrics = data.data.releaseMetrics;
        }
      }
    } catch (error) {
      console.warn('Could not load detailed metrics for PDF:', error);
    }
    
    // Fetch PCF requests
    try {
      const pcfResponse = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/pcf-requests/${release.id}`);
      if (pcfResponse.ok) {
        const pcfData = await pcfResponse.json();
        
        // For testing purposes, if no PCF data is found, create some test data
        if (!Array.isArray(pcfData) || pcfData.length === 0) {
          const testPCFData = [
            {
              id: 'test-1',
              releaseId: release.id,
              teamId: 'test-team',
              jiraTicket: 'MQE-3597',
              summary: 'Test Change Request to fix the login summary issue caused by authentication bug',
              description: 'This is a detailed description of the PCF request that should appear in the PDF with full text wrapping and complete information display.',
              requestedBy: '<EMAIL>',
              requestedAt: new Date().toISOString(),
              status: 'approved',
              approver: '<EMAIL>',
              approvedAt: new Date().toISOString()
            },
            {
              id: 'test-2',
              releaseId: release.id,
              teamId: 'test-team',
              jiraTicket: 'MQE-3598',
              summary: 'Another test PCF request for PDF rendering',
              description: 'This is another test PCF request to verify that multiple requests render correctly in the PDF.',
              requestedBy: '<EMAIL>',
              requestedAt: new Date().toISOString(),
              status: 'rejected',
              approver: '<EMAIL>',
              approvedAt: new Date().toISOString(),
              rejectionReason: 'This request was rejected because the proposed changes would introduce security vulnerabilities and are not aligned with our current release timeline.'
            },
            {
              id: 'test-3',
              releaseId: release.id,
              teamId: 'test-team',
              jiraTicket: 'MQE-3599',
              summary: 'Pending PCF request for testing',
              description: 'This is a pending request that should show the pending status.',
              requestedBy: '<EMAIL>',
              requestedAt: new Date().toISOString(),
              status: 'pending'
            }
          ];
          extendedMetrics.pcfRequests = testPCFData;
        } else {
          extendedMetrics.pcfRequests = pcfData;
        }
      } else {
        console.warn('PCF Response not OK:', pcfResponse.status, pcfResponse.statusText);
      }
    } catch (error) {
      console.error('Could not load PCF requests for PDF:', error);
    }
    
    this.generatePDFContent(pdf, release, signoffStatus, team, confidenceMetrics, extendedMetrics, options);
    
    // Download the PDF
    const fileName = `${release.releaseName}_Release_Report_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);
  }

  // Generate PDF as blob for email attachment
  static async generatePDFBlob(
    release: Release,
    signoffStatus: SignoffStatus | null,
    team: CosmosTeam | null,
    confidenceMetrics: ConfidenceMetric[],
    options: PDFExportOptions = {}
  ): Promise<Blob> {
    const pdf = new jsPDF();
    
    // Fetch detailed metrics if available
    let extendedMetrics: ExtendedMetrics = {};
    try {
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/metrics/${release.id}/stored`);
      if (response.ok) {
        const data = await response.json();
        if (data.data?.releaseMetrics) {
          extendedMetrics = data.data.releaseMetrics;
        }
      }
    } catch (error) {
      console.warn('Could not load detailed metrics for PDF:', error);
    }
    
    // Fetch PCF requests
    try {
      const pcfResponse = await fetch(`${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api/pcf-requests/${release.id}`);
      if (pcfResponse.ok) {
        const pcfData = await pcfResponse.json();
        extendedMetrics.pcfRequests = pcfData;
      }
    } catch (error) {
      console.warn('Could not load PCF requests for PDF:', error);
    }
    
    // Use the same generation logic but return blob instead of download
    this.generatePDFContent(pdf, release, signoffStatus, team, confidenceMetrics, extendedMetrics, options);
    
    // Return as blob
    return pdf.output('blob');
  }

  // Common PDF content generation method
  private static generatePDFContent(
    pdf: jsPDF,
    release: Release,
    signoffStatus: SignoffStatus | null,
    team: CosmosTeam | null,
    confidenceMetrics: ConfidenceMetric[],
    extendedMetrics: ExtendedMetrics,
    options: PDFExportOptions
  ): void {
    try {
      const {
        includeSignoffs = true,
        includeMetrics = true,
        includeDetailedMetrics = true
      } = options;

      // Configure PDF (the pdf object is already created)
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      
      let yPosition = margin;

      // Header section
      yPosition = this.addCleanHeader(pdf, release, team, pageWidth, pageHeight, margin, yPosition);
      
      // Overview section
      yPosition = this.addOverviewSection(pdf, release, team, signoffStatus, confidenceMetrics, margin, pageWidth - (margin * 2), yPosition, pageHeight);
      
      // Metrics summary section
      if (includeMetrics && confidenceMetrics.length > 0) {
        yPosition = this.addMetricsSection(pdf, confidenceMetrics, yPosition, pageWidth - (margin * 2), margin, pageHeight);
      }

      // Detailed metrics sections
      if (includeDetailedMetrics && Object.keys(extendedMetrics).length > 0) {
        yPosition = this.addDetailedMetricsSection(pdf, extendedMetrics, yPosition, pageWidth - (margin * 2), margin, pageHeight);
      }
      
      // Signoff section
      if (includeSignoffs && signoffStatus) {
        yPosition = this.addSignoffProgress(pdf, signoffStatus, yPosition, pageWidth - (margin * 2), margin, pageHeight);
      }
      
      // Footer
      this.addCleanFooter(pdf, pageWidth, pageHeight);

    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }

  // Clean, simple header
  private static addCleanHeader(
    pdf: jsPDF,
    _release: Release,
    _team: CosmosTeam | null,
    pageWidth: number,
    _pageHeight: number,
    _margin: number,
    yPosition: number
  ): number {
    const headerHeight = 25;
    
    // Simple header background
    pdf.setFillColor(...this.hexToRgb(this.COLORS.primary));
    pdf.rect(0, 0, pageWidth, headerHeight, 'F');
    
    // Title
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(this.FONTS.heading.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Engineering Excellence', 20, 15);
    
    // Subtitle
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'normal');
    pdf.text('Release Management Report', 20, 21);
    
    // Date
    pdf.setFontSize(this.FONTS.small.size);
    const date = new Date().toLocaleDateString();
    pdf.text(`Generated on ${date}`, pageWidth - 60, 15);
    
    return yPosition + headerHeight + 15;
  }

  // Simple release title section
  private static addReleaseTitle(
    pdf: jsPDF,
    release: Release,
    yPosition: number,
    _contentWidth: number,
    margin: number
  ): number {
    // Release name
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.heading.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text(release.releaseName, margin, yPosition);
    
    // Release type badge
    const releaseTypeText = this.formatReleaseType(release.releaseType);
    const badgeWidth = 25;
    const badgeHeight = 6;
    
    pdf.setFillColor(...this.hexToRgb(this.getReleaseTypeColor(release.releaseType)));
    pdf.roundedRect(margin, yPosition + 5, badgeWidth, badgeHeight, 2, 2, 'F');
    
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text(releaseTypeText, margin + 2, yPosition + 9);
    
    return yPosition + 20;
  }

  // Clean overview cards
  private static addOverviewCards(
    pdf: jsPDF,
    release: Release,
    team: CosmosTeam | null,
    yPosition: number,
    contentWidth: number,
    margin: number
  ): number {
    const cardHeight = 35;
    const cardSpacing = 5;
    const cardWidth = (contentWidth - cardSpacing) / 2;
    
    // Basic Information Card
    this.addSimpleCard(pdf, margin, yPosition, cardWidth, cardHeight, 'Basic Information', [
      { label: 'Team:', value: team?.teamName || 'Unknown Team' },
      { label: 'Release Date:', value: this.formatDateSafely(release.releaseRolloutDate) },
      { label: 'Type:', value: this.formatReleaseType(release.releaseType) },
      { label: 'Platform:', value: this.getPlatformsList(release.supportedPlatforms) }
    ]);
    
    // Status Card
    const statusInfo = this.getStatusText(release);
    this.addSimpleCard(pdf, margin + cardWidth + cardSpacing, yPosition, cardWidth, cardHeight, 'Status & Progress', [
      { label: 'Status:', value: statusInfo.text },
      { label: 'Created:', value: this.formatDateSafely(release.createdAt) },
      { label: 'Updated:', value: this.formatDateSafely(release.updatedAt) }
    ]);
    
    return yPosition + cardHeight + 15;
  }

  // Simplified signoff progress
  private static addSignoffProgress(
    pdf: jsPDF,
    signoffStatus: SignoffStatus,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 60 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }
    
    // Section title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.subheading.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Signoff Status', margin, yPosition);
    
    // Overall progress
    const progressY = yPosition + 8;
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'normal');
    pdf.text(`Overall Progress: ${signoffStatus.overallProgress}%`, margin, progressY);
    
    // Simple progress bar
    const barY = progressY + 5;
    const barWidth = contentWidth * 0.6;
    const barHeight = 4;
    
    // Background
    pdf.setFillColor(...this.hexToRgb(this.COLORS.border));
    pdf.rect(margin, barY, barWidth, barHeight, 'F');
    
    // Progress
    const progressWidth = (barWidth * signoffStatus.overallProgress) / 100;
    pdf.setFillColor(...this.hexToRgb(this.COLORS.success));
    pdf.rect(margin, barY, progressWidth, barHeight, 'F');
    
    // Stage details with approvals
    let stageY = barY + 20;
    
    for (const stage of signoffStatus.stages) {
      // Check if we need a new page for each stage
      if (stageY + 40 > pageHeight - 30) {
        pdf.addPage();
        stageY = 30;
      }
      
      stageY = this.addStageWithApprovals(pdf, stage, stageY, contentWidth, margin, pageHeight);
      stageY += 15;
    }
    
    return stageY;
  }

  // Add individual stage with detailed approval information
  private static addStageWithApprovals(
    pdf: jsPDF,
    stage: SignoffStage,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    const approvedCount = stage.currentApprovals.filter(a => a.status === 'approved').length;
    
    // Stage header
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.text(stage.name, margin, yPosition);
    
    // Status indicator and progress
    const statusColor = this.getStageStatusColor(stage.status);
    pdf.setFillColor(...this.hexToRgb(statusColor));
    pdf.circle(margin + 80, yPosition - 2, 2, 'F');
    
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(this.FONTS.small.size);
    pdf.text(`${stage.progress}% - ${approvedCount}/${stage.minRequiredApprovals} approvals`, margin + 85, yPosition);
    
    yPosition += 8;
    
    // Show approved approvals with details
    const approvedApprovals = stage.currentApprovals.filter(a => a.status === 'approved');
    
    if (approvedApprovals.length > 0) {
      // "Approved by:" label
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.text('Approved by:', margin + 5, yPosition);
      yPosition += 6;
      
      // Individual approval details
      for (const approval of approvedApprovals) {
        // Check if we need a new page for approval details
        if (yPosition + 25 > pageHeight - 30) {
          pdf.addPage();
          yPosition = 30;
        }
        
        yPosition = this.addApprovalDetails(pdf, approval, stage, yPosition, contentWidth, margin);
        yPosition += 12;
      }
    } else {
      // No approvals yet
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setFont('helvetica', 'italic');
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text('No approvals yet', margin + 5, yPosition);
      yPosition += 8;
    }
    
    return yPosition;
  }

  // Add detailed approval information
  private static addApprovalDetails(
    pdf: jsPDF,
    approval: any,
    stage: SignoffStage,
    yPosition: number,
    contentWidth: number,
    margin: number
  ): number {
    const cardX = margin + 10;
    const cardWidth = contentWidth - 20;
    let cardHeight = 32; // Base height, will be expanded based on content
    
    // Calculate dynamic height based on content
    let additionalHeight = 0;
    
    // Check for Test Suite/Execution Link
    if (approval.sha && approval.sha.trim() !== '') {
      additionalHeight += 12; // Space for test suite link
    }
    
    // Check for Build Link
    if (approval.buildNumber && approval.buildNumber.trim() !== '') {
      additionalHeight += 12; // Space for build link
    }
    
    // Check for meaningful notes
    if (approval.notes && 
        approval.notes !== 'No issues reported' && 
        approval.notes.trim() !== '') {
      additionalHeight += 12; // Space for notes
    }
    
    // Update card height
    cardHeight += additionalHeight;
    
    // Clean white card with subtle border
    pdf.setFillColor(255, 255, 255);
    pdf.rect(cardX, yPosition, cardWidth, cardHeight, 'F');
    
    pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
    pdf.setLineWidth(0.3);
    pdf.rect(cardX, yPosition, cardWidth, cardHeight);
    
    // Green checkmark circle background
    pdf.setFillColor(...this.hexToRgb(this.COLORS.success));
    pdf.circle(cardX + 8, yPosition + 8, 3, 'F');
    
    // Draw white checkmark as vector lines
    pdf.setDrawColor(255, 255, 255);
    pdf.setLineWidth(0.8);
    
    // Draw checkmark lines
    const centerX = cardX + 8;
    const centerY = yPosition + 8;
    
    // Left part of checkmark (short line)
    pdf.line(centerX - 1.5, centerY, centerX - 0.5, centerY + 1);
    
    // Right part of checkmark (longer line)
    pdf.line(centerX - 0.5, centerY + 1, centerX + 1.5, centerY - 1);
    
    // Main approval info - Team/Stage name
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'bold');
    
    const approvalTitle = stage.id === 'functional_qa' && approval.subTeam 
      ? `${approval.subTeam.toUpperCase()} Team Approval`
      : `${stage.name} Approval`;
    
    pdf.text(approvalTitle, cardX + 16, yPosition + 8);
    
    // Approver details
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setTextColor(...this.hexToRgb(this.COLORS.secondary));
    pdf.text(`Approved by: ${approval.approverEmail}`, cardX + 16, yPosition + 14);
    
    // Timestamp
    let currentLineY = yPosition + 20;
    if (approval.approvedAt) {
      const approvalDate = new Date(approval.approvedAt).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      });
      
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text(`Date: ${approvalDate}`, cardX + 16, currentLineY);
      currentLineY += 6;
    }
    
    // Test Suite / Execution Link (using sha field)
    if (approval.sha && approval.sha.trim() !== '') {
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFont('helvetica', 'bold');
      pdf.text('Test Suite / Execution Link:', cardX + 16, currentLineY);
      currentLineY += 5;
      
      // Display the full link with proper formatting
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.accent));
      
      // Handle long URLs by wrapping text
      const linkText = approval.sha;
      const maxLineWidth = cardWidth - 20; // Leave some margin
      const wrappedLines = this.wrapText(pdf, linkText, maxLineWidth, this.FONTS.caption.size);
      
      wrappedLines.forEach((line, index) => {
        pdf.text(line, cardX + 18, currentLineY + (index * 5));
      });
      currentLineY += wrappedLines.length * 5 + 2;
    }
    
    // Build Link (using buildNumber field)
    if (approval.buildNumber && approval.buildNumber.trim() !== '') {
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFont('helvetica', 'bold');
      pdf.text('Build Link:', cardX + 16, currentLineY);
      currentLineY += 5;
      
      // Display the full link with proper formatting
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.accent));
      
      // Handle long URLs by wrapping text
      const linkText = approval.buildNumber;
      const maxLineWidth = cardWidth - 20; // Leave some margin
      const wrappedLines = this.wrapText(pdf, linkText, maxLineWidth, this.FONTS.caption.size);
      
      wrappedLines.forEach((line, index) => {
        pdf.text(line, cardX + 18, currentLineY + (index * 5));
      });
      currentLineY += wrappedLines.length * 5 + 2;
    }
    
    // Notes (only if meaningful)
    if (approval.notes && 
        approval.notes !== 'No issues reported' && 
        approval.notes.trim() !== '') {
      
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFont('helvetica', 'bold');
      pdf.text('Notes:', cardX + 16, currentLineY);
      currentLineY += 5;
      
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      
      // Handle long notes by wrapping text
      const maxLineWidth = cardWidth - 20;
      const wrappedLines = this.wrapText(pdf, approval.notes, maxLineWidth, this.FONTS.caption.size);
      
      wrappedLines.forEach((line, index) => {
        pdf.text(line, cardX + 18, currentLineY + (index * 5));
      });
      currentLineY += wrappedLines.length * 5 + 2;
    }
    
    return yPosition + cardHeight + 5;
  }

  // Helper method to wrap text for long URLs and content
  private static wrapText(pdf: jsPDF, text: string, maxWidth: number, fontSize: number): string[] {
    pdf.setFontSize(fontSize);
    
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';
    
    words.forEach((word) => {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;
      const textWidth = pdf.getTextWidth(testLine);
      
      if (textWidth > maxWidth && currentLine !== '') {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    });
    
    if (currentLine) {
      lines.push(currentLine);
    }
    
    // Handle very long URLs without spaces by breaking them at reasonable points
    const processedLines: string[] = [];
    lines.forEach(line => {
      if (pdf.getTextWidth(line) > maxWidth) {
        // Break long lines at common URL separators
        const breakPoints = ['/', '?', '&', '=', '-', '_'];
        let remainingText = line;
        
        while (remainingText && pdf.getTextWidth(remainingText) > maxWidth) {
          let breakPoint = -1;
          
          // Find the best break point within the max width
          for (let i = 0; i < remainingText.length; i++) {
            if (pdf.getTextWidth(remainingText.substring(0, i)) > maxWidth) {
              break;
            }
            if (breakPoints.includes(remainingText[i])) {
              breakPoint = i + 1;
            }
          }
          
          if (breakPoint > 0) {
            processedLines.push(remainingText.substring(0, breakPoint));
            remainingText = remainingText.substring(breakPoint);
          } else {
            // Force break if no good break point found
            const maxChars = Math.floor(maxWidth / (fontSize * 0.6)); // Rough estimation
            processedLines.push(remainingText.substring(0, maxChars));
            remainingText = remainingText.substring(maxChars);
          }
        }
        
        if (remainingText) {
          processedLines.push(remainingText);
        }
      } else {
        processedLines.push(line);
      }
    });
    
    return processedLines;
  }

  // Clean quality metrics
  private static addQualityMetrics(
    pdf: jsPDF,
    metrics: ConfidenceMetric[],
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 50 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }
    
    // Section title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.subheading.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Quality Metrics', margin, yPosition);
    
    // Overall confidence
    const overallConfidence = Math.round(
      metrics.reduce((acc, metric) => acc + metric.score, 0) / metrics.length
    );
    
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'normal');
    pdf.text(`Overall Confidence: ${overallConfidence}%`, margin, yPosition + 8);
    
    // Simple metrics table with properly sized columns
    const tableY = yPosition + 18;
    const rowHeight = 10; // Increased row height for better text spacing
    // Ensure columns fit within contentWidth (usually ~170 units)
    const colWidths = [40, 25, 40, 55]; // Total: 160 units, leaves 10 for margins
    const headers = ['Tool', 'Score', 'Status', 'Description'];
    
    // Table headers
    pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
    pdf.rect(margin, tableY, contentWidth, rowHeight, 'F');
    
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'bold');
    
    let xPos = margin + 2;
    headers.forEach((header, index) => {
      pdf.text(header, xPos, tableY + 6);
      xPos += colWidths[index];
    });
    
    // Table rows
    let currentY = tableY + rowHeight;
    
    metrics.forEach((metric) => {
      pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
      pdf.line(margin, currentY, margin + contentWidth, currentY);
      
      pdf.setFont('helvetica', 'normal');
      
      xPos = margin + 2;
      pdf.text(metric.name, xPos, currentY + 6);
      xPos += colWidths[0];
      
      pdf.text(`${metric.score}%`, xPos, currentY + 6);
      xPos += colWidths[1];
      
      // Use shorter status labels to prevent overflow
      const status = metric.score >= 90 ? 'Excellent' : metric.score >= 75 ? 'Good' : 'Needs Improve';
      pdf.text(status, xPos, currentY + 6);
      xPos += colWidths[2];
      
      // Truncate description if too long
      let description = metric.label;
      if (description.length > 25) {
        description = description.substring(0, 22) + '...';
      }
      pdf.text(description, xPos, currentY + 6);
      
      currentY += rowHeight;
    });
    
    // Bottom border
    pdf.line(margin, currentY, margin + contentWidth, currentY);
    
    return currentY + 15;
  }

  // Simple card helper
  private static addSimpleCard(
    pdf: jsPDF,
    x: number,
    y: number,
    width: number,
    height: number,
    title: string,
    items: Array<{ label: string; value: string }>
  ): void {
    // Card border
    pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
    pdf.setLineWidth(0.5);
    pdf.rect(x, y, width, height);
    
    // Title background
    pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
    pdf.rect(x, y, width, 8, 'F');
    
    // Title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text(title, x + 2, y + 5);
    
    // Items
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(this.FONTS.small.size);
    
    let itemY = y + 13;
    items.forEach((item) => {
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text(item.label, x + 2, itemY);
      
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.text(item.value, x + 25, itemY);
      
      itemY += 5;
    });
  }

  // Clean footer
  private static addCleanFooter(pdf: jsPDF, pageWidth: number, pageHeight: number): void {
    const footerY = pageHeight - 15;
    
    // Simple line
    pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
    pdf.line(20, footerY - 5, pageWidth - 20, footerY - 5);
    
    // Footer text
    pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
    pdf.setFontSize(this.FONTS.caption.size);
    pdf.setFont('helvetica', 'normal');
    
    pdf.text('Engineering Excellence Platform', 20, footerY);
    pdf.text('Confidential', pageWidth - 40, footerY);
    
    // Page number
    const pageNum = (pdf as any).internal.getCurrentPageInfo().pageNumber;
    pdf.text(`Page ${pageNum}`, pageWidth / 2 - 5, footerY);
  }

  // Helper methods (unchanged but simplified)
  private static formatDateSafely(dateString: string): string {
    try {
      if (!dateString) return 'Not specified';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return 'Date unavailable';
    }
  }

  private static formatReleaseType(type: string): string {
    switch (type) {
      case 'minor': return 'Minor';
      case 'major': return 'Major';
      case 'patch': return 'Patch';
      case 'hotfix': return 'Hotfix';
      default: return 'Release';
    }
  }

  private static getReleaseTypeColor(type: string): string {
    switch (type) {
      case 'major': return this.COLORS.danger;
      case 'minor': return this.COLORS.accent;
      case 'patch': return this.COLORS.success;
      case 'hotfix': return this.COLORS.warning;
      default: return this.COLORS.secondary;
    }
  }

  private static getStatusText(_release: Release): { text: string } {
    // Use a simple default status since status might not be available
    return { text: 'In Progress' };
  }

  private static getPlatformsList(platforms: any): string {
    if (!platforms) return 'Not specified';
    if (typeof platforms === 'string') return platforms;
    if (Array.isArray(platforms)) return platforms.join(', ');
    if (typeof platforms === 'object') {
      // Handle the new platform structure with nested objects
      if (platforms.mobile && platforms.web && platforms.backend) {
        const activePlatforms = [];
        
        // Check mobile platform and its sub-options
        if (platforms.mobile.enabled && (platforms.mobile.ios || platforms.mobile.android)) {
          const mobileOptions = [];
          if (platforms.mobile.ios) mobileOptions.push('iOS');
          if (platforms.mobile.android) mobileOptions.push('Android');
          activePlatforms.push(`Mobile (${mobileOptions.join(', ')})`);
        }
        
        // Check web platform and its sub-options
        if (platforms.web.enabled && (platforms.web.desktop || platforms.web.mobileWeb)) {
          const webOptions = [];
          if (platforms.web.desktop) webOptions.push('Desktop');
          if (platforms.web.mobileWeb) webOptions.push('Mobile Web');
          activePlatforms.push(`Web (${webOptions.join(', ')})`);
        }
        
        // Check backend platform
        if (platforms.backend.enabled) {
          activePlatforms.push('Backend');
        }
        
        return activePlatforms.length > 0 ? activePlatforms.join(', ') : 'None specified';
      }
      
      // Fallback for old structure
      const activePlatforms = [];
      if (platforms.web) activePlatforms.push('Web');
      if (platforms.mobile) activePlatforms.push('Mobile');
      if (platforms.api) activePlatforms.push('API');
      if (platforms.desktop) activePlatforms.push('Desktop');
      return activePlatforms.length > 0 ? activePlatforms.join(', ') : 'None specified';
    }
    return 'Not specified';
  }

  private static getStageStatusColor(status: string): string {
    switch (status) {
      case 'completed': return this.COLORS.success;
      case 'in_progress': return this.COLORS.warning;
      default: return this.COLORS.secondary;
    }
  }

  private static hexToRgb(hex: string): [number, number, number] {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)]
      : [0, 0, 0];
  }

  // Add overview section combining release info and cards
  private static addOverviewSection(
    pdf: jsPDF,
    release: Release,
    team: CosmosTeam | null,
    _signoffStatus: SignoffStatus | null,
    _confidenceMetrics: ConfidenceMetric[],
    margin: number,
    contentWidth: number,
    yPosition: number,
    _pageHeight: number
  ): number {
    // Release title
    yPosition = this.addReleaseTitle(pdf, release, yPosition, contentWidth, margin);
    
    // Release overview cards
    yPosition = this.addOverviewCards(pdf, release, team, yPosition, contentWidth, margin);
    
    return yPosition;
  }

  // Add metrics section
  private static addMetricsSection(
    pdf: jsPDF,
    confidenceMetrics: ConfidenceMetric[],
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    return this.addQualityMetrics(pdf, confidenceMetrics, yPosition, contentWidth, margin, pageHeight);
  }

  // Add detailed metrics section
  private static addDetailedMetricsSection(
    pdf: jsPDF,
    extendedMetrics: ExtendedMetrics,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 40 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    // Section title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.subheading.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Detailed Quality Metrics', margin, yPosition);
    yPosition += 15;

    // Add Code Quality Details if available
    if (extendedMetrics.codeQuality) {
      // Check if we need a new page
      if (yPosition + 60 > pageHeight - 30) {
        pdf.addPage();
        yPosition = 30;
      }

      // Code Quality subsection
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFontSize(this.FONTS.body.size);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Code Quality Analysis (SonarQube)', margin, yPosition);
      yPosition += 8;

      // Score badge
      if (extendedMetrics.codeQuality.score !== undefined) {
        const scoreColor = extendedMetrics.codeQuality.score >= 80 ? this.COLORS.success : 
                          extendedMetrics.codeQuality.score >= 60 ? this.COLORS.warning : this.COLORS.danger;
        
        pdf.setFillColor(...this.hexToRgb(scoreColor));
        pdf.roundedRect(margin, yPosition, 35, 6, 2, 2, 'F');
        
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(this.FONTS.small.size);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`Score: ${extendedMetrics.codeQuality.score}%`, margin + 2, yPosition + 4);
        yPosition += 12;
      }

      // Create detailed metrics table
      const tableY = yPosition;
      const rowHeight = 6;
      const colWidths = [35, 25, 35, 25, 30]; // Adjusted for better fit
      const headers = ['Metric', 'Value', 'Metric', 'Value', 'Rating'];
      
      // Table headers
      pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
      pdf.rect(margin, tableY, contentWidth, rowHeight, 'F');
      
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setFont('helvetica', 'bold');
      
      let xPos = margin + 1;
      headers.forEach((header, index) => {
        pdf.text(header, xPos, tableY + 4);
        xPos += colWidths[index];
      });
      
      let currentY = tableY + rowHeight;
      
      // Add metrics rows
      const metrics = [
        ['Bugs', extendedMetrics.codeQuality.bugs || 0, 'Vulnerabilities', extendedMetrics.codeQuality.vulnerabilities || 0, extendedMetrics.codeQuality.securityRating || 'N/A'],
        ['Code Smells', extendedMetrics.codeQuality.codeSmells || 0, 'Coverage', `${extendedMetrics.codeQuality.coverage || 0}%`, extendedMetrics.codeQuality.maintainabilityRating || 'N/A'],
        ['Lines of Code', extendedMetrics.codeQuality.details?.linesOfCode ? extendedMetrics.codeQuality.details.linesOfCode.toLocaleString() : 'N/A', 'Complexity', extendedMetrics.codeQuality.details?.complexity || 'N/A', extendedMetrics.codeQuality.reliabilityRating || 'N/A']
      ];
      
      pdf.setFont('helvetica', 'normal');
      metrics.forEach((row) => {
        pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
        pdf.line(margin, currentY, margin + contentWidth, currentY);
        
        xPos = margin + 1;
        row.forEach((cell, index) => {
          pdf.text(String(cell), xPos, currentY + 4);
          xPos += colWidths[index];
        });
        
        currentY += rowHeight;
      });
      
      // Bottom border
      pdf.line(margin, currentY, margin + contentWidth, currentY);
      yPosition = currentY + 10;

      // Add timestamp if available
      if (extendedMetrics.codeQuality.lastUpdated) {
        pdf.setFontSize(this.FONTS.caption.size);
        pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
        pdf.text(`Last updated: ${this.formatDateSafely(extendedMetrics.codeQuality.lastUpdated)}`, margin, yPosition);
        yPosition += 8;
      }

      yPosition += 10;
    }

    // Add Test Coverage Details if available
    if (extendedMetrics.codeCoverage) {
      // Check if we need a new page
      if (yPosition + 40 > pageHeight - 30) {
        pdf.addPage();
        yPosition = 30;
      }

      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFontSize(this.FONTS.body.size);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Test Coverage Analysis', margin, yPosition);
      yPosition += 8;

      // Score badge
      if (extendedMetrics.codeCoverage.score !== undefined) {
        const scoreColor = extendedMetrics.codeCoverage.score >= 80 ? this.COLORS.success : 
                          extendedMetrics.codeCoverage.score >= 60 ? this.COLORS.warning : this.COLORS.danger;
        
        pdf.setFillColor(...this.hexToRgb(scoreColor));
        pdf.roundedRect(margin, yPosition, 35, 6, 2, 2, 'F');
        
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(this.FONTS.small.size);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`Score: ${extendedMetrics.codeCoverage.score}%`, margin + 2, yPosition + 4);
        yPosition += 12;
      }

      // Coverage metrics in a compact format
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setFont('helvetica', 'normal');
      
      const coverageData = [
        `Coverage: ${extendedMetrics.codeCoverage.coverage || 0}%`,
        `Lines to Cover: ${extendedMetrics.codeCoverage.linesToCover ? extendedMetrics.codeCoverage.linesToCover.toLocaleString() : 'N/A'}`,
        `Tests: ${extendedMetrics.codeCoverage.tests || 0}`,
        `Success Rate: ${extendedMetrics.codeCoverage.testSuccessDensity || 100}%`
      ];

      // Display in two columns
      const colWidth = contentWidth / 2;
      coverageData.forEach((item, index) => {
        const x = index % 2 === 0 ? margin + 2 : margin + colWidth + 2;
        const y = yPosition + Math.floor(index / 2) * 6;
        pdf.text(`• ${item}`, x, y);
      });

      yPosition += Math.ceil(coverageData.length / 2) * 6 + 5;

      // Add timestamp if available
      if (extendedMetrics.codeCoverage.lastUpdated) {
        pdf.setFontSize(this.FONTS.caption.size);
        pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
        pdf.text(`Last updated: ${this.formatDateSafely(extendedMetrics.codeCoverage.lastUpdated)}`, margin, yPosition);
        yPosition += 8;
      }

      yPosition += 10;
    }

    // Add Build Stability Details if available
    if (extendedMetrics.buildStability) {
      // Check if we need a new page
      if (yPosition + 40 > pageHeight - 30) {
        pdf.addPage();
        yPosition = 30;
      }

      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      pdf.setFontSize(this.FONTS.body.size);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Build Stability (Looper)', margin, yPosition);
      yPosition += 8;

      // Score badge
      if (extendedMetrics.buildStability.score !== undefined) {
        const scoreColor = extendedMetrics.buildStability.score >= 90 ? this.COLORS.success : 
                          extendedMetrics.buildStability.score >= 75 ? this.COLORS.warning : this.COLORS.danger;
        
        pdf.setFillColor(...this.hexToRgb(scoreColor));
        pdf.roundedRect(margin, yPosition, 35, 6, 2, 2, 'F');
        
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(this.FONTS.small.size);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`Score: ${extendedMetrics.buildStability.score}%`, margin + 2, yPosition + 4);
        yPosition += 12;
      }

      // Build statistics if available
      const stats = extendedMetrics.buildStability.statistics || extendedMetrics.buildStability.processedData?.statistics;
      if (stats) {
        pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
        pdf.setFontSize(this.FONTS.small.size);
        pdf.setFont('helvetica', 'normal');
        
        const buildData = [
          `Total Builds: ${stats.total || 0}`,
          `Success Rate: ${stats.successRate || 0}%`,
          `Successful: ${stats.success || 0}`,
          `Failed: ${stats.failure || 0}`,
          `Average Build Time: ${stats.avgBuildTime ? (stats.avgBuildTime > 60 ? `${Math.round(stats.avgBuildTime / 60)}m` : `${Math.round(stats.avgBuildTime)}s`) : 'N/A'}`,
          `Last Build: ${stats.lastBuildDate ? this.formatDateSafely(stats.lastBuildDate) : 'N/A'}`
        ];

        // Display in two columns
        const colWidth = contentWidth / 2;
        buildData.forEach((item, index) => {
          const x = index % 2 === 0 ? margin + 2 : margin + colWidth + 2;
          const y = yPosition + Math.floor(index / 2) * 6;
          pdf.text(`• ${item}`, x, y);
        });

        yPosition += Math.ceil(buildData.length / 2) * 6 + 5;
      }

      // Add timestamp if available
      if (extendedMetrics.buildStability.lastUpdated) {
        pdf.setFontSize(this.FONTS.caption.size);
        pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
        pdf.text(`Last updated: ${this.formatDateSafely(extendedMetrics.buildStability.lastUpdated)}`, margin, yPosition);
        yPosition += 8;
      }

      yPosition += 10;
    }

    // Add Change Management Summary if available
    if (extendedMetrics.changeRequests || extendedMetrics.changeLog || (extendedMetrics.pcfRequests && extendedMetrics.pcfRequests.length > 0)) {
      yPosition = this.addChangeManagementDetails(pdf, extendedMetrics, yPosition, contentWidth, margin, pageHeight);
    }

    return yPosition;
  }

  // Add change management details
  private static addChangeManagementDetails(
    pdf: jsPDF,
    extendedMetrics: ExtendedMetrics,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 30 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    // Section title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.subheading.size);
    pdf.setFont('helvetica', 'bold');
    
    // Dynamic section title based on available data
    const hasChangeRequests = !!extendedMetrics.changeRequests;
    const hasChangeLog = !!extendedMetrics.changeLog;
    const hasPCFRequests = !!(extendedMetrics.pcfRequests && extendedMetrics.pcfRequests.length > 0);
    
    let sectionTitle = 'Change Management Summary';
    if (hasPCFRequests && !hasChangeRequests && !hasChangeLog) {
      sectionTitle = 'Post Code Freeze Requests';
    } else if ((hasChangeRequests || hasChangeLog) && !hasPCFRequests) {
      sectionTitle = 'Change Management Summary';
    } else {
      sectionTitle = 'Change Management & PCF Requests';
    }
    
    pdf.text(sectionTitle, margin, yPosition);
    yPosition += 15;

    // Add Change Requests Summary (ServiceNow) if available
    if (extendedMetrics.changeRequests) {
      yPosition = this.addChangeRequestsSummary(pdf, extendedMetrics.changeRequests, yPosition, contentWidth, margin, pageHeight);
    }

    // Add Changelog Details (GitHub PRs) if available
    if (extendedMetrics.changeLog) {
      yPosition = this.addChangelogDetails(pdf, extendedMetrics.changeLog, yPosition, contentWidth, margin, pageHeight);
    }

    // Add PCF Requests if available
    if (extendedMetrics.pcfRequests && extendedMetrics.pcfRequests.length > 0) {
      yPosition = this.addPCFRequestsDetails(pdf, extendedMetrics.pcfRequests, yPosition, contentWidth, margin, pageHeight);
    }

    return yPosition;
  }

  // Add change requests summary
  private static addChangeRequestsSummary(
    pdf: jsPDF,
    changeRequests: any,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 40 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Change Requests (ServiceNow)', margin, yPosition);
    yPosition += 8;

    // Score badge
    if (changeRequests.score !== undefined) {
      const scoreColor = changeRequests.score >= 80 ? this.COLORS.success : 
                        changeRequests.score >= 60 ? this.COLORS.warning : this.COLORS.danger;
      
      pdf.setFillColor(...this.hexToRgb(scoreColor));
      pdf.roundedRect(margin, yPosition, 35, 6, 2, 2, 'F');
      
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`Score: ${changeRequests.score}%`, margin + 2, yPosition + 4);
      yPosition += 12;
    }

    // Change requests metrics in compact format
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'normal');
    
    // Use actual data from rawData/items if available
    const changeRequestItems = changeRequests.rawData || changeRequests.items || [];
    const totalRequests = changeRequestItems.length || changeRequests.totalRequests || changeRequests.totalPRs || 0;
    const openRequests = changeRequestItems.filter((cr: any) => 
      ['Open', 'New', 'Assigned', 'Work in Progress', 'Pending'].includes(cr.state || cr.state_dv)
    ).length || changeRequests.openRequests || changeRequests.openPRs || 0;
    const resolvedRequests = changeRequestItems.filter((cr: any) => 
      ['Resolved', 'Closed'].includes(cr.state || cr.state_dv)
    ).length || changeRequests.resolvedRequests || changeRequests.mergedPRs || 0;
    const inProgressRequests = changeRequestItems.filter((cr: any) => 
      ['Work in Progress', 'In Progress'].includes(cr.state || cr.state_dv)
    ).length || changeRequests.inProgressRequests || 0;
    
    const changeRequestData = [
      `Total Requests: ${totalRequests}`,
      `Resolved: ${resolvedRequests}`,
      `Open: ${openRequests}`,
      `In Progress: ${inProgressRequests}`
    ];

    // Display in two columns
    const colWidth = contentWidth / 2;
    changeRequestData.forEach((item, index) => {
      const x = index % 2 === 0 ? margin + 2 : margin + colWidth + 2;
      const y = yPosition + Math.floor(index / 2) * 6;
      pdf.text(`• ${item}`, x, y);
    });

    yPosition += Math.ceil(changeRequestData.length / 2) * 6 + 10;

    // Add detailed change requests table if data is available
    if (Array.isArray(changeRequestItems) && changeRequestItems.length > 0) {
      yPosition = this.addChangeRequestsTable(pdf, changeRequestItems, yPosition, contentWidth, margin, pageHeight);
    }

    // Add timestamp if available
    if (changeRequests.lastUpdated) {
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text(`Last updated: ${this.formatDateSafely(changeRequests.lastUpdated)}`, margin, yPosition);
      yPosition += 8;
    }

    return yPosition + 10;
  }

  // Add change requests table
  private static addChangeRequestsTable(
    pdf: jsPDF,
    changeRequests: any[],
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 60 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    // Table title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Change Request Details:', margin, yPosition);
    yPosition += 8;

    // Sort change requests by date (most recent first) and take the most recent 10-15
    const maxCRsToShow = 12;
    const sortedCRs = changeRequests
      .sort((a, b) => {
        const dateA = new Date(a.openedAt || a.opened_at || 0).getTime();
        const dateB = new Date(b.openedAt || b.opened_at || 0).getTime();
        return dateB - dateA;
      })
      .slice(0, maxCRsToShow);

    // Table configuration
    const tableY = yPosition;
    const rowHeight = 8;
    const colWidths = [20, 55, 20, 25, 20]; // Request#, Description, Priority, State, Date
    const headers = ['Request #', 'Description', 'Priority', 'State', 'Date'];
    
    // Table headers
    pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
    pdf.rect(margin, tableY, contentWidth, rowHeight, 'F');
    
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.caption.size);
    pdf.setFont('helvetica', 'bold');
    
    let xPos = margin + 1;
    headers.forEach((header, index) => {
      pdf.text(header, xPos, tableY + 5);
      xPos += colWidths[index];
    });
    
    let currentY = tableY + rowHeight;
    
    // Table rows
    pdf.setFont('helvetica', 'normal');
    sortedCRs.forEach((cr) => {
      // Check if we need a new page for this row
      if (currentY + rowHeight > pageHeight - 30) {
        pdf.addPage();
        currentY = 30;
        
        // Redraw headers on new page
        pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
        pdf.rect(margin, currentY, contentWidth, rowHeight, 'F');
        
        pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
        pdf.setFontSize(this.FONTS.caption.size);
        pdf.setFont('helvetica', 'bold');
        
        xPos = margin + 1;
        headers.forEach((header, index) => {
          pdf.text(header, xPos, currentY + 5);
          xPos += colWidths[index];
        });
        
        currentY += rowHeight;
        pdf.setFont('helvetica', 'normal');
      }

      // Draw row border
      pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
      pdf.line(margin, currentY, margin + contentWidth, currentY);
      
      xPos = margin + 1;
      
      // Request Number
      const requestNumber = cr.number || 'N/A';
      pdf.text(requestNumber, xPos, currentY + 5);
      xPos += colWidths[0];
      
      // Description (truncate if too long)
      const description = cr.shortDescription || cr.description || 'No description';
      const truncatedDescription = description.length > 25 ? description.substring(0, 22) + '...' : description;
      pdf.text(truncatedDescription, xPos, currentY + 5);
      xPos += colWidths[1];
      
      // Priority with color
      const priority = this.getPriorityLabel(cr.priority);
      let priorityColor = this.COLORS.secondary;
      if (priority === 'Critical' || priority === 'High') priorityColor = this.COLORS.danger;
      else if (priority === 'Medium') priorityColor = this.COLORS.warning;
      else if (priority === 'Low') priorityColor = this.COLORS.success;
      
      pdf.setTextColor(...this.hexToRgb(priorityColor));
      pdf.text(priority, xPos, currentY + 5);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      xPos += colWidths[2];
      
      // State with color
      const state = cr.state || cr.state_dv || 'Unknown';
      let stateColor = this.COLORS.secondary;
      if (state === 'Resolved' || state === 'Closed') stateColor = this.COLORS.success;
      else if (state === 'Open' || state === 'New') stateColor = this.COLORS.warning;
      else if (state.includes('Progress')) stateColor = this.COLORS.accent;
      
      pdf.setTextColor(...this.hexToRgb(stateColor));
      const truncatedState = state.length > 10 ? state.substring(0, 8) + '..' : state;
      pdf.text(truncatedState, xPos, currentY + 5);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      xPos += colWidths[3];
      
      // Date (format as MM/DD)
      let dateText = 'N/A';
      const dateField = cr.openedAt || cr.opened_at;
      if (dateField) {
        try {
          const date = new Date(dateField);
          dateText = date.toLocaleDateString('en-US', { 
            month: '2-digit', 
            day: '2-digit' 
          });
        } catch (error) {
          dateText = 'Invalid';
        }
      }
      pdf.text(dateText, xPos, currentY + 5);
      
      currentY += rowHeight;
    });
    
    // Bottom border
    pdf.line(margin, currentY, margin + contentWidth, currentY);
    
    // Show count if truncated
    if (changeRequests.length > maxCRsToShow) {
      currentY += 5;
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text(`Showing ${maxCRsToShow} of ${changeRequests.length} change requests (most recent)`, margin, currentY);
      currentY += 8;
    }
    
    return currentY + 10;
  }

  // Helper method to get priority label
  private static getPriorityLabel(priority: string | number): string {
    if (typeof priority === 'number') priority = priority.toString();
    
    switch (priority) {
      case '0':
      case '1': return 'Critical';
      case '2': return 'High';
      case '3': return 'Medium';
      case '4':
      case '5': return 'Low';
      default: return priority || 'Unknown';
    }
  }

  // Add detailed changelog with PR information
  private static addChangelogDetails(
    pdf: jsPDF,
    changeLog: any,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 40 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Changelog (GitHub Pull Requests)', margin, yPosition);
    yPosition += 8;

    // Score badge
    if (changeLog.score !== undefined) {
      const scoreColor = changeLog.score >= 80 ? this.COLORS.success : 
                        changeLog.score >= 60 ? this.COLORS.warning : this.COLORS.danger;
      
      pdf.setFillColor(...this.hexToRgb(scoreColor));
      pdf.roundedRect(margin, yPosition, 35, 6, 2, 2, 'F');
      
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`Score: ${changeLog.score}%`, margin + 2, yPosition + 4);
      yPosition += 12;
    }

    // Summary metrics
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'normal');
    
    const summaryData = [
      `Total PRs: ${changeLog.totalPRs || 0}`,
      `Merged: ${changeLog.mergedPRs || 0}`,
      `Open: ${changeLog.openPRs || 0}`,
      `Closed: ${changeLog.closedPRs || 0}`
    ];

    // Display summary in two columns
    const colWidth = contentWidth / 2;
    summaryData.forEach((item, index) => {
      const x = index % 2 === 0 ? margin + 2 : margin + colWidth + 2;
      const y = yPosition + Math.floor(index / 2) * 6;
      pdf.text(`• ${item}`, x, y);
    });

    yPosition += Math.ceil(summaryData.length / 2) * 6 + 10;

    // Add detailed PR table if rawData is available
    const pullRequests = changeLog.rawData || [];
    if (Array.isArray(pullRequests) && pullRequests.length > 0) {
      yPosition = this.addPullRequestsTable(pdf, pullRequests, yPosition, contentWidth, margin, pageHeight);
    }

    // Add branch and date range info
    if (changeLog.branch || changeLog.dateRange) {
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      
      if (changeLog.branch) {
        pdf.text(`Branch: ${changeLog.branch}`, margin, yPosition);
        yPosition += 6;
      }
    }

    // Add timestamp if available
    if (changeLog.lastUpdated) {
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text(`Last updated: ${this.formatDateSafely(changeLog.lastUpdated)}`, margin, yPosition);
      yPosition += 8;
    }

    return yPosition + 10;
  }

  // Add pull requests table
  private static addPullRequestsTable(
    pdf: jsPDF,
    pullRequests: any[],
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 60 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    // Table title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Pull Request Details:', margin, yPosition);
    yPosition += 8;

    // Sort PRs by number (descending) and take the most recent 15-20
    const maxPRsToShow = 15;
    const sortedPRs = pullRequests
      .sort((a, b) => (b.number || 0) - (a.number || 0))
      .slice(0, maxPRsToShow);

    // Table configuration
    const tableY = yPosition;
    const rowHeight = 8;
    const colWidths = [15, 35, 65, 20, 25]; // PR#, Author, Title, Status, Date
    const headers = ['PR#', 'Author', 'Title', 'Status', 'Date'];
    
    // Table headers
    pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
    pdf.rect(margin, tableY, contentWidth, rowHeight, 'F');
    
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.caption.size);
    pdf.setFont('helvetica', 'bold');
    
    let xPos = margin + 1;
    headers.forEach((header, index) => {
      pdf.text(header, xPos, tableY + 5);
      xPos += colWidths[index];
    });
    
    let currentY = tableY + rowHeight;
    
    // Table rows
    pdf.setFont('helvetica', 'normal');
    sortedPRs.forEach((pr) => {
      // Check if we need a new page for this row
      if (currentY + rowHeight > pageHeight - 30) {
        pdf.addPage();
        currentY = 30;
        
        // Redraw headers on new page
        pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
        pdf.rect(margin, currentY, contentWidth, rowHeight, 'F');
        
        pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
        pdf.setFontSize(this.FONTS.caption.size);
        pdf.setFont('helvetica', 'bold');
        
        xPos = margin + 1;
        headers.forEach((header, index) => {
          pdf.text(header, xPos, currentY + 5);
          xPos += colWidths[index];
        });
        
        currentY += rowHeight;
        pdf.setFont('helvetica', 'normal');
      }

      // Draw row border
      pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
      pdf.line(margin, currentY, margin + contentWidth, currentY);
      
      xPos = margin + 1;
      
      // PR Number
      pdf.text(`#${pr.number || 'N/A'}`, xPos, currentY + 5);
      xPos += colWidths[0];
      
      // Author (truncate if too long)
      const author = pr.author || 'Unknown';
      const truncatedAuthor = author.length > 12 ? author.substring(0, 12) + '...' : author;
      pdf.text(truncatedAuthor, xPos, currentY + 5);
      xPos += colWidths[1];
      
      // Title (truncate if too long)
      const title = pr.title || 'No title';
      const truncatedTitle = title.length > 35 ? title.substring(0, 32) + '...' : title;
      pdf.text(truncatedTitle, xPos, currentY + 5);
      xPos += colWidths[2];
      
      // Status with color
      const status = pr.status || 'unknown';
      let statusColor = this.COLORS.secondary;
      if (status === 'merged') statusColor = this.COLORS.success;
      else if (status === 'open') statusColor = this.COLORS.warning;
      else if (status === 'closed') statusColor = this.COLORS.danger;
      
      pdf.setTextColor(...this.hexToRgb(statusColor));
      pdf.text(status.charAt(0).toUpperCase() + status.slice(1), xPos, currentY + 5);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      xPos += colWidths[3];
      
      // Date (format as MM/DD)
      let dateText = 'N/A';
      if (pr.created_at) {
        try {
          const date = new Date(pr.created_at);
          dateText = date.toLocaleDateString('en-US', { 
            month: '2-digit', 
            day: '2-digit' 
          });
        } catch (error) {
          dateText = 'Invalid';
        }
      }
      pdf.text(dateText, xPos, currentY + 5);
      
      currentY += rowHeight;
    });
    
    // Bottom border
    pdf.line(margin, currentY, margin + contentWidth, currentY);
    
    // Show count if truncated
    if (pullRequests.length > maxPRsToShow) {
      currentY += 5;
      pdf.setFontSize(this.FONTS.caption.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text(`Showing ${maxPRsToShow} of ${pullRequests.length} pull requests (most recent)`, margin, currentY);
      currentY += 8;
    }
    
    return currentY + 10;
  }

  // Add PCF requests table
  private static addPCFRequestsTable(
    pdf: jsPDF,
    pcfRequests: any[],
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 60 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    // Table title
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('PCF Request Details:', margin, yPosition);
    yPosition += 8;

    // Sort PCF requests by date (most recent first)
    const sortedPCFs = pcfRequests.sort((a, b) => {
      const dateA = new Date(a.requestedAt || 0).getTime();
      const dateB = new Date(b.requestedAt || 0).getTime();
      return dateB - dateA;
    });

    // Table configuration
    const tableY = yPosition;
    const rowHeight = 8;
    const colWidths = [25, 60, 25, 25, 25]; // Jira, Description, Status, Requester, Date
    const headers = ['Jira Ticket', 'Description', 'Status', 'Requester', 'Date'];
    
    // Table headers
    pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
    pdf.rect(margin, tableY, contentWidth, rowHeight, 'F');
    
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.caption.size);
    pdf.setFont('helvetica', 'bold');
    
    let xPos = margin + 1;
    headers.forEach((header, index) => {
      pdf.text(header, xPos, tableY + 5);
      xPos += colWidths[index];
    });
    
    let currentY = tableY + rowHeight;
    
    // Table rows
    pdf.setFont('helvetica', 'normal');
    sortedPCFs.forEach((pcf) => {
      // Check if we need a new page for this row
      if (currentY + rowHeight > pageHeight - 30) {
        pdf.addPage();
        currentY = 30;
        
        // Redraw headers on new page
        pdf.setFillColor(...this.hexToRgb(this.COLORS.background));
        pdf.rect(margin, currentY, contentWidth, rowHeight, 'F');
        
        pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
        pdf.setFontSize(this.FONTS.caption.size);
        pdf.setFont('helvetica', 'bold');
        
        xPos = margin + 1;
        headers.forEach((header, index) => {
          pdf.text(header, xPos, currentY + 5);
          xPos += colWidths[index];
        });
        
        currentY += rowHeight;
        pdf.setFont('helvetica', 'normal');
      }

      // Draw row border
      pdf.setDrawColor(...this.hexToRgb(this.COLORS.border));
      pdf.line(margin, currentY, margin + contentWidth, currentY);
      
      xPos = margin + 1;
      
      // Jira Ticket
      pdf.text(pcf.jiraTicket || 'N/A', xPos, currentY + 5);
      xPos += colWidths[0];
      
      // Description (truncate if too long)
      const description = pcf.summary || 'No description';
      const truncatedDesc = description.length > 40 ? description.substring(0, 37) + '...' : description;
      pdf.text(truncatedDesc, xPos, currentY + 5);
      xPos += colWidths[1];
      
      // Status with color
      let statusColor = this.COLORS.secondary;
      if (pcf.status === 'approved') statusColor = this.COLORS.success;
      else if (pcf.status === 'rejected') statusColor = this.COLORS.danger;
      else if (pcf.status === 'pending') statusColor = this.COLORS.warning;
      
      pdf.setTextColor(...this.hexToRgb(statusColor));
      pdf.text(pcf.status.charAt(0).toUpperCase() + pcf.status.slice(1), xPos, currentY + 5);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
      xPos += colWidths[2];
      
      // Requester (truncate if too long)
      const requester = pcf.requestedBy || 'Unknown';
      const truncatedReq = requester.length > 15 ? requester.substring(0, 12) + '...' : requester;
      pdf.text(truncatedReq, xPos, currentY + 5);
      xPos += colWidths[3];
      
      // Date (format as MM/DD)
      let dateText = 'N/A';
      if (pcf.requestedAt) {
        try {
          const date = new Date(pcf.requestedAt);
          dateText = date.toLocaleDateString('en-US', { 
            month: '2-digit', 
            day: '2-digit' 
          });
        } catch (error) {
          dateText = 'Invalid';
        }
      }
      pdf.text(dateText, xPos, currentY + 5);
      
      currentY += rowHeight;
    });
    
    // Bottom border
    pdf.line(margin, currentY, margin + contentWidth, currentY);
    
    return currentY + 10;
  }

  // Add PCF requests details
  private static addPCFRequestsDetails(
    pdf: jsPDF,
    pcfRequests: any[],
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // Check if we need a new page
    if (yPosition + 40 > pageHeight - 30) {
      pdf.addPage();
      yPosition = 30;
    }

    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.body.size);
    pdf.setFont('helvetica', 'bold');
    pdf.text('PCF Requests', margin, yPosition);
    yPosition += 8;

    // PCF requests metrics in compact format
    pdf.setTextColor(...this.hexToRgb(this.COLORS.text));
    pdf.setFontSize(this.FONTS.small.size);
    pdf.setFont('helvetica', 'normal');
    
    const pcfRequestData = [
      `Total Requests: ${pcfRequests.length}`,
      `Approved: ${pcfRequests.filter((pr: any) => pr.status === 'approved').length}`,
      `Pending: ${pcfRequests.filter((pr: any) => pr.status === 'pending').length}`,
      `Rejected: ${pcfRequests.filter((pr: any) => pr.status === 'rejected').length}`
    ];

    // Display summary in two columns
    const colWidth = contentWidth / 2;
    pcfRequestData.forEach((item, index) => {
      const x = index % 2 === 0 ? margin + 2 : margin + colWidth + 2;
      const y = yPosition + Math.floor(index / 2) * 6;
      pdf.text(`• ${item}`, x, y);
    });

    yPosition += Math.ceil(pcfRequestData.length / 2) * 6 + 10;

    // Add detailed PCF requests table if data is available
    if (Array.isArray(pcfRequests) && pcfRequests.length > 0) {
      yPosition = this.addPCFRequestsTable(pdf, pcfRequests, yPosition, contentWidth, margin, pageHeight);
    } else {
      // Add "No PCF requests" message
      pdf.setFontSize(this.FONTS.small.size);
      pdf.setTextColor(...this.hexToRgb(this.COLORS.lightText));
      pdf.text('No PCF requests found for this release.', margin + 2, yPosition);
      yPosition += 15;
    }

    return yPosition;
  }
} 