import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ApiResponse } from '../types/api';
import type { Team } from '../types';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
      timeout: 10000,
  withCredentials: true, // Include cookies for session-based auth
});

// Request interceptor to add auth token if available
api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
  (error) => {
    return Promise.reject(error);
  }
    );

    // Response interceptor for error handling
api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
      // Clear token and redirect to login
          localStorage.removeItem('authToken');
      // window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

// Team API methods
export const teamAPI = {
  getTeams: () => api.get<Team[]>('/teams'),
  getTeam: (id: string) => api.get<Team>(`/teams/${id}`),
  createTeam: (data: Omit<Team, 'id'>) => api.post<Team>('/teams', data),
  updateTeam: (id: string, data: Partial<Team>) => api.put<Team>(`/teams/${id}`, data),
  deleteTeam: (id: string) => api.delete(`/teams/${id}`),
};

export interface CosmosTeam {
  id: string;
  teamName: string;
  metadata?: {
    // Platform identification
    platform: string;
    platform_type: string;
    
    // Platform-specific configuration
    github_repo: string;
    repo_default_branch?: string;
    looper_url?: string;
    looper_team_id?: string;
    sonarqube_url: string;
    sonarqube_project_id: string;
    
    // Team communication
    slack_webhook?: string;
    
    // ServiceNow integration
    serviceNowId?: string;
  };
  roles?: {
    functional_qa?: {
      cart: string[];
      checkout: string[];
      plp: string[];
      pdp: string[];
      sng: string[];
    };
    e2e_qa?: string[];
    engineering_leadership?: string[];
  };
  release_types?: any;
}

class ApiService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Teams API (legacy support)
  async getTeams(): Promise<{ success: boolean; data: CosmosTeam[] }> {
    try {
      const response = await this.request<{ data: CosmosTeam[] }>('/api/teams');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching teams:', error);
      return {
        success: false,
        data: []
      };
    }
  }

  async getTeam(id: string): Promise<{ success: boolean; data: CosmosTeam | null }> {
    try {
      const response = await this.request<{ data: CosmosTeam }>(`/api/teams/${id}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching team:', error);
      return {
        success: false,
        data: null
      };
    }
  }

  async createTeam(team: Omit<CosmosTeam, 'id'>): Promise<{ success: boolean; data: CosmosTeam | null }> {
    try {
      const response = await this.request<{ data: CosmosTeam }>('/api/teams', {
        method: 'POST',
        body: JSON.stringify(team),
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error creating team:', error);
      return {
        success: false,
        data: null
      };
    }
  }

  async updateTeam(id: string, updates: Partial<CosmosTeam>): Promise<{ success: boolean; data: CosmosTeam | null }> {
    try {
      const response = await this.request<{ data: CosmosTeam }>(`/api/teams/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating team:', error);
      return {
        success: false,
        data: null
      };
    }
  }

  // Generic HTTP methods for backward compatibility
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await axios.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await axios.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await axios.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response: AxiosResponse<ApiResponse<T>> = await axios.delete(url, config);
    return response.data;
  }
}

export const apiService = new ApiService();

export default api;
