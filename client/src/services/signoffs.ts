const API_BASE_URL = `${import.meta.env.VITE_BACKEND_URL || 'http://localhost:3002'}/api`;

// Types for signoff responses
export interface SignoffStage {
  id: string;
  name: string;
  status: 'pending' | 'in_progress' | 'completed';
  isAvailable: boolean;
  progress: number;
  requiredApprovers: Array<{
    subTeam?: string;
    approvers: string[];
    email?: string;
  }>;
  currentApprovals: Approval[];
  minRequiredApprovals: number;
  lastUpdate?: string;
  issues?: string;
}

export interface Approval {
  id: string;
  releaseId: string;
  teamId: string;
  stage: string;
  approverEmail: string;
  status: 'pending' | 'approved' | 'rejected';
  sha?: string;
  buildNumber?: string;
  subTeam?: string;
  approvedAt?: string;
  notes?: string;
}

export interface SignoffStatus {
  releaseId: string;
  releaseType: string;
  teamId: string;
  overallProgress: number;
  stages: SignoffStage[];
  totalStages: number;
  completedStages: number;
}

export interface ApprovalRequest {
  approverEmail: string;
  sha?: string;
  buildNumber?: string;
  notes?: string;
  subTeam?: string;
}

export interface ApprovalResponse {
  approval: Approval;
  newProgress: number;
  stageCompleted: boolean;
  releaseCompleted: boolean;
}

export const signoffsService = {
  /**
   * Get signoff status for a release
   */
  async getSignoffStatus(releaseId: string): Promise<SignoffStatus> {
    const response = await fetch(`${API_BASE_URL}/signoffs/${releaseId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get signoff status');
    }

    return response.json();
  },

  /**
   * Submit an approval for a specific stage
   */
  async submitApproval(
    releaseId: string, 
    stage: string, 
    approvalData: ApprovalRequest
  ): Promise<ApprovalResponse> {
    const response = await fetch(`${API_BASE_URL}/signoffs/${releaseId}/approve/${stage}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(approvalData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to submit approval');
    }

    return response.json();
  },

  /**
   * Get available approvers for a stage
   */
  async getApprovers(releaseId: string, stage: string): Promise<{
    stage: string;
    approvers: Array<{
      subTeam?: string;
      approvers: string[];
      email?: string;
    }>;
  }> {
    const response = await fetch(`${API_BASE_URL}/signoffs/${releaseId}/approvers/${stage}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get approvers');
    }

    return response.json();
  },
}; 