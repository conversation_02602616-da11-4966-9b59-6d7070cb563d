version: '3.8'

services:
  backend:
    build: .
    ports:
      - "3002:3002"
    environment:
      # Authentication Configuration
      - AUTH_PROVIDER=github
      - VITE_BACKEND_URL=http://localhost:3002
      - BACKEND_URL=http://localhost:3002
      - FRONTEND_URL=http://localhost:5174

      # GitHub OAuth Configuration
      - GITHUB_CLIENT_ID=********************
      - GITHUB_CLIENT_SECRET=cf3ad0e9734a6f622646333a8b145e43ae5ad85c
      - GITHUB_CALLBACK_URL=http://localhost:3002/api/auth/github/callback
      - GITHUB_AUTH_ENABLED=true

      # Azure AD OAuth Configuration
      - AZURE_CALLBACK_URL=http://localhost:3002/api/auth/azure/callback

      # Cosmos DB Configuration
      - COSMOSDB_ENDPOINT=https://signoff-release.documents.azure.com:443/
      - COSMOSDB_KEY=****************************************************************************************
      - COSMOSDB_DATABASE=release-signoff
      - COSMOSDB_CONTAINER_TEAMS=teams
      - COSMOSDB_CONTAINER_RELEASES=releases

      # Azure Key Vault Configuration
      - AZURE_TENANT_ID=9d2d24c7-9410-42ea-bb6e-4cc60f50cd39
      - AZURE_CLIENT_ID=d2658184-9724-4703-bbfd-edfca8ff8841
      - AZURE_CLIENT_SECRET=****************************************
      - KEY_VAULT_NAME=release-portal-vault
      - KEY_VAULT_URL=https://release-portal-vault.vault.azure.net/
      - AZURE_ADDITIONALLY_ALLOWED_TENANTS=*
      - AZURE_AUTHORITY_HOST=https://login.microsoftonline.com

      # Server Configuration
      - PORT=3002
      - NODE_ENV=development

      # Rate Limiting
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100

  frontend:
    image: node:20-alpine
    ports:
      - "5174:5174"
    working_dir: /app
    volumes:
      - ./client:/app
    environment:
      - NODE_ENV=development
      - VITE_BACKEND_URL=http://localhost:3002
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0 --port 5174" 