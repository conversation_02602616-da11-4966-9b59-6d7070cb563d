owner:
  group: Samsclub-DevOps-Azure-NP-O

notify:
  slack:
    channelName: release-portal

managedNamespace:
  apmId: "SOF0002701"

profiles:
  - node

build:
  artifact: "engineering-excellence"
  docker:
    app:
      dockerFile: Dockerfile
      buildArgs:
        VITE_BACKEND_URL: "https://signal.walmartlabs.com"
  looper:
    enableDockerNoCache: false

deploy:
  namespace: engineering-excellence
  releaseType:
    strategy: normal
    rollbackOnError: false
    waitForReady: true
    deployTimeout: 1800
  gslb:
    clusterProfiles:
      dev:
        cname: signal.walmartlabs.com
  helm:
    values:
      livenessProbe:
        enabled: "true"
        path: "/api/health"
        port: 3001
        initialDelaySeconds: 60
      readinessProbe:
        enabled: "true"
        path: "/api/health"
        port: 3001
        initialDelaySeconds: 60
      min:
        cpu: 25m
        memory: 40Mi
      max:
        cpu: 50m
        memory: 80Mi
      networking:
        httpsEnabled: true
        pathPrefix: /
        externalPort: "3001"
        internalPort: "3001"
        ports:
          api:
            internalPort: 3001
            externalPort: 3001
            protocol: TCP
  stages:
    - name: dev
      refEventFilters:
        - refs: ["main"]
          events: [onPush]
      target:
        cluster_id: [useast-dev-az-002]
