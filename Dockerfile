FROM node:20-alpine as base
RUN addgroup -S -g 10000 app \
    && adduser -S -D -u 10000 -s /sbin/nologin -h /opt/app/ -G app app
USER 10000

FROM base as build-server
USER root
WORKDIR /opt/app
RUN chown -R app:app /opt/app
USER 10000
COPY --chown=app:app server/*.json ./
RUN npm ci

COPY --chown=app:app server/ ./

RUN npm run build
RUN ls -la dist/  # Added debug logging to verify build output

FROM base as build-client
WORKDIR /opt/app/client
COPY client/package.json ./
COPY client/package-lock.json ./
USER root
RUN chown -R app:app /opt/app/client
USER 10000
RUN npm ci
COPY client/ ./
ARG VITE_BACKEND_URL
ENV VITE_BACKEND_URL=$VITE_BACKEND_URL
RUN npm run build

FROM base
ENV PORT 3000
ENV NODE_ENV production
EXPOSE 3000
USER 10000
WORKDIR /opt/app

# Copy root package.json for workspace setup
COPY --chown=app:app ./*.json ./

# Install root dependencies (concurrently, etc.)
RUN npm ci

# Copy pre-built client files (if they exist)
COPY --from=build-client /opt/app/client/dist /opt/app/client/dist

# Copy built server
COPY --from=build-server /opt/app/dist /opt/app/server/dist

# Copy certificate bundle for SSL verification
COPY --chown=app:app server/cert /opt/app/server/cert

# Copy server package.json for production dependencies
WORKDIR /opt/app/server
COPY --chown=app:app server/*.json ./

# Create node_modules directory with correct permissions
USER root
RUN mkdir -p node_modules && chown -R app:app node_modules && chown -R app:app /opt/app/server
USER 10000

RUN npm ci --only=production

# Go back to root directory for start command
WORKDIR /opt/app

# Start the application
CMD ["npm", "start"]
