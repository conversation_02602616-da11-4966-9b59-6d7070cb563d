{"name": "engineering-excellence", "version": "1.0.0", "description": "Highly modular production-grade full stack application", "private": true, "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "cd server && npm start", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm test", "test:server": "cd server && npm test", "lint": "npm run lint:client && npm run lint:server", "lint:client": "cd client && npm run lint", "lint:server": "cd server && npm run lint", "install:all": "cd client && npm install && cd ../server && npm install", "load-yaml": "cd server && npx ts-node src/scripts/load-yaml-to-db.ts", "cleanup-duplicates": "cd server && npx ts-node src/scripts/cleanup-duplicate-teams.ts"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}